"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2647],{80063:function(t,e,n){n.d(e,{L:function(){return o}});class o{constructor(t){this.isMounted=!1,this.node=t}update(){}}},37997:function(t,e,n){n.d(e,{s:function(){return l}});var o=n(20569),r=n(86627),i=n(80063);class a extends i.L{constructor(t){super(t),t.animationState||(t.animationState=(0,r.M)(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),(0,o.H)(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let u=0;class s extends i.L{constructor(){super(...arguments),this.id=u++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:n}=this.node.presenceContext,{isPresent:o}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===o)return;let r=this.node.animationState.setActive("exit",!t,{custom:null!=n?n:this.node.getProps().custom});e&&!t&&r.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let l={animation:{Feature:a},exit:{Feature:s}}},35938:function(t,e,n){n.d(e,{A:function(){return r}});let o={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},r={};for(let t in o)r[t]={isEnabled:e=>o[t].some(t=>!!e[t])}},22279:function(t,e,n){n.d(e,{o:function(){return u}});var o=n(81318),r=n(51886),i=n(58920),a=n(64611);let u={pan:{Feature:r.q},drag:{Feature:o.h,ProjectionNode:a.u,MeasureLayout:i.q}}},47021:function(t,e,n){n.d(e,{E:function(){return f}});var o=n(83022),r=n(67369),i=n(66429),a=n(80063);let u=new WeakMap,s=new WeakMap,l=t=>{let e=u.get(t.target);e&&e(t)},c=t=>{t.forEach(l)},d={some:0,all:1};class m extends a.L{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:o="some",once:r}=t,i={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof o?o:d[o]};return function(t,e,n){let o=function({root:t,...e}){let n=t||document;s.has(n)||s.set(n,{});let o=s.get(n),r=JSON.stringify(e);return o[r]||(o[r]=new IntersectionObserver(c,{root:t,...e})),o[r]}(e);return u.set(t,n),o.observe(t),()=>{u.delete(t),o.unobserve(t)}}(this.node.current,i,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:n,onViewportLeave:o}=this.node.getProps(),i=e?n:o;i&&i(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}let f={inView:{Feature:m},tap:{Feature:i.Q},focus:{Feature:r.f},hover:{Feature:o.a}}},82760:function(t,e,n){n.d(e,{b:function(){return i}});var o=n(64611),r=n(58920);let i={layout:{ProjectionNode:o.u,MeasureLayout:r.q}}},58920:function(t,e,n){n.d(e,{q:function(){return x}});var o=n(2265),r=n(49637),i=n(58881),a=n(29913),u=n(50215),s=n(27492);function l(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let c={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!s.px.test(t))return t;t=parseFloat(t)}let n=l(t,e.target.x),o=l(t,e.target.y);return`${n}% ${o}%`}};var d=n(38090),m=n(15636),f=n(98639),p=n(58345);class h extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:o}=this.props,{projection:r}=t;(0,f.B)(y),r&&(e.group&&e.group.add(r),n&&n.register&&o&&n.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),u.V.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:n,drag:o,isPresent:r}=this.props,i=n.projection;return i&&(i.isPresent=r,o||t.layoutDependency!==e||void 0===e?i.willUpdate():this.safeToRemove(),t.isPresent===r||(r?i.promote():i.relegate()||p.Wi.postRender(()=>{let t=i.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(o),n&&n.deregister&&n.deregister(o))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function x(t){let[e,n]=(0,r.oO)(),u=(0,o.useContext)(i.p);return o.createElement(h,{...t,layoutGroup:u,switchLayoutGroup:(0,o.useContext)(a.g),isPresent:e,safeToRemove:n})}let y={borderRadius:{...c,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:c,borderTopRightRadius:c,borderBottomLeftRadius:c,borderBottomRightRadius:c,boxShadow:{correct:(t,{treeScale:e,projectionDelta:n})=>{let o=m.P.parse(t);if(o.length>5)return t;let r=m.P.createTransformer(t),i="number"!=typeof o[0]?1:0,a=n.x.scale*e.x,u=n.y.scale*e.y;o[0+i]/=a,o[1+i]/=u;let s=(0,d.C)(a,u,.5);return"number"==typeof o[2+i]&&(o[2+i]/=s),"number"==typeof o[3+i]&&(o[3+i]/=s),r(o)}}}},51765:function(t,e,n){n.d(e,{F:function(){return y}});var o=n(2265),r=n(45750),i=n(56961),a=n(64252),u=n(11534),s=n(47337),l=n(61750),c=n(34006),d=n(82349),m=n(35938),f=n(44563),p=n(58881),h=n(29913);let x=Symbol.for("motionComponentSymbol");function y({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:y,Component:v}){t&&function(t){for(let e in t)m.A[e]={...m.A[e],...t[e]}}(t);let g=(0,o.forwardRef)(function(m,x){var g;let b;let C={...(0,o.useContext)(r._),...m,layoutId:function({layoutId:t}){let e=(0,o.useContext)(p.p).id;return e&&void 0!==t?e+"-"+t:t}(m)},{isStatic:w}=C,E=(0,d.H)(m),S=y(m,w);if(!w&&f.j){E.visualElement=function(t,e,n,c){let{visualElement:d}=(0,o.useContext)(i.v),m=(0,o.useContext)(s.u),f=(0,o.useContext)(a.O),p=(0,o.useContext)(r._).reducedMotion,h=(0,o.useRef)();c=c||m.renderer,!h.current&&c&&(h.current=c(t,{visualState:e,parent:d,props:n,presenceContext:f,blockInitialAnimation:!!f&&!1===f.initial,reducedMotionConfig:p}));let x=h.current;(0,o.useInsertionEffect)(()=>{x&&x.update(n,f)});let y=(0,o.useRef)(!!(n[l.M]&&!window.HandoffComplete));return(0,u.L)(()=>{x&&(x.render(),y.current&&x.animationState&&x.animationState.animateChanges())}),(0,o.useEffect)(()=>{x&&(x.updateFeatures(),!y.current&&x.animationState&&x.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),x}(v,S,C,e);let n=(0,o.useContext)(h.g),c=(0,o.useContext)(s.u).strict;E.visualElement&&(b=E.visualElement.loadFeatures(C,c,t,n))}return o.createElement(i.v.Provider,{value:E},b&&E.visualElement?o.createElement(b,{visualElement:E.visualElement,...C}):null,n(v,m,(g=E.visualElement,(0,o.useCallback)(t=>{t&&S.mount&&S.mount(t),g&&(t?g.mount(t):g.unmount()),x&&("function"==typeof x?x(t):(0,c.I)(x)&&(x.current=t))},[g])),S,w,E.visualElement))});return g[x]=v,g}},77556:function(t,e,n){n.d(e,{j:function(){return i}});var o=n(98639),r=n(8834);function i(t,{layout:e,layoutId:n}){return r.G.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!o.P[t]||"opacity"===t)}},74935:function(t,e,n){n.d(e,{t:function(){return d}});var o=n(2265),r=n(20569),i=n(64252),a=n(31297),u=n(53576),s=n(84364),l=n(56961),c=n(17743);let d=t=>(e,n)=>{let d=(0,o.useContext)(l.v),m=(0,o.useContext)(i.O),f=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},o,i,u){let l={latestValues:function(t,e,n,o){let i={},u=o(t,{});for(let t in u)i[t]=(0,s.b)(u[t]);let{initial:l,animate:d}=t,m=(0,c.G)(t),f=(0,c.M)(t);e&&f&&!m&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===d&&(d=e.animate));let p=!!n&&!1===n.initial,h=(p=p||!1===l)?d:l;return h&&"boolean"!=typeof h&&!(0,r.H)(h)&&(Array.isArray(h)?h:[h]).forEach(e=>{let n=(0,a.o)(t,e);if(!n)return;let{transitionEnd:o,transition:r,...u}=n;for(let t in u){let e=u[t];if(Array.isArray(e)){let t=p?e.length-1:0;e=e[t]}null!==e&&(i[t]=e)}for(let t in o)i[t]=o[t]}),i}(o,i,u,t),renderState:e()};return n&&(l.mount=t=>n(o,t,l)),l})(t,e,d,m);return n?f():(0,u.h)(f)}},35998:function(t,e,n){n.d(e,{Z:function(){return r}});let o=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||o.has(t)}},19510:function(t,e,n){n.d(e,{V:function(){return m}});var o=n(26378),r=n(76376),i=n(38090),a=n(44439),u=n(27492);let s=["TopLeft","TopRight","BottomLeft","BottomRight"],l=s.length,c=t=>"string"==typeof t?parseFloat(t):t,d=t=>"number"==typeof t||u.px.test(t);function m(t,e,n,o,r,a){r?(t.opacity=(0,i.C)(0,void 0!==n.opacity?n.opacity:1,p(o)),t.opacityExit=(0,i.C)(void 0!==e.opacity?e.opacity:1,0,h(o))):a&&(t.opacity=(0,i.C)(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,o));for(let r=0;r<l;r++){let a=`border${s[r]}Radius`,l=f(e,a),m=f(n,a);(void 0!==l||void 0!==m)&&(l||(l=0),m||(m=0),0===l||0===m||d(l)===d(m)?(t[a]=Math.max((0,i.C)(c(l),c(m),o),0),(u.aQ.test(m)||u.aQ.test(l))&&(t[a]+="%")):t[a]=m)}(e.rotate||n.rotate)&&(t.rotate=(0,i.C)(e.rotate||0,n.rotate||0,o))}function f(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let p=x(0,.5,o.Bn),h=x(.5,.95,a.Z);function x(t,e,n){return o=>o<t?0:o>e?1:n((0,r.Y)(t,e,o))}},82063:function(t,e,n){function o({top:t,left:e,right:n,bottom:o}){return{x:{min:e,max:n},y:{min:t,max:o}}}function r({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function i(t,e){if(!e)return t;let n=e({x:t.left,y:t.top}),o=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:o.y,right:o.x}}n.d(e,{d7:function(){return i},i8:function(){return o},z2:function(){return r}})},72644:function(t,e,n){function o(t,e){t.min=e.min,t.max=e.max}function r(t,e){o(t.x,e.x),o(t.y,e.y)}n.d(e,{j:function(){return r}})},47227:function(t,e,n){n.d(e,{D2:function(){return h},YY:function(){return l},am:function(){return d},o2:function(){return s},q2:function(){return i}});var o=n(38090),r=n(12787);function i(t,e,n){return n+e*(t-n)}function a(t,e,n,o,r){return void 0!==r&&(t=o+r*(t-o)),o+n*(t-o)+e}function u(t,e=0,n=1,o,r){t.min=a(t.min,e,n,o,r),t.max=a(t.max,e,n,o,r)}function s(t,{x:e,y:n}){u(t.x,e.translate,e.scale,e.originPoint),u(t.y,n.translate,n.scale,n.originPoint)}function l(t,e,n,o=!1){let i,a;let u=n.length;if(u){e.x=e.y=1;for(let l=0;l<u;l++){a=(i=n[l]).projectionDelta;let u=i.instance;(!u||!u.style||"contents"!==u.style.display)&&(o&&i.options.layoutScroll&&i.scroll&&i!==i.root&&h(t,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(e.x*=a.x.scale,e.y*=a.y.scale,s(t,a)),o&&(0,r.ud)(i.latestValues)&&h(t,i.latestValues))}e.x=c(e.x),e.y=c(e.y)}}function c(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function d(t,e){t.min=t.min+e,t.max=t.max+e}function m(t,e,[n,r,i]){let a=void 0!==e[i]?e[i]:.5,s=(0,o.C)(t.min,t.max,a);u(t,e[n],e[r],s,e.scale)}let f=["x","scaleX","originX"],p=["y","scaleY","originY"];function h(t,e){m(t.x,e,f),m(t.y,e,p)}},25549:function(t,e,n){n.d(e,{JO:function(){return r},b3:function(){return d},tf:function(){return l},wS:function(){return i},y$:function(){return u}});var o=n(38090);function r(t){return t.max-t.min}function i(t,e=0,n=.01){return Math.abs(t-e)<=n}function a(t,e,n,a=.5){t.origin=a,t.originPoint=(0,o.C)(e.min,e.max,t.origin),t.scale=r(n)/r(e),(i(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=(0,o.C)(n.min,n.max,t.origin)-t.originPoint,(i(t.translate)||isNaN(t.translate))&&(t.translate=0)}function u(t,e,n,o){a(t.x,e.x,n.x,o?o.originX:void 0),a(t.y,e.y,n.y,o?o.originY:void 0)}function s(t,e,n){t.min=n.min+e.min,t.max=t.min+r(e)}function l(t,e,n){s(t.x,e.x,n.x),s(t.y,e.y,n.y)}function c(t,e,n){t.min=e.min-n.min,t.max=t.min+r(e)}function d(t,e,n){c(t.x,e.x,n.x),c(t.y,e.y,n.y)}},21478:function(t,e,n){n.d(e,{mg:function(){return c}});var o=n(38090),r=n(27492),i=n(47227);function a(t,e,n,o,r){return t-=e,t=(0,i.q2)(t,1/n,o),void 0!==r&&(t=(0,i.q2)(t,1/r,o)),t}function u(t,e,[n,i,u],s,l){!function(t,e=0,n=1,i=.5,u,s=t,l=t){if(r.aQ.test(e)&&(e=parseFloat(e),e=(0,o.C)(l.min,l.max,e/100)-l.min),"number"!=typeof e)return;let c=(0,o.C)(s.min,s.max,i);t===s&&(c-=e),t.min=a(t.min,e,n,c,u),t.max=a(t.max,e,n,c,u)}(t,e[n],e[i],e[u],e.scale,s,l)}let s=["x","scaleX","originX"],l=["y","scaleY","originY"];function c(t,e,n,o){u(t.x,e,s,n?n.x:void 0,o?o.x:void 0),u(t.y,e,l,n?n.y:void 0,o?o.y:void 0)}},96674:function(t,e,n){n.d(e,{dO:function(){return a},wc:function(){return r}});let o=()=>({translate:0,scale:1,origin:0,originPoint:0}),r=()=>({x:o(),y:o()}),i=()=>({min:0,max:0}),a=()=>({x:i(),y:i()})},72728:function(t,e,n){n.d(e,{BS:function(){return i},Dm:function(){return s},RD:function(){return a},Rm:function(){return u}});var o=n(25549);function r(t){return 0===t.translate&&1===t.scale}function i(t){return r(t.x)&&r(t.y)}function a(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function u(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function s(t){return(0,o.JO)(t.x)/(0,o.JO)(t.y)}},64611:function(t,e,n){n.d(e,{u:function(){return u}});var o=n(82458),r=n(34969);let i=(0,o.yV)({attachResizeListener:(t,e)=>(0,r.E)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),a={current:void 0},u=(0,o.yV)({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!a.current){let t=new i({});t.mount(window),t.setOptions({layoutScroll:!0}),a.current=t}return a.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position})},50215:function(t,e,n){n.d(e,{V:function(){return o}});let o={hasAnimatedSinceResize:!0,hasEverUpdated:!1}},29818:function(t,e,n){n.d(e,{t:function(){return r}});var o=n(69013);class r{constructor(){this.members=[]}add(t){(0,o.y4)(this.members,t),t.scheduleRender()}remove(t){if((0,o.cl)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let n=this.members.findIndex(e=>t===e);if(0===n)return!1;for(let t=n;t>=0;t--){let n=this.members[t];if(!1!==n.isPresent){e=n;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:o}=t.options;!1===o&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}},98639:function(t,e,n){n.d(e,{B:function(){return r},P:function(){return o}});let o={};function r(t){Object.assign(o,t)}},62734:function(t,e,n){n.d(e,{d:function(){return o}});function o(t,e,n){let o="",r=t.x.translate/e.x,i=t.y.translate/e.y;if((r||i)&&(o=`translate3d(${r}px, ${i}px, 0) `),(1!==e.x||1!==e.y)&&(o+=`scale(${1/e.x}, ${1/e.y}) `),n){let{rotate:t,rotateX:e,rotateY:r}=n;t&&(o+=`rotate(${t}deg) `),e&&(o+=`rotateX(${e}deg) `),r&&(o+=`rotateY(${r}deg) `)}let a=t.x.scale*e.x,u=t.y.scale*e.y;return(1!==a||1!==u)&&(o+=`scale(${a}, ${u})`),o||"none"}},65255:function(t,e,n){n.d(e,{U:function(){return o}});function o(t){return[t("x"),t("y")]}},12787:function(t,e,n){function o(t){return void 0===t||1===t}function r({scale:t,scaleX:e,scaleY:n}){return!o(t)||!o(e)||!o(n)}function i(t){return r(t)||a(t)||t.z||t.rotate||t.rotateX||t.rotateY}function a(t){var e,n;return(e=t.x)&&"0%"!==e||(n=t.y)&&"0%"!==n}n.d(e,{D_:function(){return a},Lj:function(){return r},ud:function(){return i}})},50813:function(t,e,n){n.d(e,{J:function(){return i},z:function(){return a}});var o=n(82063),r=n(47227);function i(t,e){return(0,o.i8)((0,o.d7)(t.getBoundingClientRect(),e))}function a(t,e,n){let o=i(t,n),{scroll:a}=e;return a&&((0,r.am)(o.x,a.offset.x),(0,r.am)(o.y,a.offset.y)),o}}}]);