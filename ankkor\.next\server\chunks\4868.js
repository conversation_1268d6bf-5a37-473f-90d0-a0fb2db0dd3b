exports.id=4868,exports.ids=[4868],exports.modules={80956:function(e,s,t){var n;n=function(e){return e.enc.Hex},e.exports=n(t(99878))},94868:(e,s,t)=>{"use strict";t.d(s,{s:()=>tT});var n=t(80956),i=t(14766),r=Object.defineProperty;((e,s)=>{for(var t in s)r(e,t,{get:s[t],enumerable:!0})})({},{UpstashError:()=>c,UrlError:()=>o});var c=class extends Error{constructor(e){super(e),this.name="UpstashError"}},o=class extends Error{constructor(e){super(`Upstash Redis client was passed an invalid URL. You should pass a URL starting with https. Received: "${e}". `),this.name="UrlError"}};function h(e){try{return function e(s){let t=Array.isArray(s)?s.map(s=>{try{return e(s)}catch{return s}}):JSON.parse(s);return"number"==typeof t&&t.toString()!==s?s:t}(e)}catch{return e}}function a(e){return[e[0],...h(e.slice(1))]}var p=class{baseUrl;headers;options;readYourWrites;upstashSyncToken="";hasCredentials;retry;constructor(e){if(this.options={backend:e.options?.backend,agent:e.agent,responseEncoding:e.responseEncoding??"base64",cache:e.cache,signal:e.signal,keepAlive:e.keepAlive??!0},this.upstashSyncToken="",this.readYourWrites=e.readYourWrites??!0,this.baseUrl=(e.baseUrl||"").replace(/\/$/,""),this.baseUrl&&!/^https?:\/\/[^\s#$./?].\S*$/.test(this.baseUrl))throw new o(this.baseUrl);this.headers={"Content-Type":"application/json",...e.headers},this.hasCredentials=!!(this.baseUrl&&this.headers.authorization.split(" ")[1]),"base64"===this.options.responseEncoding&&(this.headers["Upstash-Encoding"]="base64"),this.retry="boolean"!=typeof e.retry||e.retry?{attempts:e.retry?.retries??5,backoff:e.retry?.backoff??(e=>50*Math.exp(e))}:{attempts:1,backoff:()=>0}}mergeTelemetry(e){this.headers=m(this.headers,"Upstash-Telemetry-Runtime",e.runtime),this.headers=m(this.headers,"Upstash-Telemetry-Platform",e.platform),this.headers=m(this.headers,"Upstash-Telemetry-Sdk",e.sdk)}async request(e){let s=function(...e){let s={};for(let t of e)if(t)for(let[e,n]of Object.entries(t))null!=n&&(s[e]=n);return s}(this.headers,e.headers??{}),t=[this.baseUrl,...e.path??[]].join("/"),n="text/event-stream"===s.Accept,i={cache:this.options.cache,method:"POST",headers:s,body:JSON.stringify(e.body),keepalive:this.options.keepAlive,agent:this.options.agent,signal:e.signal??this.options.signal,backend:this.options.backend};if(this.hasCredentials||console.warn("[Upstash Redis] Redis client was initialized without url or token. Failed to execute command."),this.readYourWrites){let e=this.upstashSyncToken;this.headers["upstash-sync-token"]=e}let r=null,o=null;for(let e=0;e<=this.retry.attempts;e++)try{r=await fetch(t,i);break}catch(s){if(this.options.signal?.aborted){r=new Response(new Blob([JSON.stringify({result:this.options.signal.reason??"Aborted"})]),{status:200,statusText:this.options.signal.reason??"Aborted"});break}o=s,e<this.retry.attempts&&await new Promise(s=>setTimeout(s,this.retry.backoff(e)))}if(!r)throw o??Error("Exhausted all retries");if(!r.ok){let s=await r.json();throw new c(`${s.error}, command was: ${JSON.stringify(e.body)}`)}if(this.readYourWrites){let e=r.headers;this.upstashSyncToken=e.get("upstash-sync-token")??""}if(n&&e&&e.onMessage&&r.body){let s=r.body.getReader(),t=new TextDecoder;return(async()=>{try{for(;;){let{value:n,done:i}=await s.read();if(i)break;for(let s of t.decode(n).split("\n"))if(s.startsWith("data: ")){let t=s.slice(6);e.onMessage?.(t)}}}catch(e){e instanceof Error&&"AbortError"===e.name||console.error("Stream reading error:",e)}finally{try{await s.cancel()}catch{}}})(),{result:1}}let h=await r.json();if(this.readYourWrites){let e=r.headers;this.upstashSyncToken=e.get("upstash-sync-token")??""}return"base64"===this.options.responseEncoding?Array.isArray(h)?h.map(({result:e,error:s})=>({result:u(e),error:s})):{result:u(h.result),error:h.error}:h}};function l(e){let s="";try{let t=atob(e),n=t.length,i=new Uint8Array(n);for(let e=0;e<n;e++)i[e]=t.charCodeAt(e);s=new TextDecoder().decode(i)}catch{s=e}return s}function u(e){let s;switch(typeof e){case"undefined":return e;case"number":s=e;break;case"object":s=Array.isArray(e)?e.map(e=>"string"==typeof e?l(e):Array.isArray(e)?e.map(e=>u(e)):e):null;break;case"string":s="OK"===e?"OK":l(e)}return s}function m(e,s,t){return t&&(e[s]=e[s]?[e[s],t].join(","):t),e}var d=e=>{switch(typeof e){case"string":case"number":case"boolean":return e;default:return JSON.stringify(e)}},x=class{command;serialize;deserialize;headers;path;onMessage;isStreaming;signal;constructor(e,s){if(this.serialize=d,this.deserialize=s?.automaticDeserialization===void 0||s.automaticDeserialization?s?.deserialize??h:e=>e,this.command=e.map(e=>this.serialize(e)),this.headers=s?.headers,this.path=s?.path,this.onMessage=s?.streamOptions?.onMessage,this.isStreaming=s?.streamOptions?.isStreaming??!1,this.signal=s?.streamOptions?.signal,s?.latencyLogging){let e=this.exec.bind(this);this.exec=async s=>{let t=performance.now(),n=await e(s),i=(performance.now()-t).toFixed(2);return console.log(`Latency for \x1b[38;2;19;185;39m${this.command[0].toString().toUpperCase()}\x1b[0m: \x1b[38;2;0;255;255m${i} ms\x1b[0m`),n}}}async exec(e){let{result:s,error:t}=await e.request({body:this.command,path:this.path,upstashSyncToken:e.upstashSyncToken,headers:this.headers,onMessage:this.onMessage,isStreaming:this.isStreaming,signal:this.signal});if(t)throw new c(t);if(void 0===s)throw TypeError("Request did not return a result");return this.deserialize(s)}},w=class extends x{constructor(e,s){let t=["hrandfield",e[0]];"number"==typeof e[1]&&t.push(e[1]),e[2]&&t.push("WITHVALUES"),super(t,{deserialize:e[2]?e=>(function(e){if(0===e.length)return null;let s={};for(let t=0;t<e.length;t+=2){let n=e[t],i=e[t+1];try{s[n]=JSON.parse(i)}catch{s[n]=i}}return s})(e):s?.deserialize,...s})}},g=class extends x{constructor(e,s){super(["append",...e],s)}},y=class extends x{constructor([e,s,t],n){let i=["bitcount",e];"number"==typeof s&&i.push(s),"number"==typeof t&&i.push(t),super(i,n)}},O=class{constructor(e,s,t,n=e=>e.exec(this.client)){this.client=s,this.opts=t,this.execOperation=n,this.command=["bitfield",...e]}command;chain(...e){return this.command.push(...e),this}get(...e){return this.chain("get",...e)}set(...e){return this.chain("set",...e)}incrby(...e){return this.chain("incrby",...e)}overflow(e){return this.chain("overflow",e)}exec(){let e=new x(this.command,this.opts);return this.execOperation(e)}},b=class extends x{constructor(e,s){super(["bitop",...e],s)}},f=class extends x{constructor(e,s){super(["bitpos",...e],s)}},E=class extends x{constructor([e,s,t],n){super(["COPY",e,s,...t?.replace?["REPLACE"]:[]],{...n,deserialize:e=>e>0?"COPIED":"NOT_COPIED"})}},S=class extends x{constructor(e){super(["dbsize"],e)}},A=class extends x{constructor(e,s){super(["decr",...e],s)}},T=class extends x{constructor(e,s){super(["decrby",...e],s)}},R=class extends x{constructor(e,s){super(["del",...e],s)}},v=class extends x{constructor(e,s){super(["echo",...e],s)}},N=class extends x{constructor([e,s,t],n){super(["eval_ro",e,s.length,...s,...t??[]],n)}},k=class extends x{constructor([e,s,t],n){super(["eval",e,s.length,...s,...t??[]],n)}},z=class extends x{constructor([e,s,t],n){super(["evalsha_ro",e,s.length,...s,...t??[]],n)}},U=class extends x{constructor([e,s,t],n){super(["evalsha",e,s.length,...s,...t??[]],n)}},C=class extends x{constructor(e,s){super(e.map(e=>"string"==typeof e?e:String(e)),s)}},P=class extends x{constructor(e,s){super(["exists",...e],s)}},I=class extends x{constructor(e,s){super(["expire",...e.filter(Boolean)],s)}},L=class extends x{constructor(e,s){super(["expireat",...e],s)}},M=class extends x{constructor(e,s){let t=["flushall"];e&&e.length>0&&e[0].async&&t.push("async"),super(t,s)}},D=class extends x{constructor([e],s){let t=["flushdb"];e?.async&&t.push("async"),super(t,s)}},J=class extends x{constructor([e,s,...t],n){let i=["geoadd",e];"nx"in s&&s.nx?i.push("nx"):"xx"in s&&s.xx&&i.push("xx"),"ch"in s&&s.ch&&i.push("ch"),"latitude"in s&&s.latitude&&i.push(s.longitude,s.latitude,s.member),i.push(...t.flatMap(({latitude:e,longitude:s,member:t})=>[s,e,t])),super(i,n)}},F=class extends x{constructor([e,s,t,n="M"],i){super(["GEODIST",e,s,t,n],i)}},Y=class extends x{constructor(e,s){let[t]=e;super(["GEOHASH",t,...Array.isArray(e[1])?e[1]:e.slice(1)],s)}},j=class extends x{constructor(e,s){let[t]=e;super(["GEOPOS",t,...Array.isArray(e[1])?e[1]:e.slice(1)],{deserialize:e=>(function(e){let s=[];for(let t of e)t?.[0]&&t?.[1]&&s.push({lng:Number.parseFloat(t[0]),lat:Number.parseFloat(t[1])});return s})(e),...s})}},$=class extends x{constructor([e,s,t,n,i],r){let c=["GEOSEARCH",e];("FROMMEMBER"===s.type||"frommember"===s.type)&&c.push(s.type,s.member),("FROMLONLAT"===s.type||"fromlonlat"===s.type)&&c.push(s.type,s.coordinate.lon,s.coordinate.lat),("BYRADIUS"===t.type||"byradius"===t.type)&&c.push(t.type,t.radius,t.radiusType),("BYBOX"===t.type||"bybox"===t.type)&&c.push(t.type,t.rect.width,t.rect.height,t.rectType),c.push(n),i?.count&&c.push("COUNT",i.count.limit,...i.count.any?["ANY"]:[]),super([...c,...i?.withCoord?["WITHCOORD"]:[],...i?.withDist?["WITHDIST"]:[],...i?.withHash?["WITHHASH"]:[]],{deserialize:e=>i?.withCoord||i?.withDist||i?.withHash?e.map(e=>{let s=1,t={};try{t.member=JSON.parse(e[0])}catch{t.member=e[0]}return i.withDist&&(t.dist=Number.parseFloat(e[s++])),i.withHash&&(t.hash=e[s++].toString()),i.withCoord&&(t.coord={long:Number.parseFloat(e[s][0]),lat:Number.parseFloat(e[s][1])}),t}):e.map(e=>{try{return{member:JSON.parse(e)}}catch{return{member:e}}}),...r})}},W=class extends x{constructor([e,s,t,n,i,r],c){let o=["GEOSEARCHSTORE",e,s];("FROMMEMBER"===t.type||"frommember"===t.type)&&o.push(t.type,t.member),("FROMLONLAT"===t.type||"fromlonlat"===t.type)&&o.push(t.type,t.coordinate.lon,t.coordinate.lat),("BYRADIUS"===n.type||"byradius"===n.type)&&o.push(n.type,n.radius,n.radiusType),("BYBOX"===n.type||"bybox"===n.type)&&o.push(n.type,n.rect.width,n.rect.height,n.rectType),o.push(i),r?.count&&o.push("COUNT",r.count.limit,...r.count.any?["ANY"]:[]),super([...o,...r?.storeDist?["STOREDIST"]:[]],c)}},X=class extends x{constructor(e,s){super(["get",...e],s)}},_=class extends x{constructor(e,s){super(["getbit",...e],s)}},G=class extends x{constructor(e,s){super(["getdel",...e],s)}},B=class extends x{constructor([e,s],t){let n=["getex",e];s&&("ex"in s&&"number"==typeof s.ex?n.push("ex",s.ex):"px"in s&&"number"==typeof s.px?n.push("px",s.px):"exat"in s&&"number"==typeof s.exat?n.push("exat",s.exat):"pxat"in s&&"number"==typeof s.pxat?n.push("pxat",s.pxat):"persist"in s&&s.persist&&n.push("persist")),super(n,t)}},H=class extends x{constructor(e,s){super(["getrange",...e],s)}},K=class extends x{constructor(e,s){super(["getset",...e],s)}},q=class extends x{constructor(e,s){super(["hdel",...e],s)}},V=class extends x{constructor(e,s){super(["hexists",...e],s)}},Q=class extends x{constructor(e,s){let[t,n,i,r]=e,c=Array.isArray(n)?n:[n];super(["hexpire",t,i,...r?[r]:[],"FIELDS",c.length,...c],s)}},Z=class extends x{constructor(e,s){let[t,n,i,r]=e,c=Array.isArray(n)?n:[n];super(["hexpireat",t,i,...r?[r]:[],"FIELDS",c.length,...c],s)}},ee=class extends x{constructor(e,s){let[t,n]=e,i=Array.isArray(n)?n:[n];super(["hexpiretime",t,"FIELDS",i.length,...i],s)}},es=class extends x{constructor(e,s){let[t,n]=e,i=Array.isArray(n)?n:[n];super(["hpersist",t,"FIELDS",i.length,...i],s)}},et=class extends x{constructor(e,s){let[t,n,i,r]=e,c=Array.isArray(n)?n:[n];super(["hpexpire",t,i,...r?[r]:[],"FIELDS",c.length,...c],s)}},en=class extends x{constructor(e,s){let[t,n,i,r]=e,c=Array.isArray(n)?n:[n];super(["hpexpireat",t,i,...r?[r]:[],"FIELDS",c.length,...c],s)}},ei=class extends x{constructor(e,s){let[t,n]=e,i=Array.isArray(n)?n:[n];super(["hpexpiretime",t,"FIELDS",i.length,...i],s)}},er=class extends x{constructor(e,s){let[t,n]=e,i=Array.isArray(n)?n:[n];super(["hpttl",t,"FIELDS",i.length,...i],s)}},ec=class extends x{constructor(e,s){super(["hget",...e],s)}},eo=class extends x{constructor(e,s){super(["hgetall",...e],{deserialize:e=>(function(e){if(0===e.length)return null;let s={};for(let t=0;t<e.length;t+=2){let n=e[t],i=e[t+1];try{let e=!Number.isNaN(Number(i))&&!Number.isSafeInteger(Number(i));s[n]=e?i:JSON.parse(i)}catch{s[n]=i}}return s})(e),...s})}},eh=class extends x{constructor(e,s){super(["hincrby",...e],s)}},ea=class extends x{constructor(e,s){super(["hincrbyfloat",...e],s)}},ep=class extends x{constructor([e],s){super(["hkeys",e],s)}},el=class extends x{constructor(e,s){super(["hlen",...e],s)}},eu=class extends x{constructor([e,...s],t){super(["hmget",e,...s],{deserialize:e=>(function(e,s){if(s.every(e=>null===e))return null;let t={};for(let[n,i]of e.entries())try{t[i]=JSON.parse(s[n])}catch{t[i]=s[n]}return t})(s,e),...t})}},em=class extends x{constructor([e,s],t){super(["hmset",e,...Object.entries(s).flatMap(([e,s])=>[e,s])],t)}},ed=class extends x{constructor([e,s,t],n){let i=["hscan",e,s];t?.match&&i.push("match",t.match),"number"==typeof t?.count&&i.push("count",t.count),super(i,{deserialize:a,...n})}},ex=class extends x{constructor([e,s],t){super(["hset",e,...Object.entries(s).flatMap(([e,s])=>[e,s])],t)}},ew=class extends x{constructor(e,s){super(["hsetnx",...e],s)}},eg=class extends x{constructor(e,s){super(["hstrlen",...e],s)}},ey=class extends x{constructor(e,s){let[t,n]=e,i=Array.isArray(n)?n:[n];super(["httl",t,"FIELDS",i.length,...i],s)}},eO=class extends x{constructor(e,s){super(["hvals",...e],s)}},eb=class extends x{constructor(e,s){super(["incr",...e],s)}},ef=class extends x{constructor(e,s){super(["incrby",...e],s)}},eE=class extends x{constructor(e,s){super(["incrbyfloat",...e],s)}},eS=class extends x{constructor(e,s){super(["JSON.ARRAPPEND",...e],s)}},eA=class extends x{constructor(e,s){super(["JSON.ARRINDEX",...e],s)}},eT=class extends x{constructor(e,s){super(["JSON.ARRINSERT",...e],s)}},eR=class extends x{constructor(e,s){super(["JSON.ARRLEN",e[0],e[1]??"$"],s)}},ev=class extends x{constructor(e,s){super(["JSON.ARRPOP",...e],s)}},eN=class extends x{constructor(e,s){super(["JSON.ARRTRIM",e[0],e[1]??"$",e[2]??0,e[3]??0],s)}},ek=class extends x{constructor(e,s){super(["JSON.CLEAR",...e],s)}},ez=class extends x{constructor(e,s){super(["JSON.DEL",...e],s)}},eU=class extends x{constructor(e,s){super(["JSON.FORGET",...e],s)}},eC=class extends x{constructor(e,s){let t=["JSON.GET"];"string"==typeof e[1]?t.push(...e):(t.push(e[0]),e[1]&&(e[1].indent&&t.push("INDENT",e[1].indent),e[1].newline&&t.push("NEWLINE",e[1].newline),e[1].space&&t.push("SPACE",e[1].space)),t.push(...e.slice(2))),super(t,s)}},eP=class extends x{constructor(e,s){super(["JSON.MERGE",...e],s)}},eI=class extends x{constructor(e,s){super(["JSON.MGET",...e[0],e[1]],s)}},eL=class extends x{constructor(e,s){let t=["JSON.MSET"];for(let s of e)t.push(s.key,s.path,s.value);super(t,s)}},eM=class extends x{constructor(e,s){super(["JSON.NUMINCRBY",...e],s)}},eD=class extends x{constructor(e,s){super(["JSON.NUMMULTBY",...e],s)}},eJ=class extends x{constructor(e,s){super(["JSON.OBJKEYS",...e],s)}},eF=class extends x{constructor(e,s){super(["JSON.OBJLEN",...e],s)}},eY=class extends x{constructor(e,s){super(["JSON.RESP",...e],s)}},ej=class extends x{constructor(e,s){let t=["JSON.SET",e[0],e[1],e[2]];e[3]&&(e[3].nx?t.push("NX"):e[3].xx&&t.push("XX")),super(t,s)}},e$=class extends x{constructor(e,s){super(["JSON.STRAPPEND",...e],s)}},eW=class extends x{constructor(e,s){super(["JSON.STRLEN",...e],s)}},eX=class extends x{constructor(e,s){super(["JSON.TOGGLE",...e],s)}},e_=class extends x{constructor(e,s){super(["JSON.TYPE",...e],s)}},eG=class extends x{constructor(e,s){super(["keys",...e],s)}},eB=class extends x{constructor(e,s){super(["lindex",...e],s)}},eH=class extends x{constructor(e,s){super(["linsert",...e],s)}},eK=class extends x{constructor(e,s){super(["llen",...e],s)}},eq=class extends x{constructor(e,s){super(["lmove",...e],s)}},eV=class extends x{constructor(e,s){let[t,n,i,r]=e;super(["LMPOP",t,...n,i,...r?["COUNT",r]:[]],s)}},eQ=class extends x{constructor(e,s){super(["lpop",...e],s)}},eZ=class extends x{constructor(e,s){let t=["lpos",e[0],e[1]];"number"==typeof e[2]?.rank&&t.push("rank",e[2].rank),"number"==typeof e[2]?.count&&t.push("count",e[2].count),"number"==typeof e[2]?.maxLen&&t.push("maxLen",e[2].maxLen),super(t,s)}},e1=class extends x{constructor(e,s){super(["lpush",...e],s)}},e0=class extends x{constructor(e,s){super(["lpushx",...e],s)}},e2=class extends x{constructor(e,s){super(["lrange",...e],s)}},e8=class extends x{constructor(e,s){super(["lrem",...e],s)}},e5=class extends x{constructor(e,s){super(["lset",...e],s)}},e6=class extends x{constructor(e,s){super(["ltrim",...e],s)}},e3=class extends x{constructor(e,s){super(["mget",...Array.isArray(e[0])?e[0]:e],s)}},e4=class extends x{constructor([e],s){super(["mset",...Object.entries(e).flatMap(([e,s])=>[e,s])],s)}},e9=class extends x{constructor([e],s){super(["msetnx",...Object.entries(e).flat()],s)}},e7=class extends x{constructor(e,s){super(["persist",...e],s)}},se=class extends x{constructor(e,s){super(["pexpire",...e],s)}},ss=class extends x{constructor(e,s){super(["pexpireat",...e],s)}},st=class extends x{constructor(e,s){super(["pfadd",...e],s)}},sn=class extends x{constructor(e,s){super(["pfcount",...e],s)}},si=class extends x{constructor(e,s){super(["pfmerge",...e],s)}},sr=class extends x{constructor(e,s){let t=["ping"];e?.[0]!==void 0&&t.push(e[0]),super(t,s)}},sc=class extends x{constructor(e,s){super(["psetex",...e],s)}},so=class extends x{constructor(e,s){super(["pttl",...e],s)}},sh=class extends x{constructor(e,s){super(["publish",...e],s)}},sa=class extends x{constructor(e){super(["randomkey"],e)}},sp=class extends x{constructor(e,s){super(["rename",...e],s)}},sl=class extends x{constructor(e,s){super(["renamenx",...e],s)}},su=class extends x{constructor(e,s){super(["rpop",...e],s)}},sm=class extends x{constructor(e,s){super(["rpush",...e],s)}},sd=class extends x{constructor(e,s){super(["rpushx",...e],s)}},sx=class extends x{constructor(e,s){super(["sadd",...e],s)}},sw=class extends x{constructor([e,s],t){let n=["scan",e];s?.match&&n.push("match",s.match),"number"==typeof s?.count&&n.push("count",s.count),s?.type&&s.type.length>0&&n.push("type",s.type),super(n,{deserialize:a,...t})}},sg=class extends x{constructor(e,s){super(["scard",...e],s)}},sy=class extends x{constructor(e,s){super(["script","exists",...e],{deserialize:e=>e,...s})}},sO=class extends x{constructor([e],s){let t=["script","flush"];e?.sync?t.push("sync"):e?.async&&t.push("async"),super(t,s)}},sb=class extends x{constructor(e,s){super(["script","load",...e],s)}},sf=class extends x{constructor(e,s){super(["sdiff",...e],s)}},sE=class extends x{constructor(e,s){super(["sdiffstore",...e],s)}},sS=class extends x{constructor([e,s,t],n){let i=["set",e,s];t&&("nx"in t&&t.nx?i.push("nx"):"xx"in t&&t.xx&&i.push("xx"),"get"in t&&t.get&&i.push("get"),"ex"in t&&"number"==typeof t.ex?i.push("ex",t.ex):"px"in t&&"number"==typeof t.px?i.push("px",t.px):"exat"in t&&"number"==typeof t.exat?i.push("exat",t.exat):"pxat"in t&&"number"==typeof t.pxat?i.push("pxat",t.pxat):"keepTtl"in t&&t.keepTtl&&i.push("keepTtl")),super(i,n)}},sA=class extends x{constructor(e,s){super(["setbit",...e],s)}},sT=class extends x{constructor(e,s){super(["setex",...e],s)}},sR=class extends x{constructor(e,s){super(["setnx",...e],s)}},sv=class extends x{constructor(e,s){super(["setrange",...e],s)}},sN=class extends x{constructor(e,s){super(["sinter",...e],s)}},sk=class extends x{constructor(e,s){super(["sinterstore",...e],s)}},sz=class extends x{constructor(e,s){super(["sismember",...e],s)}},sU=class extends x{constructor(e,s){super(["smembers",...e],s)}},sC=class extends x{constructor(e,s){super(["smismember",e[0],...e[1]],s)}},sP=class extends x{constructor(e,s){super(["smove",...e],s)}},sI=class extends x{constructor([e,s],t){let n=["spop",e];"number"==typeof s&&n.push(s),super(n,t)}},sL=class extends x{constructor([e,s],t){let n=["srandmember",e];"number"==typeof s&&n.push(s),super(n,t)}},sM=class extends x{constructor(e,s){super(["srem",...e],s)}},sD=class extends x{constructor([e,s,t],n){let i=["sscan",e,s];t?.match&&i.push("match",t.match),"number"==typeof t?.count&&i.push("count",t.count),super(i,{deserialize:a,...n})}},sJ=class extends x{constructor(e,s){super(["strlen",...e],s)}},sF=class extends x{constructor(e,s){super(["sunion",...e],s)}},sY=class extends x{constructor(e,s){super(["sunionstore",...e],s)}},sj=class extends x{constructor(e){super(["time"],e)}},s$=class extends x{constructor(e,s){super(["touch",...e],s)}},sW=class extends x{constructor(e,s){super(["ttl",...e],s)}},sX=class extends x{constructor(e,s){super(["type",...e],s)}},s_=class extends x{constructor(e,s){super(["unlink",...e],s)}},sG=class extends x{constructor([e,s,t],n){super(["XACK",e,s,...Array.isArray(t)?[...t]:[t]],n)}},sB=class extends x{constructor([e,s,t,n],i){let r=["XADD",e];for(let[e,i]of(n&&(n.nomkStream&&r.push("NOMKSTREAM"),n.trim&&(r.push(n.trim.type,n.trim.comparison,n.trim.threshold),void 0!==n.trim.limit&&r.push("LIMIT",n.trim.limit))),r.push(s),Object.entries(t)))r.push(e,i);super(r,i)}},sH=class extends x{constructor([e,s,t,n,i,r],c){let o=[];r?.count&&o.push("COUNT",r.count),r?.justId&&o.push("JUSTID"),super(["XAUTOCLAIM",e,s,t,n,i,...o],c)}},sK=class extends x{constructor([e,s,t,n,i,r],c){let o=Array.isArray(i)?[...i]:[i],h=[];r?.idleMS&&h.push("IDLE",r.idleMS),r?.idleMS&&h.push("TIME",r.timeMS),r?.retryCount&&h.push("RETRYCOUNT",r.retryCount),r?.force&&h.push("FORCE"),r?.justId&&h.push("JUSTID"),r?.lastId&&h.push("LASTID",r.lastId),super(["XCLAIM",e,s,t,n,...o,...h],c)}},sq=class extends x{constructor([e,s],t){super(["XDEL",e,...Array.isArray(s)?[...s]:[s]],t)}},sV=class extends x{constructor([e,s],t){let n=["XGROUP"];switch(s.type){case"CREATE":n.push("CREATE",e,s.group,s.id),s.options&&(s.options.MKSTREAM&&n.push("MKSTREAM"),void 0!==s.options.ENTRIESREAD&&n.push("ENTRIESREAD",s.options.ENTRIESREAD.toString()));break;case"CREATECONSUMER":n.push("CREATECONSUMER",e,s.group,s.consumer);break;case"DELCONSUMER":n.push("DELCONSUMER",e,s.group,s.consumer);break;case"DESTROY":n.push("DESTROY",e,s.group);break;case"SETID":n.push("SETID",e,s.group,s.id),s.options?.ENTRIESREAD!==void 0&&n.push("ENTRIESREAD",s.options.ENTRIESREAD.toString());break;default:throw Error("Invalid XGROUP")}super(n,t)}},sQ=class extends x{constructor([e,s],t){let n=[];"CONSUMERS"===s.type?n.push("CONSUMERS",e,s.group):n.push("GROUPS",e),super(["XINFO",...n],t)}},sZ=class extends x{constructor(e,s){super(["XLEN",...e],s)}},s1=class extends x{constructor([e,s,t,n,i,r],c){super(["XPENDING",e,s,...r?.idleTime?["IDLE",r.idleTime]:[],t,n,i,...r?.consumer===void 0?[]:Array.isArray(r.consumer)?[...r.consumer]:[r.consumer]],c)}},s0=class extends x{constructor([e,s,t,n],i){let r=["XRANGE",e,s,t];"number"==typeof n&&r.push("COUNT",n),super(r,{deserialize:e=>(function(e){let s={};for(let t of e)for(let e=0;e<t.length;e+=2){let n=t[e],i=t[e+1];n in s||(s[n]={});for(let e=0;e<i.length;e+=2){let t=i[e],r=i[e+1];try{s[n][t]=JSON.parse(r)}catch{s[n][t]=r}}}return s})(e),...i})}},s2=class extends x{constructor([e,s,t],n){if(Array.isArray(e)&&Array.isArray(s)&&e.length!==s.length)throw Error("ERR Unbalanced XREAD list of streams: for each stream key an ID or '$' must be specified");let i=[];"number"==typeof t?.count&&i.push("COUNT",t.count),"number"==typeof t?.blockMS&&i.push("BLOCK",t.blockMS),i.push("STREAMS",...Array.isArray(e)?[...e]:[e],...Array.isArray(s)?[...s]:[s]),super(["XREAD",...i],n)}},s8=class extends x{constructor([e,s,t,n,i],r){if(Array.isArray(t)&&Array.isArray(n)&&t.length!==n.length)throw Error("ERR Unbalanced XREADGROUP list of streams: for each stream key an ID or '$' must be specified");let c=[];"number"==typeof i?.count&&c.push("COUNT",i.count),"number"==typeof i?.blockMS&&c.push("BLOCK",i.blockMS),"boolean"==typeof i?.NOACK&&i.NOACK&&c.push("NOACK"),c.push("STREAMS",...Array.isArray(t)?[...t]:[t],...Array.isArray(n)?[...n]:[n]),super(["XREADGROUP","GROUP",e,s,...c],r)}},s5=class extends x{constructor([e,s,t,n],i){let r=["XREVRANGE",e,s,t];"number"==typeof n&&r.push("COUNT",n),super(r,{deserialize:e=>(function(e){let s={};for(let t of e)for(let e=0;e<t.length;e+=2){let n=t[e],i=t[e+1];n in s||(s[n]={});for(let e=0;e<i.length;e+=2){let t=i[e],r=i[e+1];try{s[n][t]=JSON.parse(r)}catch{s[n][t]=r}}}return s})(e),...i})}},s6=class extends x{constructor([e,s],t){let{limit:n,strategy:i,threshold:r,exactness:c="~"}=s;super(["XTRIM",e,i,c,r,...n?["LIMIT",n]:[]],t)}},s3=class extends x{constructor([e,s,...t],n){let i=["zadd",e];"nx"in s&&s.nx?i.push("nx"):"xx"in s&&s.xx&&i.push("xx"),"ch"in s&&s.ch&&i.push("ch"),"incr"in s&&s.incr&&i.push("incr"),"lt"in s&&s.lt?i.push("lt"):"gt"in s&&s.gt&&i.push("gt"),"score"in s&&"member"in s&&i.push(s.score,s.member),i.push(...t.flatMap(({score:e,member:s})=>[e,s])),super(i,n)}},s4=class extends x{constructor(e,s){super(["zcard",...e],s)}},s9=class extends x{constructor(e,s){super(["zcount",...e],s)}},s7=class extends x{constructor(e,s){super(["zincrby",...e],s)}},te=class extends x{constructor([e,s,t,n],i){let r=["zinterstore",e,s];Array.isArray(t)?r.push(...t):r.push(t),n&&("weights"in n&&n.weights?r.push("weights",...n.weights):"weight"in n&&"number"==typeof n.weight&&r.push("weights",n.weight),"aggregate"in n&&r.push("aggregate",n.aggregate)),super(r,i)}},ts=class extends x{constructor(e,s){super(["zlexcount",...e],s)}},tt=class extends x{constructor([e,s],t){let n=["zpopmax",e];"number"==typeof s&&n.push(s),super(n,t)}},tn=class extends x{constructor([e,s],t){let n=["zpopmin",e];"number"==typeof s&&n.push(s),super(n,t)}},ti=class extends x{constructor([e,s,t,n],i){let r=["zrange",e,s,t];n?.byScore&&r.push("byscore"),n?.byLex&&r.push("bylex"),n?.rev&&r.push("rev"),n?.count!==void 0&&void 0!==n.offset&&r.push("limit",n.offset,n.count),n?.withScores&&r.push("withscores"),super(r,i)}},tr=class extends x{constructor(e,s){super(["zrank",...e],s)}},tc=class extends x{constructor(e,s){super(["zrem",...e],s)}},to=class extends x{constructor(e,s){super(["zremrangebylex",...e],s)}},th=class extends x{constructor(e,s){super(["zremrangebyrank",...e],s)}},ta=class extends x{constructor(e,s){super(["zremrangebyscore",...e],s)}},tp=class extends x{constructor(e,s){super(["zrevrank",...e],s)}},tl=class extends x{constructor([e,s,t],n){let i=["zscan",e,s];t?.match&&i.push("match",t.match),"number"==typeof t?.count&&i.push("count",t.count),super(i,{deserialize:a,...n})}},tu=class extends x{constructor(e,s){super(["zscore",...e],s)}},tm=class extends x{constructor([e,s,t],n){let i=["zunion",e];Array.isArray(s)?i.push(...s):i.push(s),t&&("weights"in t&&t.weights?i.push("weights",...t.weights):"weight"in t&&"number"==typeof t.weight&&i.push("weights",t.weight),"aggregate"in t&&i.push("aggregate",t.aggregate),t.withScores&&i.push("withscores")),super(i,n)}},td=class extends x{constructor([e,s,t,n],i){let r=["zunionstore",e,s];Array.isArray(t)?r.push(...t):r.push(t),n&&("weights"in n&&n.weights?r.push("weights",...n.weights):"weight"in n&&"number"==typeof n.weight&&r.push("weights",n.weight),"aggregate"in n&&r.push("aggregate",n.aggregate)),super(r,i)}},tx=class extends x{constructor(e,s){super(["zdiffstore",...e],s)}},tw=class extends x{constructor(e,s){let[t,n]=e;super(["zmscore",t,...n],s)}},tg=class{client;commands;commandOptions;multiExec;constructor(e){if(this.client=e.client,this.commands=[],this.commandOptions=e.commandOptions,this.multiExec=e.multiExec??!1,this.commandOptions?.latencyLogging){let e=this.exec.bind(this);this.exec=async s=>{let t=performance.now(),n=await (s?e(s):e()),i=(performance.now()-t).toFixed(2);return console.log(`Latency for \x1b[38;2;19;185;39m${this.multiExec?["MULTI-EXEC"]:["PIPELINE"].toString().toUpperCase()}\x1b[0m: \x1b[38;2;0;255;255m${i} ms\x1b[0m`),n}}}exec=async e=>{if(0===this.commands.length)throw Error("Pipeline is empty");let s=this.multiExec?["multi-exec"]:["pipeline"],t=await this.client.request({path:s,body:Object.values(this.commands).map(e=>e.command)});return e?.keepErrors?t.map(({error:e,result:s},t)=>({error:e,result:this.commands[t].deserialize(s)})):t.map(({error:e,result:s},t)=>{if(e)throw new c(`Command ${t+1} [ ${this.commands[t].command[0]} ] failed: ${e}`);return this.commands[t].deserialize(s)})};length(){return this.commands.length}chain(e){return this.commands.push(e),this}append=(...e)=>this.chain(new g(e,this.commandOptions));bitcount=(...e)=>this.chain(new y(e,this.commandOptions));bitfield=(...e)=>new O(e,this.client,this.commandOptions,this.chain.bind(this));bitop=(e,s,t,...n)=>this.chain(new b([e,s,t,...n],this.commandOptions));bitpos=(...e)=>this.chain(new f(e,this.commandOptions));copy=(...e)=>this.chain(new E(e,this.commandOptions));zdiffstore=(...e)=>this.chain(new tx(e,this.commandOptions));dbsize=()=>this.chain(new S(this.commandOptions));decr=(...e)=>this.chain(new A(e,this.commandOptions));decrby=(...e)=>this.chain(new T(e,this.commandOptions));del=(...e)=>this.chain(new R(e,this.commandOptions));echo=(...e)=>this.chain(new v(e,this.commandOptions));evalRo=(...e)=>this.chain(new N(e,this.commandOptions));eval=(...e)=>this.chain(new k(e,this.commandOptions));evalshaRo=(...e)=>this.chain(new z(e,this.commandOptions));evalsha=(...e)=>this.chain(new U(e,this.commandOptions));exists=(...e)=>this.chain(new P(e,this.commandOptions));expire=(...e)=>this.chain(new I(e,this.commandOptions));expireat=(...e)=>this.chain(new L(e,this.commandOptions));flushall=e=>this.chain(new M(e,this.commandOptions));flushdb=(...e)=>this.chain(new D(e,this.commandOptions));geoadd=(...e)=>this.chain(new J(e,this.commandOptions));geodist=(...e)=>this.chain(new F(e,this.commandOptions));geopos=(...e)=>this.chain(new j(e,this.commandOptions));geohash=(...e)=>this.chain(new Y(e,this.commandOptions));geosearch=(...e)=>this.chain(new $(e,this.commandOptions));geosearchstore=(...e)=>this.chain(new W(e,this.commandOptions));get=(...e)=>this.chain(new X(e,this.commandOptions));getbit=(...e)=>this.chain(new _(e,this.commandOptions));getdel=(...e)=>this.chain(new G(e,this.commandOptions));getex=(...e)=>this.chain(new B(e,this.commandOptions));getrange=(...e)=>this.chain(new H(e,this.commandOptions));getset=(e,s)=>this.chain(new K([e,s],this.commandOptions));hdel=(...e)=>this.chain(new q(e,this.commandOptions));hexists=(...e)=>this.chain(new V(e,this.commandOptions));hexpire=(...e)=>this.chain(new Q(e,this.commandOptions));hexpireat=(...e)=>this.chain(new Z(e,this.commandOptions));hexpiretime=(...e)=>this.chain(new ee(e,this.commandOptions));httl=(...e)=>this.chain(new ey(e,this.commandOptions));hpexpire=(...e)=>this.chain(new et(e,this.commandOptions));hpexpireat=(...e)=>this.chain(new en(e,this.commandOptions));hpexpiretime=(...e)=>this.chain(new ei(e,this.commandOptions));hpttl=(...e)=>this.chain(new er(e,this.commandOptions));hpersist=(...e)=>this.chain(new es(e,this.commandOptions));hget=(...e)=>this.chain(new ec(e,this.commandOptions));hgetall=(...e)=>this.chain(new eo(e,this.commandOptions));hincrby=(...e)=>this.chain(new eh(e,this.commandOptions));hincrbyfloat=(...e)=>this.chain(new ea(e,this.commandOptions));hkeys=(...e)=>this.chain(new ep(e,this.commandOptions));hlen=(...e)=>this.chain(new el(e,this.commandOptions));hmget=(...e)=>this.chain(new eu(e,this.commandOptions));hmset=(e,s)=>this.chain(new em([e,s],this.commandOptions));hrandfield=(e,s,t)=>this.chain(new w([e,s,t],this.commandOptions));hscan=(...e)=>this.chain(new ed(e,this.commandOptions));hset=(e,s)=>this.chain(new ex([e,s],this.commandOptions));hsetnx=(e,s,t)=>this.chain(new ew([e,s,t],this.commandOptions));hstrlen=(...e)=>this.chain(new eg(e,this.commandOptions));hvals=(...e)=>this.chain(new eO(e,this.commandOptions));incr=(...e)=>this.chain(new eb(e,this.commandOptions));incrby=(...e)=>this.chain(new ef(e,this.commandOptions));incrbyfloat=(...e)=>this.chain(new eE(e,this.commandOptions));keys=(...e)=>this.chain(new eG(e,this.commandOptions));lindex=(...e)=>this.chain(new eB(e,this.commandOptions));linsert=(e,s,t,n)=>this.chain(new eH([e,s,t,n],this.commandOptions));llen=(...e)=>this.chain(new eK(e,this.commandOptions));lmove=(...e)=>this.chain(new eq(e,this.commandOptions));lpop=(...e)=>this.chain(new eQ(e,this.commandOptions));lmpop=(...e)=>this.chain(new eV(e,this.commandOptions));lpos=(...e)=>this.chain(new eZ(e,this.commandOptions));lpush=(e,...s)=>this.chain(new e1([e,...s],this.commandOptions));lpushx=(e,...s)=>this.chain(new e0([e,...s],this.commandOptions));lrange=(...e)=>this.chain(new e2(e,this.commandOptions));lrem=(e,s,t)=>this.chain(new e8([e,s,t],this.commandOptions));lset=(e,s,t)=>this.chain(new e5([e,s,t],this.commandOptions));ltrim=(...e)=>this.chain(new e6(e,this.commandOptions));mget=(...e)=>this.chain(new e3(e,this.commandOptions));mset=e=>this.chain(new e4([e],this.commandOptions));msetnx=e=>this.chain(new e9([e],this.commandOptions));persist=(...e)=>this.chain(new e7(e,this.commandOptions));pexpire=(...e)=>this.chain(new se(e,this.commandOptions));pexpireat=(...e)=>this.chain(new ss(e,this.commandOptions));pfadd=(...e)=>this.chain(new st(e,this.commandOptions));pfcount=(...e)=>this.chain(new sn(e,this.commandOptions));pfmerge=(...e)=>this.chain(new si(e,this.commandOptions));ping=e=>this.chain(new sr(e,this.commandOptions));psetex=(e,s,t)=>this.chain(new sc([e,s,t],this.commandOptions));pttl=(...e)=>this.chain(new so(e,this.commandOptions));publish=(...e)=>this.chain(new sh(e,this.commandOptions));randomkey=()=>this.chain(new sa(this.commandOptions));rename=(...e)=>this.chain(new sp(e,this.commandOptions));renamenx=(...e)=>this.chain(new sl(e,this.commandOptions));rpop=(...e)=>this.chain(new su(e,this.commandOptions));rpush=(e,...s)=>this.chain(new sm([e,...s],this.commandOptions));rpushx=(e,...s)=>this.chain(new sd([e,...s],this.commandOptions));sadd=(e,s,...t)=>this.chain(new sx([e,s,...t],this.commandOptions));scan=(...e)=>this.chain(new sw(e,this.commandOptions));scard=(...e)=>this.chain(new sg(e,this.commandOptions));scriptExists=(...e)=>this.chain(new sy(e,this.commandOptions));scriptFlush=(...e)=>this.chain(new sO(e,this.commandOptions));scriptLoad=(...e)=>this.chain(new sb(e,this.commandOptions));sdiff=(...e)=>this.chain(new sf(e,this.commandOptions));sdiffstore=(...e)=>this.chain(new sE(e,this.commandOptions));set=(e,s,t)=>this.chain(new sS([e,s,t],this.commandOptions));setbit=(...e)=>this.chain(new sA(e,this.commandOptions));setex=(e,s,t)=>this.chain(new sT([e,s,t],this.commandOptions));setnx=(e,s)=>this.chain(new sR([e,s],this.commandOptions));setrange=(...e)=>this.chain(new sv(e,this.commandOptions));sinter=(...e)=>this.chain(new sN(e,this.commandOptions));sinterstore=(...e)=>this.chain(new sk(e,this.commandOptions));sismember=(e,s)=>this.chain(new sz([e,s],this.commandOptions));smembers=(...e)=>this.chain(new sU(e,this.commandOptions));smismember=(e,s)=>this.chain(new sC([e,s],this.commandOptions));smove=(e,s,t)=>this.chain(new sP([e,s,t],this.commandOptions));spop=(...e)=>this.chain(new sI(e,this.commandOptions));srandmember=(...e)=>this.chain(new sL(e,this.commandOptions));srem=(e,...s)=>this.chain(new sM([e,...s],this.commandOptions));sscan=(...e)=>this.chain(new sD(e,this.commandOptions));strlen=(...e)=>this.chain(new sJ(e,this.commandOptions));sunion=(...e)=>this.chain(new sF(e,this.commandOptions));sunionstore=(...e)=>this.chain(new sY(e,this.commandOptions));time=()=>this.chain(new sj(this.commandOptions));touch=(...e)=>this.chain(new s$(e,this.commandOptions));ttl=(...e)=>this.chain(new sW(e,this.commandOptions));type=(...e)=>this.chain(new sX(e,this.commandOptions));unlink=(...e)=>this.chain(new s_(e,this.commandOptions));zadd=(...e)=>(e[1],this.chain(new s3([e[0],e[1],...e.slice(2)],this.commandOptions)));xadd=(...e)=>this.chain(new sB(e,this.commandOptions));xack=(...e)=>this.chain(new sG(e,this.commandOptions));xdel=(...e)=>this.chain(new sq(e,this.commandOptions));xgroup=(...e)=>this.chain(new sV(e,this.commandOptions));xread=(...e)=>this.chain(new s2(e,this.commandOptions));xreadgroup=(...e)=>this.chain(new s8(e,this.commandOptions));xinfo=(...e)=>this.chain(new sQ(e,this.commandOptions));xlen=(...e)=>this.chain(new sZ(e,this.commandOptions));xpending=(...e)=>this.chain(new s1(e,this.commandOptions));xclaim=(...e)=>this.chain(new sK(e,this.commandOptions));xautoclaim=(...e)=>this.chain(new sH(e,this.commandOptions));xtrim=(...e)=>this.chain(new s6(e,this.commandOptions));xrange=(...e)=>this.chain(new s0(e,this.commandOptions));xrevrange=(...e)=>this.chain(new s5(e,this.commandOptions));zcard=(...e)=>this.chain(new s4(e,this.commandOptions));zcount=(...e)=>this.chain(new s9(e,this.commandOptions));zincrby=(e,s,t)=>this.chain(new s7([e,s,t],this.commandOptions));zinterstore=(...e)=>this.chain(new te(e,this.commandOptions));zlexcount=(...e)=>this.chain(new ts(e,this.commandOptions));zmscore=(...e)=>this.chain(new tw(e,this.commandOptions));zpopmax=(...e)=>this.chain(new tt(e,this.commandOptions));zpopmin=(...e)=>this.chain(new tn(e,this.commandOptions));zrange=(...e)=>this.chain(new ti(e,this.commandOptions));zrank=(e,s)=>this.chain(new tr([e,s],this.commandOptions));zrem=(e,...s)=>this.chain(new tc([e,...s],this.commandOptions));zremrangebylex=(...e)=>this.chain(new to(e,this.commandOptions));zremrangebyrank=(...e)=>this.chain(new th(e,this.commandOptions));zremrangebyscore=(...e)=>this.chain(new ta(e,this.commandOptions));zrevrank=(e,s)=>this.chain(new tp([e,s],this.commandOptions));zscan=(...e)=>this.chain(new tl(e,this.commandOptions));zscore=(e,s)=>this.chain(new tu([e,s],this.commandOptions));zunionstore=(...e)=>this.chain(new td(e,this.commandOptions));zunion=(...e)=>this.chain(new tm(e,this.commandOptions));get json(){return{arrappend:(...e)=>this.chain(new eS(e,this.commandOptions)),arrindex:(...e)=>this.chain(new eA(e,this.commandOptions)),arrinsert:(...e)=>this.chain(new eT(e,this.commandOptions)),arrlen:(...e)=>this.chain(new eR(e,this.commandOptions)),arrpop:(...e)=>this.chain(new ev(e,this.commandOptions)),arrtrim:(...e)=>this.chain(new eN(e,this.commandOptions)),clear:(...e)=>this.chain(new ek(e,this.commandOptions)),del:(...e)=>this.chain(new ez(e,this.commandOptions)),forget:(...e)=>this.chain(new eU(e,this.commandOptions)),get:(...e)=>this.chain(new eC(e,this.commandOptions)),merge:(...e)=>this.chain(new eP(e,this.commandOptions)),mget:(...e)=>this.chain(new eI(e,this.commandOptions)),mset:(...e)=>this.chain(new eL(e,this.commandOptions)),numincrby:(...e)=>this.chain(new eM(e,this.commandOptions)),nummultby:(...e)=>this.chain(new eD(e,this.commandOptions)),objkeys:(...e)=>this.chain(new eJ(e,this.commandOptions)),objlen:(...e)=>this.chain(new eF(e,this.commandOptions)),resp:(...e)=>this.chain(new eY(e,this.commandOptions)),set:(...e)=>this.chain(new ej(e,this.commandOptions)),strappend:(...e)=>this.chain(new e$(e,this.commandOptions)),strlen:(...e)=>this.chain(new eW(e,this.commandOptions)),toggle:(...e)=>this.chain(new eX(e,this.commandOptions)),type:(...e)=>this.chain(new e_(e,this.commandOptions))}}},ty=class{pipelinePromises=new WeakMap;activePipeline=null;indexInCurrentPipeline=0;redis;pipeline;pipelineCounter=0;constructor(e){this.redis=e,this.pipeline=e.pipeline()}async withAutoPipeline(e){let s=this.activePipeline??this.redis.pipeline();this.activePipeline||(this.activePipeline=s,this.indexInCurrentPipeline=0);let t=this.indexInCurrentPipeline++;e(s);let n=this.deferExecution().then(()=>{if(!this.pipelinePromises.has(s)){let e=s.exec({keepErrors:!0});this.pipelineCounter+=1,this.pipelinePromises.set(s,e),this.activePipeline=null}return this.pipelinePromises.get(s)}),i=(await n)[t];if(i.error)throw new c(`Command failed: ${i.error}`);return i.result}async deferExecution(){await Promise.resolve(),await Promise.resolve()}},tO=class extends x{constructor(e,s){super([],{...s,headers:{Accept:"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"},path:["psubscribe",...e],streamOptions:{isStreaming:!0,onMessage:s?.streamOptions?.onMessage,signal:s?.streamOptions?.signal}})}},tb=class extends EventTarget{subscriptions;client;listeners;constructor(e,s,t=!1){for(let n of(super(),this.client=e,this.subscriptions=new Map,this.listeners=new Map,s))t?this.subscribeToPattern(n):this.subscribeToChannel(n)}subscribeToChannel(e){let s=new AbortController,t=new tf([e],{streamOptions:{signal:s.signal,onMessage:e=>this.handleMessage(e,!1)}});t.exec(this.client).catch(e=>{"AbortError"!==e.name&&this.dispatchToListeners("error",e)}),this.subscriptions.set(e,{command:t,controller:s,isPattern:!1})}subscribeToPattern(e){let s=new AbortController,t=new tO([e],{streamOptions:{signal:s.signal,onMessage:e=>this.handleMessage(e,!0)}});t.exec(this.client).catch(e=>{"AbortError"!==e.name&&this.dispatchToListeners("error",e)}),this.subscriptions.set(e,{command:t,controller:s,isPattern:!0})}handleMessage(e,s){let t=e.replace(/^data:\s*/,""),n=t.indexOf(","),i=t.indexOf(",",n+1),r=s?t.indexOf(",",i+1):-1;if(-1!==n&&-1!==i){let e=t.slice(0,n);if(s&&"pmessage"===e&&-1!==r){let e=t.slice(n+1,i),s=t.slice(i+1,r),c=t.slice(r+1);try{let t=JSON.parse(c);this.dispatchToListeners("pmessage",{pattern:e,channel:s,message:t}),this.dispatchToListeners(`pmessage:${e}`,{pattern:e,channel:s,message:t})}catch(e){this.dispatchToListeners("error",Error(`Failed to parse message: ${e}`))}}else{let s=t.slice(n+1,i),r=t.slice(i+1);try{if("subscribe"===e||"psubscribe"===e||"unsubscribe"===e||"punsubscribe"===e){let s=Number.parseInt(r);this.dispatchToListeners(e,s)}else{let t=JSON.parse(r);this.dispatchToListeners(e,{channel:s,message:t}),this.dispatchToListeners(`${e}:${s}`,{channel:s,message:t})}}catch(e){this.dispatchToListeners("error",Error(`Failed to parse message: ${e}`))}}}}dispatchToListeners(e,s){let t=this.listeners.get(e);if(t)for(let e of t)e(s)}on(e,s){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e)?.add(s)}removeAllListeners(){this.listeners.clear()}async unsubscribe(e){if(e)for(let s of e){let e=this.subscriptions.get(s);if(e){try{e.controller.abort()}catch{}this.subscriptions.delete(s)}}else{for(let e of this.subscriptions.values())try{e.controller.abort()}catch{}this.subscriptions.clear(),this.removeAllListeners()}}getSubscribedChannels(){return[...this.subscriptions.keys()]}},tf=class extends x{constructor(e,s){super([],{...s,headers:{Accept:"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"},path:["subscribe",...e],streamOptions:{isStreaming:!0,onMessage:s?.streamOptions?.onMessage,signal:s?.streamOptions?.signal}})}},tE=class{script;sha1;redis;constructor(e,s){this.redis=e,this.sha1=this.digest(s),this.script=s}async eval(e,s){return await this.redis.eval(this.script,e,s)}async evalsha(e,s){return await this.redis.evalsha(this.sha1,e,s)}async exec(e,s){return await this.redis.evalsha(this.sha1,e,s).catch(async t=>{if(t instanceof Error&&t.message.toLowerCase().includes("noscript"))return await this.redis.eval(this.script,e,s);throw t})}digest(e){return n.stringify(i(e))}},tS=class{script;sha1;redis;constructor(e,s){this.redis=e,this.sha1=this.digest(s),this.script=s}async evalRo(e,s){return await this.redis.evalRo(this.script,e,s)}async evalshaRo(e,s){return await this.redis.evalshaRo(this.sha1,e,s)}async exec(e,s){return await this.redis.evalshaRo(this.sha1,e,s).catch(async t=>{if(t instanceof Error&&t.message.toLowerCase().includes("noscript"))return await this.redis.evalRo(this.script,e,s);throw t})}digest(e){return n.stringify(i(e))}},tA=class{client;opts;enableTelemetry;enableAutoPipelining;constructor(e,s){this.client=e,this.opts=s,this.enableTelemetry=s?.enableTelemetry??!0,s?.readYourWrites===!1&&(this.client.readYourWrites=!1),this.enableAutoPipelining=s?.enableAutoPipelining??!0}get readYourWritesSyncToken(){return this.client.upstashSyncToken}set readYourWritesSyncToken(e){this.client.upstashSyncToken=e}get json(){return{arrappend:(...e)=>new eS(e,this.opts).exec(this.client),arrindex:(...e)=>new eA(e,this.opts).exec(this.client),arrinsert:(...e)=>new eT(e,this.opts).exec(this.client),arrlen:(...e)=>new eR(e,this.opts).exec(this.client),arrpop:(...e)=>new ev(e,this.opts).exec(this.client),arrtrim:(...e)=>new eN(e,this.opts).exec(this.client),clear:(...e)=>new ek(e,this.opts).exec(this.client),del:(...e)=>new ez(e,this.opts).exec(this.client),forget:(...e)=>new eU(e,this.opts).exec(this.client),get:(...e)=>new eC(e,this.opts).exec(this.client),merge:(...e)=>new eP(e,this.opts).exec(this.client),mget:(...e)=>new eI(e,this.opts).exec(this.client),mset:(...e)=>new eL(e,this.opts).exec(this.client),numincrby:(...e)=>new eM(e,this.opts).exec(this.client),nummultby:(...e)=>new eD(e,this.opts).exec(this.client),objkeys:(...e)=>new eJ(e,this.opts).exec(this.client),objlen:(...e)=>new eF(e,this.opts).exec(this.client),resp:(...e)=>new eY(e,this.opts).exec(this.client),set:(...e)=>new ej(e,this.opts).exec(this.client),strappend:(...e)=>new e$(e,this.opts).exec(this.client),strlen:(...e)=>new eW(e,this.opts).exec(this.client),toggle:(...e)=>new eX(e,this.opts).exec(this.client),type:(...e)=>new e_(e,this.opts).exec(this.client)}}use=e=>{let s=this.client.request.bind(this.client);this.client.request=t=>e(t,s)};addTelemetry=e=>{if(this.enableTelemetry)try{this.client.mergeTelemetry(e)}catch{}};createScript(e,s){return s?.readonly?new tS(this,e):new tE(this,e)}pipeline=()=>new tg({client:this.client,commandOptions:this.opts,multiExec:!1});autoPipeline=()=>(function e(s,t){return s.autoPipelineExecutor||(s.autoPipelineExecutor=new ty(s)),new Proxy(s,{get:(s,n)=>"pipelineCounter"===n?s.autoPipelineExecutor.pipelineCounter:"json"===n?e(s,!0):n in s&&!(n in s.autoPipelineExecutor.pipeline)?s[n]:(t?"function"==typeof s.autoPipelineExecutor.pipeline.json[n]:"function"==typeof s.autoPipelineExecutor.pipeline[n])?(...e)=>s.autoPipelineExecutor.withAutoPipeline(s=>{t?s.json[n](...e):s[n](...e)}):s.autoPipelineExecutor.pipeline[n]})})(this);multi=()=>new tg({client:this.client,commandOptions:this.opts,multiExec:!0});bitfield=(...e)=>new O(e,this.client,this.opts);append=(...e)=>new g(e,this.opts).exec(this.client);bitcount=(...e)=>new y(e,this.opts).exec(this.client);bitop=(e,s,t,...n)=>new b([e,s,t,...n],this.opts).exec(this.client);bitpos=(...e)=>new f(e,this.opts).exec(this.client);copy=(...e)=>new E(e,this.opts).exec(this.client);dbsize=()=>new S(this.opts).exec(this.client);decr=(...e)=>new A(e,this.opts).exec(this.client);decrby=(...e)=>new T(e,this.opts).exec(this.client);del=(...e)=>new R(e,this.opts).exec(this.client);echo=(...e)=>new v(e,this.opts).exec(this.client);evalRo=(...e)=>new N(e,this.opts).exec(this.client);eval=(...e)=>new k(e,this.opts).exec(this.client);evalshaRo=(...e)=>new z(e,this.opts).exec(this.client);evalsha=(...e)=>new U(e,this.opts).exec(this.client);exec=e=>new C(e,this.opts).exec(this.client);exists=(...e)=>new P(e,this.opts).exec(this.client);expire=(...e)=>new I(e,this.opts).exec(this.client);expireat=(...e)=>new L(e,this.opts).exec(this.client);flushall=e=>new M(e,this.opts).exec(this.client);flushdb=(...e)=>new D(e,this.opts).exec(this.client);geoadd=(...e)=>new J(e,this.opts).exec(this.client);geopos=(...e)=>new j(e,this.opts).exec(this.client);geodist=(...e)=>new F(e,this.opts).exec(this.client);geohash=(...e)=>new Y(e,this.opts).exec(this.client);geosearch=(...e)=>new $(e,this.opts).exec(this.client);geosearchstore=(...e)=>new W(e,this.opts).exec(this.client);get=(...e)=>new X(e,this.opts).exec(this.client);getbit=(...e)=>new _(e,this.opts).exec(this.client);getdel=(...e)=>new G(e,this.opts).exec(this.client);getex=(...e)=>new B(e,this.opts).exec(this.client);getrange=(...e)=>new H(e,this.opts).exec(this.client);getset=(e,s)=>new K([e,s],this.opts).exec(this.client);hdel=(...e)=>new q(e,this.opts).exec(this.client);hexists=(...e)=>new V(e,this.opts).exec(this.client);hexpire=(...e)=>new Q(e,this.opts).exec(this.client);hexpireat=(...e)=>new Z(e,this.opts).exec(this.client);hexpiretime=(...e)=>new ee(e,this.opts).exec(this.client);httl=(...e)=>new ey(e,this.opts).exec(this.client);hpexpire=(...e)=>new et(e,this.opts).exec(this.client);hpexpireat=(...e)=>new en(e,this.opts).exec(this.client);hpexpiretime=(...e)=>new ei(e,this.opts).exec(this.client);hpttl=(...e)=>new er(e,this.opts).exec(this.client);hpersist=(...e)=>new es(e,this.opts).exec(this.client);hget=(...e)=>new ec(e,this.opts).exec(this.client);hgetall=(...e)=>new eo(e,this.opts).exec(this.client);hincrby=(...e)=>new eh(e,this.opts).exec(this.client);hincrbyfloat=(...e)=>new ea(e,this.opts).exec(this.client);hkeys=(...e)=>new ep(e,this.opts).exec(this.client);hlen=(...e)=>new el(e,this.opts).exec(this.client);hmget=(...e)=>new eu(e,this.opts).exec(this.client);hmset=(e,s)=>new em([e,s],this.opts).exec(this.client);hrandfield=(e,s,t)=>new w([e,s,t],this.opts).exec(this.client);hscan=(...e)=>new ed(e,this.opts).exec(this.client);hset=(e,s)=>new ex([e,s],this.opts).exec(this.client);hsetnx=(e,s,t)=>new ew([e,s,t],this.opts).exec(this.client);hstrlen=(...e)=>new eg(e,this.opts).exec(this.client);hvals=(...e)=>new eO(e,this.opts).exec(this.client);incr=(...e)=>new eb(e,this.opts).exec(this.client);incrby=(...e)=>new ef(e,this.opts).exec(this.client);incrbyfloat=(...e)=>new eE(e,this.opts).exec(this.client);keys=(...e)=>new eG(e,this.opts).exec(this.client);lindex=(...e)=>new eB(e,this.opts).exec(this.client);linsert=(e,s,t,n)=>new eH([e,s,t,n],this.opts).exec(this.client);llen=(...e)=>new eK(e,this.opts).exec(this.client);lmove=(...e)=>new eq(e,this.opts).exec(this.client);lpop=(...e)=>new eQ(e,this.opts).exec(this.client);lmpop=(...e)=>new eV(e,this.opts).exec(this.client);lpos=(...e)=>new eZ(e,this.opts).exec(this.client);lpush=(e,...s)=>new e1([e,...s],this.opts).exec(this.client);lpushx=(e,...s)=>new e0([e,...s],this.opts).exec(this.client);lrange=(...e)=>new e2(e,this.opts).exec(this.client);lrem=(e,s,t)=>new e8([e,s,t],this.opts).exec(this.client);lset=(e,s,t)=>new e5([e,s,t],this.opts).exec(this.client);ltrim=(...e)=>new e6(e,this.opts).exec(this.client);mget=(...e)=>new e3(e,this.opts).exec(this.client);mset=e=>new e4([e],this.opts).exec(this.client);msetnx=e=>new e9([e],this.opts).exec(this.client);persist=(...e)=>new e7(e,this.opts).exec(this.client);pexpire=(...e)=>new se(e,this.opts).exec(this.client);pexpireat=(...e)=>new ss(e,this.opts).exec(this.client);pfadd=(...e)=>new st(e,this.opts).exec(this.client);pfcount=(...e)=>new sn(e,this.opts).exec(this.client);pfmerge=(...e)=>new si(e,this.opts).exec(this.client);ping=e=>new sr(e,this.opts).exec(this.client);psetex=(e,s,t)=>new sc([e,s,t],this.opts).exec(this.client);psubscribe=e=>{let s=Array.isArray(e)?e:[e];return new tb(this.client,s,!0)};pttl=(...e)=>new so(e,this.opts).exec(this.client);publish=(...e)=>new sh(e,this.opts).exec(this.client);randomkey=()=>new sa().exec(this.client);rename=(...e)=>new sp(e,this.opts).exec(this.client);renamenx=(...e)=>new sl(e,this.opts).exec(this.client);rpop=(...e)=>new su(e,this.opts).exec(this.client);rpush=(e,...s)=>new sm([e,...s],this.opts).exec(this.client);rpushx=(e,...s)=>new sd([e,...s],this.opts).exec(this.client);sadd=(e,s,...t)=>new sx([e,s,...t],this.opts).exec(this.client);scan=(...e)=>new sw(e,this.opts).exec(this.client);scard=(...e)=>new sg(e,this.opts).exec(this.client);scriptExists=(...e)=>new sy(e,this.opts).exec(this.client);scriptFlush=(...e)=>new sO(e,this.opts).exec(this.client);scriptLoad=(...e)=>new sb(e,this.opts).exec(this.client);sdiff=(...e)=>new sf(e,this.opts).exec(this.client);sdiffstore=(...e)=>new sE(e,this.opts).exec(this.client);set=(e,s,t)=>new sS([e,s,t],this.opts).exec(this.client);setbit=(...e)=>new sA(e,this.opts).exec(this.client);setex=(e,s,t)=>new sT([e,s,t],this.opts).exec(this.client);setnx=(e,s)=>new sR([e,s],this.opts).exec(this.client);setrange=(...e)=>new sv(e,this.opts).exec(this.client);sinter=(...e)=>new sN(e,this.opts).exec(this.client);sinterstore=(...e)=>new sk(e,this.opts).exec(this.client);sismember=(e,s)=>new sz([e,s],this.opts).exec(this.client);smismember=(e,s)=>new sC([e,s],this.opts).exec(this.client);smembers=(...e)=>new sU(e,this.opts).exec(this.client);smove=(e,s,t)=>new sP([e,s,t],this.opts).exec(this.client);spop=(...e)=>new sI(e,this.opts).exec(this.client);srandmember=(...e)=>new sL(e,this.opts).exec(this.client);srem=(e,...s)=>new sM([e,...s],this.opts).exec(this.client);sscan=(...e)=>new sD(e,this.opts).exec(this.client);strlen=(...e)=>new sJ(e,this.opts).exec(this.client);subscribe=e=>{let s=Array.isArray(e)?e:[e];return new tb(this.client,s)};sunion=(...e)=>new sF(e,this.opts).exec(this.client);sunionstore=(...e)=>new sY(e,this.opts).exec(this.client);time=()=>new sj().exec(this.client);touch=(...e)=>new s$(e,this.opts).exec(this.client);ttl=(...e)=>new sW(e,this.opts).exec(this.client);type=(...e)=>new sX(e,this.opts).exec(this.client);unlink=(...e)=>new s_(e,this.opts).exec(this.client);xadd=(...e)=>new sB(e,this.opts).exec(this.client);xack=(...e)=>new sG(e,this.opts).exec(this.client);xdel=(...e)=>new sq(e,this.opts).exec(this.client);xgroup=(...e)=>new sV(e,this.opts).exec(this.client);xread=(...e)=>new s2(e,this.opts).exec(this.client);xreadgroup=(...e)=>new s8(e,this.opts).exec(this.client);xinfo=(...e)=>new sQ(e,this.opts).exec(this.client);xlen=(...e)=>new sZ(e,this.opts).exec(this.client);xpending=(...e)=>new s1(e,this.opts).exec(this.client);xclaim=(...e)=>new sK(e,this.opts).exec(this.client);xautoclaim=(...e)=>new sH(e,this.opts).exec(this.client);xtrim=(...e)=>new s6(e,this.opts).exec(this.client);xrange=(...e)=>new s0(e,this.opts).exec(this.client);xrevrange=(...e)=>new s5(e,this.opts).exec(this.client);zadd=(...e)=>(e[1],new s3([e[0],e[1],...e.slice(2)],this.opts).exec(this.client));zcard=(...e)=>new s4(e,this.opts).exec(this.client);zcount=(...e)=>new s9(e,this.opts).exec(this.client);zdiffstore=(...e)=>new tx(e,this.opts).exec(this.client);zincrby=(e,s,t)=>new s7([e,s,t],this.opts).exec(this.client);zinterstore=(...e)=>new te(e,this.opts).exec(this.client);zlexcount=(...e)=>new ts(e,this.opts).exec(this.client);zmscore=(...e)=>new tw(e,this.opts).exec(this.client);zpopmax=(...e)=>new tt(e,this.opts).exec(this.client);zpopmin=(...e)=>new tn(e,this.opts).exec(this.client);zrange=(...e)=>new ti(e,this.opts).exec(this.client);zrank=(e,s)=>new tr([e,s],this.opts).exec(this.client);zrem=(e,...s)=>new tc([e,...s],this.opts).exec(this.client);zremrangebylex=(...e)=>new to(e,this.opts).exec(this.client);zremrangebyrank=(...e)=>new th(e,this.opts).exec(this.client);zremrangebyscore=(...e)=>new ta(e,this.opts).exec(this.client);zrevrank=(e,s)=>new tp([e,s],this.opts).exec(this.client);zscan=(...e)=>new tl(e,this.opts).exec(this.client);zscore=(e,s)=>new tu([e,s],this.opts).exec(this.client);zunion=(...e)=>new tm(e,this.opts).exec(this.client);zunionstore=(...e)=>new td(e,this.opts).exec(this.client)};"undefined"==typeof atob&&(global.atob=e=>Buffer.from(e,"base64").toString("utf8"));var tT=class e extends tA{constructor(e){if("request"in e){super(e);return}if(e.url?(e.url.startsWith(" ")||e.url.endsWith(" ")||/\r|\n/.test(e.url))&&console.warn("[Upstash Redis] The redis url contains whitespace or newline, which can cause errors!"):console.warn("[Upstash Redis] The 'url' property is missing or undefined in your Redis config."),e.token?(e.token.startsWith(" ")||e.token.endsWith(" ")||/\r|\n/.test(e.token))&&console.warn("[Upstash Redis] The redis token contains whitespace or newline, which can cause errors!"):console.warn("[Upstash Redis] The 'token' property is missing or undefined in your Redis config."),super(new p({baseUrl:e.url,retry:e.retry,headers:{authorization:`Bearer ${e.token}`},agent:e.agent,responseEncoding:e.responseEncoding,cache:e.cache??"no-store",signal:e.signal,keepAlive:e.keepAlive,readYourWrites:e.readYourWrites}),{automaticDeserialization:e.automaticDeserialization,enableTelemetry:!process.env.UPSTASH_DISABLE_TELEMETRY,latencyLogging:e.latencyLogging,enableAutoPipelining:e.enableAutoPipelining}),this.addTelemetry({runtime:"string"==typeof EdgeRuntime?"edge-light":`node@${process.version}`,platform:process.env.VERCEL?"vercel":process.env.AWS_REGION?"aws":"unknown",sdk:"@upstash/redis@v1.34.8"}),this.enableAutoPipelining)return this.autoPipeline()}static fromEnv(s){if(void 0===process.env)throw TypeError('[Upstash Redis] Unable to get environment variables, `process.env` is undefined. If you are deploying to cloudflare, please import from "@upstash/redis/cloudflare" instead');let t=process.env.UPSTASH_REDIS_REST_URL||process.env.KV_REST_API_URL;t||console.warn("[Upstash Redis] Unable to find environment variable: `UPSTASH_REDIS_REST_URL`");let n=process.env.UPSTASH_REDIS_REST_TOKEN||process.env.KV_REST_API_TOKEN;return n||console.warn("[Upstash Redis] Unable to find environment variable: `UPSTASH_REDIS_REST_TOKEN`"),new e({...s,url:t,token:n})}}}};