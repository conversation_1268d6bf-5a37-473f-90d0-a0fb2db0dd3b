"use strict";(()=>{var e={};e.id=6067,e.ids=[6067],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},66067:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>c,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>l});var o=t(49303),n=t(88716),i=t(60670),s=t(22249),u=e([s]);s=(u.then?(await u)():u)[0];let p=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/debug/route",pathname:"/api/debug",filename:"route",bundlePath:"app/api/debug/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\debug\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:g}=p,m="/api/debug/route";function c(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:l})}a()}catch(e){a(e)}})},22249:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{GET:()=>s,dynamic:()=>u});var o=t(87070),n=t(19910),i=e([n]);n=(i.then?(await i)():i)[0];let u="force-dynamic";async function s(e){try{let{searchParams:r}=new URL(e.url),t=r.get("handle");if(!t)return o.NextResponse.json({error:"Product handle is required"},{status:400});let a=await (0,n.gF)(t);if(!a)return o.NextResponse.json({error:`Product not found: ${t}`},{status:404});return o.NextResponse.json({id:a.id,name:a.name,slug:a.slug,price:a.price,regularPrice:a.regularPrice,salePrice:a.salePrice,onSale:a.onSale,variations:a.variations?.nodes.map(e=>({id:e.id,name:e.name,price:e.price,regularPrice:e.regularPrice,salePrice:e.salePrice}))})}catch(e){return console.error("API: Error in debug route:",e),o.NextResponse.json({error:`Failed to fetch product: ${e.message}`},{status:500})}}a()}catch(e){a(e)}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,4766,4868,9910],()=>t(66067));module.exports=a})();