"use strict";(()=>{var e={};e.id=1607,e.ids=[1607],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93690:e=>{e.exports=import("graphql-request")},49765:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>l,requestAsyncStorage:()=>u,routeModule:()=>c,serverHooks:()=>f,staticGenerationAsyncStorage:()=>p});var s=r(49303),o=r(88716),n=r(60670),i=r(89326),d=e([i]);i=(d.then?(await d)():d)[0];let c=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/checkout/route",pathname:"/api/checkout",filename:"route",bundlePath:"app/api/checkout/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\checkout\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:u,staticGenerationAsyncStorage:p,serverHooks:f}=c,y="/api/checkout/route";function l(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:p})}a()}catch(e){a(e)}})},71615:(e,t,r)=>{var a=r(88757);r.o(a,"cookies")&&r.d(t,{cookies:function(){return a.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return o}});let a=r(45869),s=r(6278);class o{get isEnabled(){return this._provider.isEnabled}enable(){let e=a.staticGenerationAsyncStorage.getStore();return e&&(0,s.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=a.staticGenerationAsyncStorage.getStore();return e&&(0,s.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return p},draftMode:function(){return f},headers:function(){return u}});let a=r(68996),s=r(53047),o=r(92044),n=r(72934),i=r(33085),d=r(6278),l=r(45869),c=r(54580);function u(){let e="headers",t=l.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.HeadersAdapter.seal(new Headers({}));(0,d.trackDynamicDataAccessed)(t,e)}return(0,c.getExpectedRequestStore)(e).headers}function p(){let e="cookies",t=l.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return a.RequestCookiesAdapter.seal(new o.RequestCookies(new Headers({})));(0,d.trackDynamicDataAccessed)(t,e)}let r=(0,c.getExpectedRequestStore)(e),s=n.actionAsyncStorage.getStore();return(null==s?void 0:s.isAction)||(null==s?void 0:s.isAppRoute)?r.mutableCookies:r.cookies}function f(){let e=(0,c.getExpectedRequestStore)("draftMode");return new i.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return o},ReadonlyHeadersError:function(){return s}});let a=r(38238);class s extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new s}}class o extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,s){if("symbol"==typeof r)return a.ReflectAdapter.get(t,r,s);let o=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==n)return a.ReflectAdapter.get(t,n,s)},set(t,r,s,o){if("symbol"==typeof r)return a.ReflectAdapter.set(t,r,s,o);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return a.ReflectAdapter.set(t,i??r,s,o)},has(t,r){if("symbol"==typeof r)return a.ReflectAdapter.has(t,r);let s=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0!==o&&a.ReflectAdapter.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return a.ReflectAdapter.deleteProperty(t,r);let s=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0===o||a.ReflectAdapter.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return s.callable;default:return a.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,a]of this.entries())e.call(t,a,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return u},ReadonlyRequestCookiesError:function(){return n},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return c},getModifiedCookieValues:function(){return l}});let a=r(92044),s=r(38238),o=r(45869);class n extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new n}}class i{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return n.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}}let d=Symbol.for("next.mutated.cookies");function l(e){let t=e[d];return t&&Array.isArray(t)&&0!==t.length?t:[]}function c(e,t){let r=l(t);if(0===r.length)return!1;let s=new a.ResponseCookies(e),o=s.getAll();for(let e of r)s.set(e);for(let e of o)s.set(e);return!0}class u{static wrap(e,t){let r=new a.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,l=()=>{let e=o.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new a.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case d:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{l()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{l()}};default:return s.ReflectAdapter.get(e,t,r)}}})}}},89326:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{POST:()=>d});var s=r(87070),o=r(93690),n=r(71615),i=e([o]);o=(i.then?(await i)():i)[0];let l=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",c=new o.GraphQLClient(l),u=(0,o.gql)`
  mutation CreateOrder($input: CreateOrderInput!) {
    createOrder(input: $input) {
      clientMutationId
      order {
        id
        databaseId
        orderKey
        orderNumber
        status
        total
      }
    }
  }
`,p=(0,o.gql)`
  mutation ProcessPayment($input: ProcessPaymentInput!) {
    processPayment(input: $input) {
      clientMutationId
      paymentResult {
        redirectUrl
        paymentStatus
        paymentDetails
      }
    }
  }
`;async function d(e){try{let t=await e.json(),r=await fetch(`${e.nextUrl.origin}/api/products/validate-stock`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:t.line_items.map(e=>({productId:e.product_id,variationId:e.variation_id,quantity:e.quantity}))})});if(!r.ok)throw Error("Stock validation failed");let a=await r.json();if(!a.allAvailable){let e=a.validations.filter(e=>!e.available).map(e=>e.message).join(", ");return s.NextResponse.json({success:!1,message:`Some items are no longer available: ${e}`,stockValidation:a.validations},{status:400})}let i=(0,n.cookies)(),d=i.get("woo_auth_token")?.value,f=d?new o.GraphQLClient(l,{headers:{Authorization:`Bearer ${d}`}}):c,y={clientMutationId:"create_order",billing:{firstName:t.billing.first_name,lastName:t.billing.last_name,address1:t.billing.address_1,address2:t.billing.address_2||"",city:t.billing.city,state:t.billing.state,postcode:t.billing.postcode,country:t.billing.country,email:t.billing.email,phone:t.billing.phone},shipping:{firstName:t.shipping.first_name,lastName:t.shipping.last_name,address1:t.shipping.address_1,address2:t.shipping.address_2||"",city:t.shipping.city,state:t.shipping.state,postcode:t.shipping.postcode,country:t.shipping.country},lineItems:t.line_items.map(e=>({productId:e.product_id,variationId:e.variation_id||null,quantity:e.quantity})),customerNote:t.customer_note||"",paymentMethod:t.payment_method,isPaid:"cod"!==t.payment_method,status:"cod"===t.payment_method?"PENDING":"PROCESSING"},h=await f.request(u,{input:y});if(!h.createOrder?.order)throw Error("Failed to create order");let m=h.createOrder.order;if("cod"!==t.payment_method&&"stripe"===t.payment_method){let e={clientMutationId:"process_payment",orderId:m.databaseId,paymentMethod:"stripe",paymentData:{}},t=await f.request(p,{input:e});if(t.processPayment?.paymentResult?.paymentStatus!=="SUCCESS")throw Error("Payment processing failed");if(t.processPayment?.paymentResult?.redirectUrl)return s.NextResponse.json({success:!0,orderId:m.databaseId,redirectUrl:t.processPayment.paymentResult.redirectUrl})}return s.NextResponse.json({success:!0,orderId:m.databaseId,orderKey:m.orderKey,orderNumber:m.orderNumber,status:m.status})}catch(e){return console.error("Checkout error:",e),s.NextResponse.json({success:!1,message:e instanceof Error?e.message:"An error occurred during checkout"},{status:500})}}a()}catch(e){a(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972],()=>r(49765));module.exports=a})();