"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7696],{24500:function(e,r,t){let n,o,a,l,i,s,c,d,u,f,p,h;Object.defineProperty(r,"__esModule",{value:!0});let m=t(61757);Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{emitter:function(){return G},hydrate:function(){return eu},initialize:function(){return J},router:function(){return n},version:function(){return V}});let _=t(38754),y=t(85893);t(40037);let g=_._(t(67294)),b=_._(t(20745)),x=t(20077),E=_._(t(58967)),P=t(37171),w=t(12179),v=t(31735),C=t(38600),R=t(45758),S=t(45782),A=t(1493),T=_._(t(52071)),j=_._(t(21413)),N=_._(t(65736)),k=t(63622),L=t(37253),M=t(80676),H=t(98261),B=t(91566),D=t(71838),I=t(3068),q=t(82488),O=t(10213),X=_._(t(36920)),F=_._(t(57930)),z=_._(t(95179)),V="14.2.24",G=(0,E.default)(),U=e=>[].slice.call(e),W=!1;class Y extends g.default.Component{componentDidCatch(e,r){this.props.fn(e,r)}componentDidMount(){this.scrollToHash(),n.isSsr&&(o.isFallback||o.nextExport&&((0,v.isDynamicRoute)(n.pathname)||location.search||W)||o.props&&o.props.__N_SSG&&(location.search||W))&&n.replace(n.pathname+"?"+String((0,C.assign)((0,C.urlQueryToSearchParams)(n.query),new URLSearchParams(location.search))),a,{_h:1,shallow:!o.isFallback&&!W}).catch(e=>{if(!e.cancelled)throw e})}componentDidUpdate(){this.scrollToHash()}scrollToHash(){let{hash:e}=location;if(!(e=e&&e.substring(1)))return;let r=document.getElementById(e);r&&setTimeout(()=>r.scrollIntoView(),0)}render(){return this.props.children}}async function J(e){void 0===e&&(e={}),F.default.onSpanEnd(z.default),o=JSON.parse(document.getElementById("__NEXT_DATA__").textContent),window.__NEXT_DATA__=o,h=o.defaultLocale;let r=o.assetPrefix||"";if(self.__next_set_public_path__(""+r+"/_next/"),(0,R.setConfig)({serverRuntimeConfig:{},publicRuntimeConfig:o.runtimeConfig||{}}),a=(0,S.getURL)(),(0,D.hasBasePath)(a)&&(a=(0,B.removeBasePath)(a)),o.scriptLoader){let{initScriptLoader:e}=t(95026);e(o.scriptLoader)}l=new j.default(o.buildId,r);let c=e=>{let[r,t]=e;return l.routeLoader.onEntrypoint(r,t)};return window.__NEXT_P&&window.__NEXT_P.map(e=>setTimeout(()=>c(e),0)),window.__NEXT_P=[],window.__NEXT_P.push=c,(s=(0,T.default)()).getIsSsr=()=>n.isSsr,i=document.getElementById("__next"),{assetPrefix:r}}function Q(e,r){return(0,y.jsx)(e,{...r})}function K(e){var r;let{children:t}=e,o=g.default.useMemo(()=>(0,q.adaptForAppRouterInstance)(n),[]);return(0,y.jsx)(Y,{fn:e=>$({App:u,err:e}).catch(e=>console.error("Error rendering page: ",e)),children:(0,y.jsx)(I.AppRouterContext.Provider,{value:o,children:(0,y.jsx)(O.SearchParamsContext.Provider,{value:(0,q.adaptForSearchParams)(n),children:(0,y.jsx)(q.PathnameContextProviderAdapter,{router:n,isAutoExport:null!=(r=self.__NEXT_DATA__.autoExport)&&r,children:(0,y.jsx)(O.PathParamsContext.Provider,{value:(0,q.adaptForPathParams)(n),children:(0,y.jsx)(P.RouterContext.Provider,{value:(0,L.makePublicRouterInstance)(n),children:(0,y.jsx)(x.HeadManagerContext.Provider,{value:s,children:(0,y.jsx)(H.ImageConfigContext.Provider,{value:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1},children:t})})})})})})})})}let Z=e=>r=>{let t={...r,Component:p,err:o.err,router:n};return(0,y.jsx)(K,{children:Q(e,t)})};function $(e){let{App:r,err:i}=e;return console.error(i),console.error("A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred"),l.loadPage("/_error").then(n=>{let{page:o,styleSheets:a}=n;return(null==c?void 0:c.Component)===o?Promise.resolve().then(()=>m._(t(18529))).then(n=>Promise.resolve().then(()=>m._(t(48141))).then(t=>(r=t.default,e.App=r,n))).then(e=>({ErrorComponent:e.default,styleSheets:[]})):{ErrorComponent:o,styleSheets:a}}).then(t=>{var l;let{ErrorComponent:s,styleSheets:c}=t,d=Z(r),u={Component:s,AppTree:d,router:n,ctx:{err:i,pathname:o.page,query:o.query,asPath:a,AppTree:d}};return Promise.resolve((null==(l=e.props)?void 0:l.err)?e.props:(0,S.loadGetInitialProps)(r,u)).then(r=>ec({...e,err:i,Component:s,styleSheets:c,props:r}))})}function ee(e){let{callback:r}=e;return g.default.useLayoutEffect(()=>r(),[r]),null}let er={navigationStart:"navigationStart",beforeRender:"beforeRender",afterRender:"afterRender",afterHydrate:"afterHydrate",routeChange:"routeChange"},et={hydration:"Next.js-hydration",beforeHydration:"Next.js-before-hydration",routeChangeToRender:"Next.js-route-change-to-render",render:"Next.js-render"},en=null,eo=!0;function ea(){[er.beforeRender,er.afterHydrate,er.afterRender,er.routeChange].forEach(e=>performance.clearMarks(e))}function el(){S.ST&&(performance.mark(er.afterHydrate),performance.getEntriesByName(er.beforeRender,"mark").length&&(performance.measure(et.beforeHydration,er.navigationStart,er.beforeRender),performance.measure(et.hydration,er.beforeRender,er.afterHydrate)),f&&performance.getEntriesByName(et.hydration).forEach(f),ea())}function ei(){if(!S.ST)return;performance.mark(er.afterRender);let e=performance.getEntriesByName(er.routeChange,"mark");e.length&&(performance.getEntriesByName(er.beforeRender,"mark").length&&(performance.measure(et.routeChangeToRender,e[0].name,er.beforeRender),performance.measure(et.render,er.beforeRender,er.afterRender),f&&(performance.getEntriesByName(et.render).forEach(f),performance.getEntriesByName(et.routeChangeToRender).forEach(f))),ea(),[et.routeChangeToRender,et.render].forEach(e=>performance.clearMeasures(e)))}function es(e){let{callbacks:r,children:t}=e;return g.default.useLayoutEffect(()=>r.forEach(e=>e()),[r]),g.default.useEffect(()=>{(0,N.default)(f)},[]),t}function ec(e){let r,{App:t,Component:o,props:a,err:l}=e,s="initial"in e?void 0:e.styleSheets;o=o||c.Component;let u={...a=a||c.props,Component:o,err:l,router:n};c=u;let f=!1,p=new Promise((e,t)=>{d&&d(),r=()=>{d=null,e()},d=()=>{f=!0,d=null;let e=Error("Cancel rendering route");e.cancelled=!0,t(e)}});function h(){r()}!function(){if(!s)return;let e=new Set(U(document.querySelectorAll("style[data-n-href]")).map(e=>e.getAttribute("data-n-href"))),r=document.querySelector("noscript[data-n-css]"),t=null==r?void 0:r.getAttribute("data-n-css");s.forEach(r=>{let{href:n,text:o}=r;if(!e.has(n)){let e=document.createElement("style");e.setAttribute("data-n-href",n),e.setAttribute("media","x"),t&&e.setAttribute("nonce",t),document.head.appendChild(e),e.appendChild(document.createTextNode(o))}})}();let m=(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(ee,{callback:function(){if(s&&!f){let e=new Set(s.map(e=>e.href)),r=U(document.querySelectorAll("style[data-n-href]")),t=r.map(e=>e.getAttribute("data-n-href"));for(let n=0;n<t.length;++n)e.has(t[n])?r[n].removeAttribute("media"):r[n].setAttribute("media","x");let n=document.querySelector("noscript[data-n-css]");n&&s.forEach(e=>{let{href:r}=e,t=document.querySelector('style[data-n-href="'+r+'"]');t&&(n.parentNode.insertBefore(t,n.nextSibling),n=t)}),U(document.querySelectorAll("link[data-n-p]")).forEach(e=>{e.parentNode.removeChild(e)})}if(e.scroll){let{x:r,y:t}=e.scroll;(0,w.handleSmoothScroll)(()=>{window.scrollTo(r,t)})}}}),(0,y.jsxs)(K,{children:[Q(t,u),(0,y.jsx)(A.Portal,{type:"next-route-announcer",children:(0,y.jsx)(k.RouteAnnouncer,{})})]})]});return!function(e,r){S.ST&&performance.mark(er.beforeRender);let t=r(eo?el:ei);en?(0,g.default.startTransition)(()=>{en.render(t)}):(en=b.default.hydrateRoot(e,t,{onRecoverableError:X.default}),eo=!1)}(i,e=>(0,y.jsx)(es,{callbacks:[e,h],children:(0,y.jsx)(g.default.StrictMode,{children:m})})),p}async function ed(e){if(e.err&&(void 0===e.Component||!e.isHydratePass)){await $(e);return}try{await ec(e)}catch(t){let r=(0,M.getProperError)(t);if(r.cancelled)throw r;await $({...e,err:r})}}async function eu(e){let r=o.err;try{let e=await l.routeLoader.whenEntrypoint("/_app");if("error"in e)throw e.error;let{component:r,exports:t}=e;u=r,t&&t.reportWebVitals&&(f=e=>{let r,{id:n,name:o,startTime:a,value:l,duration:i,entryType:s,entries:c,attribution:d}=e,u=Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12);c&&c.length&&(r=c[0].startTime);let f={id:n||u,name:o,startTime:a||r,value:null==l?i:l,label:"mark"===s||"measure"===s?"custom":"web-vital"};d&&(f.attribution=d),t.reportWebVitals(f)});let n=await l.routeLoader.whenEntrypoint(o.page);if("error"in n)throw n.error;p=n.component}catch(e){r=(0,M.getProperError)(e)}window.__NEXT_PRELOADREADY&&await window.__NEXT_PRELOADREADY(o.dynamicIds),n=(0,L.createRouter)(o.page,o.query,a,{initialProps:o.props,pageLoader:l,App:u,Component:p,wrapApp:Z,err:r,isFallback:!!o.isFallback,subscription:(e,r,t)=>ed(Object.assign({},e,{App:r,scroll:t})),locale:o.locale,locales:o.locales,defaultLocale:h,domainLocales:o.domainLocales,isPreview:o.isPreview}),W=await n._initialMatchesMiddlewarePromise;let t={App:u,initial:!0,Component:p,props:o.props,err:r,isHydratePass:!0};(null==e?void 0:e.beforeRender)&&await e.beforeRender(),ed(t)}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)}}]);