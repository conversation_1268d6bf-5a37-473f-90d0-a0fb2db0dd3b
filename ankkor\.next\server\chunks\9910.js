"use strict";exports.id=9910,exports.ids=[9910],exports.modules={92861:(e,t,a)=>{a.d(t,{Ls:()=>l,ho:()=>d,p_:()=>u,wm:()=>c});var r=a(94868);let o="woo:inventory:mapping:",s=new r.s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),n={};function i(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function d(e,t){try{return i()?(await s.set(`${o}${e}`,t),console.log(`Added WooCommerce mapping to Redis: ${e} -> ${t}`)):(n[e]=t,console.log(`Added WooCommerce mapping to memory: ${e} -> ${t}`)),!0}catch(a){console.error("Error adding WooCommerce inventory mapping:",a);try{return n[e]=t,console.log(`Added WooCommerce mapping to memory fallback: ${e} -> ${t}`),!0}catch(e){return console.error("Error adding to memory fallback:",e),!1}}}async function u(e){try{if(i()){let t=s.pipeline();for(let{productId:a,productSlug:r}of e)t.set(`${o}${a}`,r);await t.exec(),console.log(`Updated ${e.length} WooCommerce inventory mappings in Redis`)}else{for(let{productId:t,productSlug:a}of e)n[t]=a;console.log(`Updated ${e.length} WooCommerce inventory mappings in memory`)}return!0}catch(e){return console.error("Error batch updating WooCommerce inventory mappings:",e),!1}}async function l(){try{if(i()){let e=await s.keys(`${o}*`);if(e.length>0){let t={},a=await s.mget(...e);return e.forEach((e,r)=>{let s=e.replace(o,""),n=a[r];t[s]={wooId:s,inventory:0,sku:"",title:n,lastUpdated:new Date().toISOString()}}),t}}return{}}catch(e){return console.error("Error getting inventory mapping:",e),{}}}async function c(e){try{if(i()){let t=await s.keys(`${o}*`);t.length>0&&await s.del(...t);let a=s.pipeline();for(let[t,r]of Object.entries(e))a.set(`${o}${t}`,r.title||t);return await a.exec(),!0}return!1}catch(e){return console.error("Error updating inventory mapping:",e),!1}}},19910:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.d(t,{CP:()=>g,Co:()=>d,Dg:()=>p,Op:()=>y,V8:()=>$,Xp:()=>n,Zh:()=>f,e_:()=>C,gF:()=>u,s3:()=>h,tG:()=>m});var o=a(93690);a(92861);var s=e([o]);o=(s.then?(await s)():s)[0];let P={storeUrl:"https://maroon-lapwing-781450.hostingersite.com",graphqlUrl:process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",apiVersion:"v1"},b=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",T=new o.GraphQLClient(b,{headers:{"Content-Type":"application/json",Accept:"application/json"}}),S=(0,o.gql)`
  fragment ProductFields on Product {
    id
    databaseId
    name
    slug
    description
    shortDescription
    type
    image {
      sourceUrl
      altText
    }
    galleryImages {
      nodes {
        sourceUrl
        altText
      }
    }
    ... on SimpleProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
    }
    ... on VariableProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      attributes {
        nodes {
          name
          options
        }
      }
    }
  }
`,N=(0,o.gql)`
  fragment VariableProductWithVariations on VariableProduct {
    attributes {
      nodes {
        name
        options
      }
    }
    variations {
      nodes {
        id
        databaseId
        name
        price
        regularPrice
        salePrice
        stockStatus
        stockQuantity
        attributes {
          nodes {
            name
            value
          }
        }
      }
    }
  }
`,v=(0,o.gql)`
  query GetProducts(
    $first: Int
    $after: String
    $where: RootQueryToProductConnectionWhereArgs
  ) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...ProductFields
        ... on VariableProduct {
          ...VariableProductWithVariations
        }
      }
    }
  }
  ${S}
  ${N}
`,q=(0,o.gql)`
  query GetProductBySlug($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
    }
  }
  ${S}
  ${N}
`,w=(0,o.gql)`
  query GetProductBySlugWithTags($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
      productTags {
        nodes {
          id
          name
          slug
        }
      }
      productCategories {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${S}
  ${N}
`;async function n(e={}){try{return(await l(v,{first:e.first||12,after:e.after||null,where:e.where||{}},["products"],60)).products}catch(e){return console.error("Error fetching products:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function i(e){try{let t=(0,o.gql)`
      query GetProductVariations($id: ID!) {
        product(id: $id, idType: DATABASE_ID) {
          ... on VariableProduct {
            variations {
              nodes {
                id
                databaseId
                name
                price
                regularPrice
                salePrice
                stockStatus
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
          }
        }
      }
    `,a=await l(t,{id:e},[`product-${e}`,"products"],60);return a.product?.variations?.nodes||[]}catch(e){return console.error("Error fetching product variations:",e),[]}}async function d(e){try{let t=(await l(q,{slug:e},[`product-${e}`,"products"],60)).product;if(t&&"VARIABLE"===t.type){let e=await i(t.databaseId);return{...t,variations:{nodes:e}}}return t}catch(e){return console.error("Error fetching product by slug:",e),null}}async function u(e){try{return(await l(w,{slug:e},[`product-${e}`,"products"],60)).product}catch(t){return console.error(`Error fetching product with slug ${e}:`,t),null}}async function l(e,t={},a=[],r=60){try{{let o={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t}),next:{}};a&&a.length>0&&(o.next.tags=a),void 0!==r&&(o.next.revalidate=r);let s=await fetch(P.graphqlUrl,o);if(!s.ok)throw Error(`WooCommerce GraphQL API responded with status ${s.status}`);let{data:n,errors:i}=await s.json();if(i)throw console.error("GraphQL Errors:",i),Error(i[0].message);return n}}catch(e){throw console.error("Error fetching from WooCommerce:",e),e}}async function c({query:e,variables:t},a=3,r=1e3){let o=0,s=null;for(;o<a;)try{return await l(e,t,[],0)}catch(e){s=e,++o<a&&(console.log(`Retrying request (${o}/${a}) after ${r}ms`),await new Promise(e=>setTimeout(e,r)),r*=2)}throw console.error(`Failed after ${a} attempts:`,s),s}(0,o.gql)`
  query GetCategories(
    $first: Int
    $after: String
    $where: RootQueryToProductCategoryConnectionWhereArgs
  ) {
    productCategories(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
      }
    }
  }
`;let E=(0,o.gql)`
  query GetAllProducts($first: Int = 20) {
    products(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        productCategories {
          nodes {
            id
            name
            slug
          }
        }
        ... on SimpleProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          variations {
            nodes {
              stockStatus
              stockQuantity
            }
          }
        }
        image {
          id
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            id
            sourceUrl
            altText
          }
        }
        ... on VariableProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
      }
    }
  }
`,U=(0,o.gql)`
  query GetProductsByCategory($slug: ID!, $first: Int = 20) {
    productCategory(id: $slug, idType: SLUG) {
      id
      name
      slug
      description
      products(first: $first) {
        nodes {
          id
          databaseId
          name
          slug
          ... on SimpleProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          ... on VariableProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          image {
            id
            sourceUrl
            altText
          }
        }
      }
    }
  }
`,x=(0,o.gql)`
  query GetAllCategories($first: Int = 20) {
    productCategories(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
        children {
          nodes {
            id
            name
            slug
          }
        }
      }
    }
  }
`;async function p(e=20){try{let t=await c({query:E,variables:{first:e}});return t?.products?.nodes||[]}catch(e){return console.error("Error fetching all products:",e),[]}}async function m(e=20){try{let t=await c({query:x,variables:{first:e}});return t?.productCategories?.nodes||[]}catch(e){return console.error("Error fetching all categories:",e),[]}}async function g(e={}){try{let t=await c({query:x,variables:{first:e.first||20,after:e.after||null,where:e.where||{}}});return{nodes:t.productCategories.nodes,pageInfo:t.productCategories.pageInfo}}catch(e){return console.error("Error fetching categories:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}function y(e){if(!e)return null;let t=!!e.variations?.nodes?.length,a={minVariantPrice:{amount:e.price||"0",currencyCode:"INR"},maxVariantPrice:{amount:e.price||"0",currencyCode:"INR"}};if(t&&e.variations?.nodes?.length>0){let t=e.variations.nodes.map(e=>parseFloat(e.price||"0")).filter(e=>!isNaN(e));t.length>0&&(a={minVariantPrice:{amount:String(Math.min(...t)),currencyCode:"INR"},maxVariantPrice:{amount:String(Math.max(...t)),currencyCode:"INR"}})}let r=function(e){let t=[];return e.image&&t.push({url:e.image.sourceUrl,altText:e.image.altText||e.name||""}),e.galleryImages?.nodes?.length&&e.galleryImages.nodes.forEach(a=>{e.image&&a.sourceUrl===e.image.sourceUrl||t.push({url:a.sourceUrl,altText:a.altText||e.name||""})}),t}(e),o=e.variations?.nodes?.map(e=>({id:e.id,title:e.name,price:{amount:e.price||"0",currencyCode:"USD"},availableForSale:"IN_STOCK"===e.stockStatus,selectedOptions:e.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[],sku:e.sku||"",image:e.image?{url:e.image.sourceUrl,altText:e.image.altText||""}:null}))||[],s=e.attributes?.nodes?.map(e=>({name:e.name,values:e.options||[]}))||[],n=e.productCategories?.nodes?.map(e=>({handle:e.slug,title:e.name}))||[],i={};return e.metafields&&e.metafields.forEach(e=>{i[e.key]=e.value}),{id:e.id,handle:e.slug,title:e.name,description:e.description||"",descriptionHtml:e.description||"",priceRange:a,options:s,variants:o,images:r,collections:n,availableForSale:"OUT_OF_STOCK"!==e.stockStatus,metafields:i,_originalWooProduct:e}}function f(e){return e?{id:e.id,handle:e.slug,title:e.name,description:e.description||"",image:e.image?{url:e.image.sourceUrl,altText:e.image.altText||""}:null,products:e.products?.nodes?.map(y)||[]}:null}async function h(e,t={}){let a="number"==typeof t?t:t.first||10,r=(0,o.gql)`
    query SearchProducts($query: String!, $first: Int) {
      products(first: $first, where: { search: $query }) {
        nodes {
          id
          databaseId
          name
          slug
          price
          image {
            sourceUrl
            altText
          }
          shortDescription
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  `;try{let t=await T.request(r,{query:e,first:a});return t?.products||{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}catch(e){return console.error("Error searching products:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function I(e,t={}){try{let{first:a=20}=t,r=await T.request(U,{slug:e,first:a});return r?.productCategory||null}catch(t){return console.error(`Error fetching category products with slug ${e}:`,t),null}}async function $(e,t={}){let{limit:a=20,page:r=1,sort:o="DATE"}=t,s=r>1?btoa(`arrayconnection:${(r-1)*a-1}`):void 0;return I(e,{first:a,after:s,orderby:o,order:"DESC"})}async function C(e,t={}){let{limit:a=20,page:r=1,sort:o="DATE"}=t;return console.warn(`getProductsByTag called with tag: ${e} - functionality not yet implemented`),{tag:{id:"",name:e,slug:e,description:""},products:[],pageInfo:{hasNextPage:!1,endCursor:null}}}(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
    }
  }
`,(0,o.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
        nicename
        nickname
        username
      }
    }
  }
`,(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`,(0,o.gql)`
  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {
    addToCart(
      input: {
        clientMutationId: "addToCart"
        productId: $productId
        variationId: $variationId
        quantity: $quantity
        extraData: $extraData
      }
    ) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
        contentsCount
      }
    }
  }
`,(0,o.gql)`
  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {
    removeItemsFromCart(input: { keys: $keys, all: $all }) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`,(0,o.gql)`
  query GetShippingMethods {
    shippingMethods {
      nodes {
        id
        title
        description
        cost
      }
    }
  }
`,(0,o.gql)`
  query GetPaymentGateways {
    paymentGateways {
      nodes {
        id
        title
        description
        enabled
      }
    }
  }
`,(0,o.gql)`
  mutation CreateCustomer($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
      }
      authToken
      refreshToken
    }
  }
`,(0,o.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
      customerUserErrors {
        field
        message
      }
    }
  }
`,(0,o.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      displayName
      username
      role
      date
      modified
      isPayingCustomer
      orderCount
      totalSpent
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders(first: 50) {
        nodes {
          id
          databaseId
          date
          status
          total
          subtotal
          totalTax
          shippingTotal
          discountTotal
          paymentMethodTitle
          customerNote
          billing {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
            email
            phone
          }
          shipping {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
          }
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                  slug
                  image {
                    sourceUrl
                    altText
                  }
                }
              }
              variation {
                node {
                  id
                  name
                  attributes {
                    nodes {
                      name
                      value
                    }
                  }
                }
              }
              quantity
              total
              subtotal
              totalTax
            }
          }
          shippingLines {
            nodes {
              methodTitle
              total
            }
          }
          feeLines {
            nodes {
              name
              total
            }
          }
          couponLines {
            nodes {
              code
              discount
            }
          }
        }
      }
      downloadableItems {
        nodes {
          name
          downloadId
          downloadsRemaining
          accessExpires
          product {
            node {
              id
              name
            }
          }
        }
      }
      metaData {
        key
        value
      }
    }
  }
`,(0,o.gql)`
  mutation CreateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation UpdateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation DeleteAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation SetDefaultAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                name
                price
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`,r()}catch(e){r(e)}})}};