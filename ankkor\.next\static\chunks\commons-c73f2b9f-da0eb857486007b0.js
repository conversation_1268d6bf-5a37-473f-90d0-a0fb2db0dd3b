(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6518],{36853:function(e,t,n){!function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){c(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},c=Object.keys(e);for(r=0;r<c.length;r++)n=c[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(e);for(r=0;r<c.length;r++)n=c[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,o=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=o){var c=[],u=!0,i=!1;try{for(o=o.call(e);!(u=(n=o.next()).done)&&(c.push(n.value),!t||c.length!==t);u=!0);}catch(e){i=!0,r=e}finally{try{u||null==o.return||o.return()}finally{if(i)throw r}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var a,l,p,f,d,h={exports:{}};h.exports=(function(){if(d)return f;d=1;var e=p?l:(p=1,l="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,f=function(){function r(t,n,r,o,c,u){if(u!==e){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function o(){return r}r.isRequired=r;var c={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return c.PropTypes=c,c}})()();var m=(a=h.exports)&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a,y=function(e,n,r){var o=!!r,c=t.useRef(r);t.useEffect(function(){c.current=r},[r]),t.useEffect(function(){if(!o||!e)return function(){};var t=function(){c.current&&c.current.apply(c,arguments)};return e.on(n,t),function(){e.off(n,t)}},[o,n,e,c])},g=function(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e},[e]),n.current},v=function(e){return null!==e&&"object"===o(e)},C="[object Object]",E=function e(t,n){if(!v(t)||!v(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var o=Object.prototype.toString.call(t)===C;if(o!==(Object.prototype.toString.call(n)===C))return!1;if(!o&&!r)return t===n;var c=Object.keys(t),u=Object.keys(n);if(c.length!==u.length)return!1;for(var i={},s=0;s<c.length;s+=1)i[c[s]]=!0;for(var a=0;a<u.length;a+=1)i[u[a]]=!0;var l=Object.keys(i);return l.length===c.length&&l.every(function(r){return e(t[r],n[r])})},b=function(e,t,n){return v(e)?Object.keys(e).reduce(function(o,u){var i=!v(t)||!E(e[u],t[u]);return n.includes(u)?(i&&console.warn("Unsupported prop change: options.".concat(u," is not a mutable property.")),o):i?r(r({},o||{}),{},c({},u,e[u])):o},null):null},k="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(null===e||v(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},P=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(v(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return S(e,t)})};var n=S(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},O=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.5.1"}),e.registerAppInfo({name:"react-stripe-js",version:"3.5.1",url:"https://stripe.com/docs/stripe-js/react"}))},w=t.createContext(null);w.displayName="ElementsContext";var j=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},x=function(e){var n=e.stripe,r=e.options,o=e.children,c=t.useMemo(function(){return P(n)},[n]),u=i(t.useState(function(){return{stripe:"sync"===c.tag?c.stripe:null,elements:"sync"===c.tag?c.stripe.elements(r):null}}),2),s=u[0],a=u[1];t.useEffect(function(){var e=!0,t=function(e){a(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==c.tag||s.stripe?"sync"!==c.tag||s.stripe||t(c.stripe):c.stripePromise.then(function(n){n&&e&&t(n)}),function(){e=!1}},[c,s,r]);var l=g(n);t.useEffect(function(){null!==l&&l!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[l,n]);var p=g(r);return t.useEffect(function(){if(s.elements){var e=b(r,p,["clientSecret","fonts"]);e&&s.elements.update(e)}},[r,p,s.elements]),t.useEffect(function(){O(s.stripe)},[s.stripe]),t.createElement(w.Provider,{value:s},o)};x.propTypes={stripe:m.any,options:m.object};var A=function(e){return j(t.useContext(w),e)},R=function(e){return(0,e.children)(A("mounts <ElementsConsumer>"))};R.propTypes={children:m.func.isRequired};var I=["on","session"],N=t.createContext(null);N.displayName="CheckoutSdkContext";var _=function(e,t){if(!e)throw Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e},T=t.createContext(null);T.displayName="CheckoutContext";var Y=function(e,t){if(!e)return null;e.on,e.session;var n=u(e,I);return t?Object.assign(t,n):Object.assign(e.session(),n)},B=function(e){var n=e.stripe,r=e.options,o=e.children,c=t.useMemo(function(){return P(n,"Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),u=i(t.useState(null),2),s=u[0],a=u[1],l=i(t.useState(function(){return{stripe:"sync"===c.tag?c.stripe:null,checkoutSdk:null}}),2),p=l[0],f=l[1],d=function(e,t){f(function(n){return n.stripe&&n.checkoutSdk?n:{stripe:e,checkoutSdk:t}})},h=t.useRef(!1);t.useEffect(function(){var e=!0;return"async"!==c.tag||p.stripe?"sync"===c.tag&&c.stripe&&!h.current&&(h.current=!0,c.stripe.initCheckout(r).then(function(e){e&&(d(c.stripe,e),e.on("change",a))})):c.stripePromise.then(function(t){t&&e&&!h.current&&(h.current=!0,t.initCheckout(r).then(function(e){e&&(d(t,e),e.on("change",a))}))}),function(){e=!1}},[c,p,r,a]);var m=g(n);t.useEffect(function(){null!==m&&m!==n&&console.warn("Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.")},[m,n]);var y=g(r);t.useEffect(function(){if(p.checkoutSdk){var e,t,n=null==y?void 0:null===(e=y.elementsOptions)||void 0===e?void 0:e.appearance,o=null==r?void 0:null===(t=r.elementsOptions)||void 0===t?void 0:t.appearance;o&&!E(o,n)&&p.checkoutSdk.changeAppearance(o)}},[r,y,p.checkoutSdk]),t.useEffect(function(){O(p.stripe)},[p.stripe]);var v=t.useMemo(function(){return Y(p.checkoutSdk,s)},[p.checkoutSdk,s]);return p.checkoutSdk?t.createElement(N.Provider,{value:p},t.createElement(T.Provider,{value:v},o)):null};B.propTypes={stripe:m.any,options:m.shape({fetchClientSecret:m.func.isRequired,elementsOptions:m.object}).isRequired};var U=function(e){var n=t.useContext(N),r=t.useContext(w);if(n&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return n?_(n,e):j(r,e)},M=["mode"],L=function(e,n){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),o=n?function(e){U("mounts <".concat(r,">"));var n=e.id,o=e.className;return t.createElement("div",{id:n,className:o})}:function(n){var o,c=n.id,s=n.className,a=n.options,l=void 0===a?{}:a,p=n.onBlur,f=n.onFocus,d=n.onReady,h=n.onChange,m=n.onEscape,v=n.onClick,C=n.onLoadError,E=n.onLoaderStart,k=n.onNetworksChange,S=n.onConfirm,P=n.onCancel,O=n.onShippingAddressChange,w=n.onShippingRateChange,j=U("mounts <".concat(r,">")),x="elements"in j?j.elements:null,A="checkoutSdk"in j?j.checkoutSdk:null,R=i(t.useState(null),2),I=R[0],N=R[1],_=t.useRef(null),T=t.useRef(null);y(I,"blur",p),y(I,"focus",f),y(I,"escape",m),y(I,"click",v),y(I,"loaderror",C),y(I,"loaderstart",E),y(I,"networkschange",k),y(I,"confirm",S),y(I,"cancel",P),y(I,"shippingaddresschange",O),y(I,"shippingratechange",w),y(I,"change",h),d&&(o="expressCheckout"===e?d:function(){d(I)}),y(I,"ready",o),t.useLayoutEffect(function(){if(null===_.current&&null!==T.current&&(x||A)){var t=null;if(A)switch(e){case"payment":t=A.createPaymentElement(l);break;case"address":if("mode"in l){var n=l.mode,o=u(l,M);if("shipping"===n)t=A.createShippingAddressElement(o);else if("billing"===n)t=A.createBillingAddressElement(o);else throw Error("Invalid options.mode. mode must be 'billing' or 'shipping'.")}else throw Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");break;case"expressCheckout":t=A.createExpressCheckoutElement(l);break;case"currencySelector":t=A.createCurrencySelectorElement();break;default:throw Error("Invalid Element type ".concat(r,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else x&&(t=x.create(e,l));_.current=t,N(t),t&&t.mount(T.current)}},[x,A,l]);var Y=g(l);return t.useEffect(function(){if(_.current){var e=b(l,Y,["paymentRequest"]);e&&"update"in _.current&&_.current.update(e)}},[l,Y]),t.useLayoutEffect(function(){return function(){if(_.current&&"function"==typeof _.current.destroy)try{_.current.destroy(),_.current=null}catch(e){}}},[]),t.createElement("div",{id:c,className:s,ref:T})};return o.propTypes={id:m.string,className:m.string,onChange:m.func,onBlur:m.func,onFocus:m.func,onReady:m.func,onEscape:m.func,onClick:m.func,onLoadError:m.func,onLoaderStart:m.func,onNetworksChange:m.func,onConfirm:m.func,onCancel:m.func,onShippingAddressChange:m.func,onShippingRateChange:m.func,options:m.object},o.displayName=r,o.__elementType=e,o},D="undefined"==typeof window,W=t.createContext(null);W.displayName="EmbeddedCheckoutProviderContext";var q=function(){var e=t.useContext(W);if(!e)throw Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},F=D?function(e){var n=e.id,r=e.className;return q(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,o=q().embeddedCheckout,c=t.useRef(!1),u=t.useRef(null);return t.useLayoutEffect(function(){return!c.current&&o&&null!==u.current&&(o.mount(u.current),c.current=!0),function(){if(c.current&&o)try{o.unmount(),c.current=!1}catch(e){}}},[o]),t.createElement("div",{ref:u,id:n,className:r})},H=L("auBankAccount",D),V=L("card",D),$=L("cardNumber",D),z=L("cardExpiry",D),G=L("cardCvc",D),J=L("fpxBank",D),K=L("iban",D),Q=L("idealBank",D),X=L("p24Bank",D),Z=L("epsBank",D),ee=L("payment",D),et=L("expressCheckout",D),en=L("currencySelector",D),er=L("paymentRequestButton",D),eo=L("linkAuthentication",D),ec=L("address",D),eu=L("shippingAddress",D),ei=L("paymentMethodMessaging",D),es=L("affirmMessage",D),ea=L("afterpayClearpayMessage",D);e.AddressElement=ec,e.AffirmMessageElement=es,e.AfterpayClearpayMessageElement=ea,e.AuBankAccountElement=H,e.CardCvcElement=G,e.CardElement=V,e.CardExpiryElement=z,e.CardNumberElement=$,e.CheckoutProvider=B,e.CurrencySelectorElement=en,e.Elements=x,e.ElementsConsumer=R,e.EmbeddedCheckout=F,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,o=e.children,c=t.useMemo(function(){return P(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),u=t.useRef(null),s=t.useRef(null),a=i(t.useState({embeddedCheckout:null}),2),l=a[0],p=a[1];t.useEffect(function(){if(!s.current&&!u.current){var e=function(e){s.current||u.current||(s.current=e,u.current=s.current.initEmbeddedCheckout(r).then(function(e){p({embeddedCheckout:e})}))};"async"===c.tag&&!s.current&&(r.clientSecret||r.fetchClientSecret)?c.stripePromise.then(function(t){t&&e(t)}):"sync"===c.tag&&!s.current&&(r.clientSecret||r.fetchClientSecret)&&e(c.stripe)}},[c,r,l,s]),t.useEffect(function(){return function(){l.embeddedCheckout?(u.current=null,l.embeddedCheckout.destroy()):u.current&&u.current.then(function(){u.current=null,l.embeddedCheckout&&l.embeddedCheckout.destroy()})}},[l.embeddedCheckout]),t.useEffect(function(){O(s)},[s]);var f=g(n);t.useEffect(function(){null!==f&&f!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[f,n]);var d=g(r);return t.useEffect(function(){if(null!=d){if(null==r){console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.");return}void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=d.clientSecret&&r.clientSecret!==d.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=d.fetchClientSecret&&r.fetchClientSecret!==d.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=d.onComplete&&r.onComplete!==d.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=d.onShippingDetailsChange&&r.onShippingDetailsChange!==d.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=d.onLineItemsChange&&r.onLineItemsChange!==d.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")}},[d,r]),t.createElement(W.Provider,{value:l},o)},e.EpsBankElement=Z,e.ExpressCheckoutElement=et,e.FpxBankElement=J,e.IbanElement=K,e.IdealBankElement=Q,e.LinkAuthenticationElement=eo,e.P24BankElement=X,e.PaymentElement=ee,e.PaymentMethodMessagingElement=ei,e.PaymentRequestButtonElement=er,e.ShippingAddressElement=eu,e.useCheckout=function(){e="calls useCheckout()",_(t.useContext(N),e);var e,n=t.useContext(T);if(!n)throw Error("Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.");return n},e.useElements=function(){return A("calls useElements()").elements},e.useStripe=function(){return U("calls useStripe()").stripe}}(t,n(2265))}}]);