"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9971],{82458:function(t,e,i){i.d(e,{yV:function(){return k}});var s=i(34081),o=i(19510),a=i(72644),r=i(47227),n=i(25549),h=i(21478),l=i(96674),d=i(79573),u=i(72728),c=i(29818),p=i(98639),m=i(62734),y=i(65255),g=i(12787),f=i(44023),v=i(84364),T=i(50215),D=i(2227),P=i(38090),j=i(34277),x=i(94239),S=i(43289),R=i(59111),B=i(58345),A=i(44439);let V=["","X","Y","Z"],L={visibility:"hidden"},O=0,U={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function k({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:y,resetTransform:P}){return class{constructor(t={},i=null==e?void 0:e()){this.id=O++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,U.totalNodes=U.resolvedTargetDeltas=U.recalculatedProjection=0,this.nodes.forEach(C),this.nodes.forEach(W),this.nodes.forEach($),this.nodes.forEach(w),(0,j.I)(U)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new f.E)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new s.L),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=(0,x.v)(e),this.instance=e;let{layoutId:s,layout:o,visualElement:a}=this.options;if(a&&!a.current&&a.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(o||s)&&(this.isLayoutDirty=!0),t){let i;let s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=(0,D.g)(s,250),T.V.hasAnimatedSinceResize&&(T.V.hasAnimatedSinceResize=!1,this.nodes.forEach(z))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&a&&(s||o)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||a.getDefaultTransition()||Z,{onLayoutAnimationStart:r,onLayoutAnimationComplete:n}=a.getProps(),h=!this.targetLayout||!(0,u.Rm)(this.targetLayout,s)||i,l=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||l||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,l);let e={...(0,d.e)(o,"layout"),onPlay:r,onComplete:n};(a.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||z(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,B.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(H),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(M);return}this.isUpdating||this.nodes.forEach(N),this.isUpdating=!1,this.nodes.forEach(I),this.nodes.forEach(E),this.nodes.forEach(F),this.clearAllSnapshots();let t=performance.now();B.frameData.delta=(0,R.u)(0,1e3/60,t-B.frameData.timestamp),B.frameData.timestamp=t,B.frameData.isProcessing=!0,B.S6.update.process(B.frameData),B.S6.preRender.process(B.frameData),B.S6.render.process(B.frameData),B.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(b),this.sharedNodes.forEach(_)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,B.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){B.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,l.dO)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:y(this.instance),offset:i(this.instance)})}resetTransform(){if(!P)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!(0,u.BS)(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,o=s!==this.prevTransformTemplateValue;t&&(e||(0,g.ud)(this.latestValues)||o)&&(P(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),K((e=s).x),K(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,l.dO)();let e=t.measureViewportBox(),{scroll:i}=this.root;return i&&((0,r.am)(e.x,i.offset.x),(0,r.am)(e.y,i.offset.y)),e}removeElementScroll(t){let e=(0,l.dO)();(0,a.j)(e,t);for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:o,options:n}=s;if(s!==this.root&&o&&n.layoutScroll){if(o.isRoot){(0,a.j)(e,t);let{scroll:i}=this.root;i&&((0,r.am)(e.x,-i.offset.x),(0,r.am)(e.y,-i.offset.y))}(0,r.am)(e.x,o.offset.x),(0,r.am)(e.y,o.offset.y)}}return e}applyTransform(t,e=!1){let i=(0,l.dO)();(0,a.j)(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&(0,r.D2)(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),(0,g.ud)(s.latestValues)&&(0,r.D2)(i,s.latestValues)}return(0,g.ud)(this.latestValues)&&(0,r.D2)(i,this.latestValues),i}removeTransform(t){let e=(0,l.dO)();(0,a.j)(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,g.ud)(i.latestValues))continue;(0,g.Lj)(i.latestValues)&&i.updateSnapshot();let s=(0,l.dO)(),o=i.measurePageBox();(0,a.j)(s,o),(0,h.mg)(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return(0,g.ud)(this.latestValues)&&(0,h.mg)(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==B.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;let i=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=i.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=i.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=i.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==i;if(!(t||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:o,layoutId:h}=this.options;if(this.layout&&(o||h)){if(this.resolvedRelativeTargetAt=B.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,l.dO)(),this.relativeTargetOrigin=(0,l.dO)(),(0,n.b3)(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),(0,a.j)(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,l.dO)(),this.targetWithTransforms=(0,l.dO)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,n.tf)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):(0,a.j)(this.target,this.layout.layoutBox),(0,r.o2)(this.target,this.targetDelta)):(0,a.j)(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,l.dO)(),this.relativeTargetOrigin=(0,l.dO)(),(0,n.b3)(this.relativeTargetOrigin,this.target,t.target),(0,a.j)(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}U.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||(0,g.Lj)(this.parent.latestValues)||(0,g.D_)(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===B.frameData.timestamp&&(s=!1),s)return;let{layout:o,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(o||h))return;(0,a.j)(this.layoutCorrected,this.layout.layoutBox);let d=this.treeScale.x,u=this.treeScale.y;(0,r.YY)(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:c}=e;if(!c){this.projectionTransform&&(this.projectionDelta=(0,l.wc)(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=(0,l.wc)(),this.projectionDeltaWithTransform=(0,l.wc)());let p=this.projectionTransform;(0,n.y$)(this.projectionDelta,this.layoutCorrected,c,this.latestValues),this.projectionTransform=(0,m.d)(this.projectionDelta,this.treeScale),(this.projectionTransform!==p||this.treeScale.x!==d||this.treeScale.y!==u)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",c)),U.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let i;let s=this.snapshot,r=s?s.latestValues:{},h={...this.latestValues},d=(0,l.wc)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let c=(0,l.dO)(),p=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),m=this.getStack(),y=!m||m.members.length<=1,g=!!(p&&!y&&!0===this.options.crossfade&&!this.path.some(G));this.animationProgress=0,this.mixTargetDelta=e=>{var s,m;let f=e/1e3;J(d.x,t.x,f),J(d.y,t.y,f),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&((0,n.b3)(c,this.layout.layoutBox,this.relativeParent.layout.layoutBox),s=this.relativeTarget,m=this.relativeTargetOrigin,Y(s.x,m.x,c.x,f),Y(s.y,m.y,c.y,f),i&&(0,u.RD)(this.relativeTarget,i)&&(this.isProjectionDirty=!1),i||(i=(0,l.dO)()),(0,a.j)(i,this.relativeTarget)),p&&(this.animationValues=h,(0,o.V)(h,r,this.latestValues,f,g,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=f},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,B.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=B.Wi.update(()=>{T.V.hasAnimatedSinceResize=!0,this.currentAnimation=(0,S.D)(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:o}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&Q(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||(0,l.dO)();let e=(0,n.JO)(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=(0,n.JO)(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}(0,a.j)(e,i),(0,r.D2)(e,o),(0,n.y$)(this.projectionDeltaWithTransform,this.layoutCorrected,e,o)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new c.t),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.rotate||i.rotateX||i.rotateY||i.rotateZ)&&(e=!0),!e)return;let s={};for(let e=0;e<V.length;e++){let o="rotate"+V[e];i[o]&&(s[o]=i[o],t.setStaticValue(o,0))}for(let e in t.render(),s)t.setStaticValue(e,s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return L;let s={visibility:""},o=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=(0,v.b)(null==t?void 0:t.pointerEvents)||"",s.transform=o?o(this.latestValues,""):"none",s;let a=this.getLead();if(!this.projectionDelta||!this.layout||!a.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=(0,v.b)(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!(0,g.ud)(this.latestValues)&&(e.transform=o?o({},""):"none",this.hasProjected=!1),e}let r=a.animationValues||a.latestValues;this.applyTransformsToTarget(),s.transform=(0,m.d)(this.projectionDeltaWithTransform,this.treeScale,r),o&&(s.transform=o(r,s.transform));let{x:n,y:h}=this.projectionDelta;for(let t in s.transformOrigin=`${100*n.origin}% ${100*h.origin}% 0`,a.animationValues?s.opacity=a===this?null!==(i=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:s.opacity=a===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,p.P){if(void 0===r[t])continue;let{correct:e,applyTo:i}=p.P[t],o="none"===s.transform?r[t]:e(r[t],a);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=o}else s[t]=o}return this.options.layoutId&&(s.pointerEvents=a===this?(0,v.b)(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(M),this.root.sharedNodes.clear()}}}function E(t){t.updateLayout()}function F(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:o}=t.options,a=i.source!==t.layout.source;"size"===o?(0,y.U)(t=>{let s=a?i.measuredBox[t]:i.layoutBox[t],o=(0,n.JO)(s);s.min=e[t].min,s.max=s.min+o}):Q(o,i.layoutBox,e)&&(0,y.U)(s=>{let o=a?i.measuredBox[s]:i.layoutBox[s],r=(0,n.JO)(e[s]);o.max=o.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+r)});let r=(0,l.wc)();(0,n.y$)(r,e,i.layoutBox);let h=(0,l.wc)();a?(0,n.y$)(h,t.applyTransform(s,!0),i.measuredBox):(0,n.y$)(h,e,i.layoutBox);let d=!(0,u.BS)(r),c=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:o,layout:a}=s;if(o&&a){let r=(0,l.dO)();(0,n.b3)(r,i.layoutBox,o.layoutBox);let h=(0,l.dO)();(0,n.b3)(h,e,a.layoutBox),(0,u.Rm)(r,h)||(c=!0),s.options.layoutRoot&&(t.relativeTarget=h,t.relativeTargetOrigin=r,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:h,layoutDelta:r,hasLayoutChanged:d,hasRelativeTargetChanged:c})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function C(t){U.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function w(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function b(t){t.clearSnapshot()}function M(t){t.clearMeasurements()}function N(t){t.isLayoutDirty=!1}function I(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function z(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function W(t){t.resolveTargetDelta()}function $(t){t.calcProjection()}function H(t){t.resetRotation()}function _(t){t.removeLeadSnapshot()}function J(t,e,i){t.translate=(0,P.C)(e.translate,0,i),t.scale=(0,P.C)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function Y(t,e,i,s){t.min=(0,P.C)(e.min,i.min,s),t.max=(0,P.C)(e.max,i.max,s)}function G(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let Z={duration:.45,ease:[.4,0,.1,1]},X=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),q=X("applewebkit/")&&!X("chrome/")?Math.round:A.Z;function K(t){t.min=q(t.min),t.max=q(t.max)}function Q(t,e,i){return"position"===t||"preserve-aspect"===t&&!(0,n.wS)((0,u.Dm)(e),(0,u.Dm)(i),.2)}}}]);