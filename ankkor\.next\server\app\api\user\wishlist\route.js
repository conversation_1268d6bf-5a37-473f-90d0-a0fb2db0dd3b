"use strict";(()=>{var e={};e.id=8354,e.ids=[8354],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},86826:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>w,patchFetch:()=>R,requestAsyncStorage:()=>h,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>f});var o=r(49303),n=r(88716),a=r(60670),i=r(87070),c=r(71615),l=r(70591),u=r(79534);async function d(e){try{let e;let t=(0,c.cookies)().get("woo_auth_token");if(!t||!t.value)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});try{let r=(0,l.o)(t.value),s=Date.now()/1e3;if(r.exp<s)return i.NextResponse.json({success:!1,message:"Token expired"},{status:401});e=r.data.user.id}catch(e){return i.NextResponse.json({success:!1,message:"Invalid token"},{status:401})}let r=[];if((0,u.Pc)())try{r=await (0,u.U2)(`wishlist:${e}`)||[]}catch(e){console.error("Error fetching wishlist from Redis:",e)}return i.NextResponse.json({success:!0,wishlist:r})}catch(e){return console.error("Error fetching wishlist:",e),i.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}async function f(e){try{let t;let r=(0,c.cookies)().get("woo_auth_token");if(!r||!r.value)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});try{let e=(0,l.o)(r.value),s=Date.now()/1e3;if(e.exp<s)return i.NextResponse.json({success:!1,message:"Token expired"},{status:401});t=e.data.user.id}catch(e){return i.NextResponse.json({success:!1,message:"Invalid token"},{status:401})}let{wishlist:s}=await e.json();if(!Array.isArray(s))return i.NextResponse.json({success:!1,message:"Invalid wishlist data"},{status:400});if(!(0,u.Pc)())return i.NextResponse.json({success:!1,message:"Storage service unavailable"},{status:503});try{return await (0,u.t8)(`wishlist:${t}`,s,4*u.mJ.WEEK),i.NextResponse.json({success:!0,message:"Wishlist saved successfully"})}catch(e){return console.error("Error saving wishlist to Redis:",e),i.NextResponse.json({success:!1,message:"Failed to save wishlist"},{status:500})}}catch(e){return console.error("Error saving wishlist:",e),i.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/user/wishlist/route",pathname:"/api/user/wishlist",filename:"route",bundlePath:"app/api/user/wishlist/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\user\\wishlist\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:y,serverHooks:g}=p,w="/api/user/wishlist/route";function R(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}},71615:(e,t,r)=>{var s=r(88757);r.o(s,"cookies")&&r.d(t,{cookies:function(){return s.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return n}});let s=r(45869),o=r(6278);class n{get isEnabled(){return this._provider.isEnabled}enable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return f},draftMode:function(){return p},headers:function(){return d}});let s=r(68996),o=r(53047),n=r(92044),a=r(72934),i=r(33085),c=r(6278),l=r(45869),u=r(54580);function d(){let e="headers",t=l.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return o.HeadersAdapter.seal(new Headers({}));(0,c.trackDynamicDataAccessed)(t,e)}return(0,u.getExpectedRequestStore)(e).headers}function f(){let e="cookies",t=l.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.RequestCookiesAdapter.seal(new n.RequestCookies(new Headers({})));(0,c.trackDynamicDataAccessed)(t,e)}let r=(0,u.getExpectedRequestStore)(e),o=a.actionAsyncStorage.getStore();return(null==o?void 0:o.isAction)||(null==o?void 0:o.isAppRoute)?r.mutableCookies:r.cookies}function p(){let e=(0,u.getExpectedRequestStore)("draftMode");return new i.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return n},ReadonlyHeadersError:function(){return o}});let s=r(38238);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class n extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return s.ReflectAdapter.get(t,r,o);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);if(void 0!==a)return s.ReflectAdapter.get(t,a,o)},set(t,r,o,n){if("symbol"==typeof r)return s.ReflectAdapter.set(t,r,o,n);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return s.ReflectAdapter.set(t,i??r,o,n)},has(t,r){if("symbol"==typeof r)return s.ReflectAdapter.has(t,r);let o=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==n&&s.ReflectAdapter.has(t,n)},deleteProperty(t,r){if("symbol"==typeof r)return s.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===n||s.ReflectAdapter.deleteProperty(t,n)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new n(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return u},getModifiedCookieValues:function(){return l}});let s=r(92044),o=r(38238),n=r(45869);class a extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new a}}class i{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");function l(e){let t=e[c];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=l(t);if(0===r.length)return!1;let o=new s.ResponseCookies(e),n=o.getAll();for(let e of r)o.set(e);for(let e of n)o.set(e);return!0}class d{static wrap(e,t){let r=new s.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],i=new Set,l=()=>{let e=n.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of a){let r=new s.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case c:return a;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{l()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{l()}};default:return o.ReflectAdapter.get(e,t,r)}}})}}},79534:(e,t,r)=>{r.d(t,{Fs:()=>l,IV:()=>c,Pc:()=>n,U2:()=>a,mJ:()=>o,t8:()=>i});let s=new(r(94868)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),o={SHORT:300,MEDIUM:3600,LONG:86400,WEEK:604800};function n(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function a(e){if(!n())return null;try{return await s.get(e)||null}catch(t){return console.error(`Redis get error for key ${e}:`,t),null}}async function i(e,t,r){if(!n())return!1;try{return r?await s.setex(e,r,t):await s.set(e,t),!0}catch(t){return console.error(`Redis set error for key ${e}:`,t),!1}}async function c(e){if(!n())return!1;try{return await s.del(e),!0}catch(t){return console.error(`Redis delete error for key ${e}:`,t),!1}}async function l(e,t,r=o.MEDIUM){if(!n())return await t();try{let o=await s.get(e);if(null!==o)return console.log(`Cache hit for key: ${e}`),JSON.parse(o);console.log(`Cache miss for key: ${e}, fetching fresh data`);let n=await t();return await s.setex(e,r,JSON.stringify(n)),n}catch(r){return console.error(`Redis cache error for key ${e}:`,r),await t()}}},70591:(e,t,r)=>{r.d(t,{o:()=>o});class s extends Error{}function o(e,t){let r;if("string"!=typeof e)throw new s("Invalid token specified: must be a string");t||(t={});let o=!0===t.header?0:1,n=e.split(".")[o];if("string"!=typeof n)throw new s(`Invalid token specified: missing part #${o+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(n)}catch(e){throw new s(`Invalid token specified: invalid base64 for part #${o+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new s(`Invalid token specified: invalid json for part #${o+1} (${e.message})`)}}s.prototype.name="InvalidTokenError"}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,4766,4868],()=>r(86826));module.exports=s})();