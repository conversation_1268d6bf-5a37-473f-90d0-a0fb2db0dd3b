"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5302],{68123:function(n,e,t){async function s(n){try{let e=await fetch("/api/auth/update-profile",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(n)}),t=await e.json();if(!e.ok||!t.success)throw Error(t.message||"Profile update failed");return{customer:t.customer,accessToken:"token_managed_by_server",expiresAt:new Date(Date.now()+864e5).toISOString()}}catch(n){throw console.error("Error updating customer profile:",n),n}}t.d(e,{lG:function(){return s}}),t(82372)},77690:function(n,e,t){t.d(e,{kS:function(){return y},ts:function(){return w},x4:function(){return h},z2:function(){return g}});var s=t(45008),o=t(34206);function r(){let n=(0,s._)(['\n  mutation LoginUser($username: String!, $password: String!) {\n    login(input: {\n      clientMutationId: "login"\n      username: $username\n      password: $password\n    }) {\n      authToken\n      refreshToken\n      user {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n']);return r=function(){return n},n}function a(){let n=(0,s._)(["\n  mutation RegisterUser($input: RegisterCustomerInput!) {\n    registerCustomer(input: $input) {\n      clientMutationId\n      authToken\n      refreshToken\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n"]);return a=function(){return n},n}function i(){let n=(0,s._)(["\n  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {\n    refreshJwtAuthToken(input: $input) {\n      authToken\n    }\n  }\n"]);return i=function(){return n},n}function u(){let n=(0,s._)(["\n  query GetCustomer {\n    customer {\n      id\n      databaseId\n      email\n      firstName\n      lastName\n      billing {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n        email\n        phone\n      }\n      shipping {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n      }\n      orders {\n        nodes {\n          id\n          databaseId\n          date\n          status\n          total\n          lineItems {\n            nodes {\n              product {\n                node {\n                  id\n                  name\n                }\n              }\n              quantity\n              total\n            }\n          }\n        }\n      }\n    }\n  }\n"]);return u=function(){return n},n}function c(){let n=(0,s._)(["\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      clientMutationId\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return c=function(){return n},n}t(14474);let l="woo_auth_token",d="woo_refresh_token";(0,o.Ps)(r()),(0,o.Ps)(a()),(0,o.Ps)(i()),(0,o.Ps)(u()),(0,o.Ps)(c());let f="https://maroon-lapwing-781450.hostingersite.com/graphql",p=f&&!f.startsWith("http")?"https://".concat(f):f;function m(n){"undefined"!=typeof document&&(document.cookie="".concat(n,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;SameSite=Lax"))}async function h(n,e){try{let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:n,password:e}),credentials:"include"});if(!t.ok){let n=await t.json();throw Error(n.message||"Login failed")}let s=await t.json();if(s.success&&s.user)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Login successful, user data received"),{success:!0,user:s.user,token:s.token};throw console.error("Login response missing user data"),Error("Login failed: Invalid response from server")}catch(n){return console.error("Login error:",n),{success:!1,message:n.message||"Login failed"}}}async function g(n,e,t,s){try{let o=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:n,firstName:e,lastName:t,password:s}),credentials:"include"});if(!o.ok){let n=await o.json();throw Error(n.message||"Registration failed")}let r=await o.json();if(r.success&&r.customer)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Registration successful, user data received"),{success:!0,customer:r.customer,token:r.token};throw console.error("Registration response missing customer data"),Error("Registration failed: Invalid response from server")}catch(n){return console.error("Registration error:",n),{success:!1,message:n.message||"Registration failed"}}}async function y(){try{return await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"logout"}),credentials:"include"}),"undefined"!=typeof localStorage&&localStorage.removeItem("auth_session_started"),m(l),m(d),console.log("Logout successful, session cleared"),{success:!0}}catch(n){return console.error("Logout error:",n),"undefined"!=typeof localStorage&&localStorage.removeItem("auth_session_started"),m(l),m(d),{success:!0}}}async function w(){try{let n=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===n.status)return null;let e=await n.json();if(!n.ok||!e.success)return null;return e.user}catch(n){return console.error("Get user error:",n),null}}new o.g6(p,{headers:{"Content-Type":"application/json"}})},70597:function(n,e,t){t.d(e,{EJ:function(){return o},J6:function(){return s}});let s="₹",o="INR"}}]);