"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6613],{54593:function(e,t,n){n.d(t,{f:function(){return l}});var r=n(84325),a=n(44439);function l({keyframes:e,delay:t,onUpdate:n,onComplete:l}){let i=()=>(n&&n(e[e.length-1]),l&&l(),{time:0,speed:1,duration:0,play:a.Z,pause:a.Z,stop:a.Z,then:e=>(e(),Promise.resolve()),cancel:a.Z,complete:a.Z});return t?(0,r.y)({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}},84325:function(e,t,n){n.d(t,{y:function(){return m}});var r=n(18794),a=n(84962),l=n(4293),i=n(58345);let u=e=>{let t=({timestamp:t})=>e(t);return{start:()=>i.Wi.update(t,!0),stop:()=>(0,i.Pn)(t),now:()=>i.frameData.isProcessing?i.frameData.timestamp:performance.now()}};var o=n(22888),s=n(59111),c=n(56717),p=n(30431);let f={decay:l.I,inertia:l.I,tween:r.F,keyframes:r.F,spring:a.S};function m({autoplay:e=!0,delay:t=0,driver:n=u,keyframes:a,type:l="keyframes",repeat:i=0,repeatDelay:m=0,repeatType:d="loop",onPlay:y,onStop:h,onComplete:v,onUpdate:g,...k}){let w,b,P,A,D,M=1,I=!1,O=()=>{b=new Promise(e=>{w=e})};O();let X=f[l]||r.F;X!==r.F&&"number"!=typeof a[0]&&(A=(0,o.s)([0,100],a,{clamp:!1}),a=[0,100]);let Z=X({...k,keyframes:a});"mirror"===d&&(D=X({...k,keyframes:[...a].reverse(),velocity:-(k.velocity||0)}));let C="idle",F=null,T=null,$=null;null===Z.calculatedDuration&&i&&(Z.calculatedDuration=(0,p.i)(Z));let{calculatedDuration:_}=Z,E=1/0,S=1/0;null!==_&&(S=(E=_+m)*(i+1)-m);let W=0,q=e=>{if(null===T)return;M>0&&(T=Math.min(T,e)),M<0&&(T=Math.min(e-S/M,T));let n=(W=null!==F?F:Math.round(e-T)*M)-t*(M>=0?1:-1),r=M>=0?n<0:n>S;W=Math.max(n,0),"finished"===C&&null===F&&(W=S);let l=W,u=Z;if(i){let e=Math.min(W,S)/E,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,i+1))%2&&("reverse"===d?(n=1-n,m&&(n-=m/E)):"mirror"===d&&(u=D)),l=(0,s.u)(0,1,n)*E}let o=r?{done:!1,value:a[0]}:u.next(l);A&&(o.value=A(o.value));let{done:c}=o;r||null===_||(c=M>=0?W>=S:W<=0);let p=null===F&&("finished"===C||"running"===C&&c);return g&&g(o.value),p&&R(),o},x=()=>{P&&P.stop(),P=void 0},N=()=>{C="idle",x(),w(),O(),T=$=null},R=()=>{C="finished",v&&v(),x(),w()},j=()=>{if(I)return;P||(P=n(q));let e=P.now();y&&y(),null!==F?T=e-F:T&&"finished"!==C||(T=e),"finished"===C&&O(),$=T,F=null,C="running",P.start()};e&&j();let z={then:(e,t)=>b.then(e,t),get time(){return(0,c.X)(W)},set time(newTime){W=newTime=(0,c.w)(newTime),null===F&&P&&0!==M?T=P.now()-newTime/M:F=newTime},get duration(){let e=null===Z.calculatedDuration?(0,p.i)(Z):Z.calculatedDuration;return(0,c.X)(e)},get speed(){return M},set speed(newSpeed){if(newSpeed===M||!P)return;M=newSpeed,z.time=(0,c.X)(W)},get state(){return C},play:j,pause:()=>{C="paused",F=W},stop:()=>{I=!0,"idle"!==C&&(C="idle",h&&h(),N())},cancel:()=>{null!==$&&q($),N()},complete:()=>{C="finished"},sample:e=>(T=0,q(e))};return z}},44046:function(e,t,n){n.d(t,{P:function(){return d}});var r=n(56202);let a=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,l={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:a([0,.65,.55,1]),circOut:a([.55,0,1,.45]),backIn:a([.31,.01,.66,-.59]),backOut:a([.33,1.53,.69,.99])};var i=n(84325),u=n(56717),o=n(64043),s=n(44439),c=n(58345);let p=(0,o.X)(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),f=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),m=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&l[t]||(0,r.q)(t)||Array.isArray(t)&&t.every(e))}(t.ease);function d(e,t,{onUpdate:n,onComplete:o,...d}){let y,h;if(!(p()&&f.has(t)&&!d.repeatDelay&&"mirror"!==d.repeatType&&0!==d.damping&&"inertia"!==d.type))return!1;let v=!1,g=!1,k=()=>{h=new Promise(e=>{y=e})};k();let{keyframes:w,duration:b=300,ease:P,times:A}=d;if(m(t,d)){let e=(0,i.y)({...d,repeat:0,delay:0}),t={done:!1,value:w[0]},n=[],r=0;for(;!t.done&&r<2e4;)t=e.sample(r),n.push(t.value),r+=10;A=void 0,w=n,b=r-10,P="linear"}let D=function(e,t,n,{delay:i=0,duration:u,repeat:o=0,repeatType:s="loop",ease:c,times:p}={}){let f={[t]:n};p&&(f.offset=p);let m=function e(t){if(t)return(0,r.q)(t)?a(t):Array.isArray(t)?t.map(e):l[t]}(c);return Array.isArray(m)&&(f.easing=m),e.animate(f,{delay:i,duration:u,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(e.owner.current,t,w,{...d,duration:b,ease:P,times:A}),M=()=>{g=!1,D.cancel()},I=()=>{g=!0,c.Wi.update(M),y(),k()};return D.onfinish=()=>{g||(e.set(function(e,{repeat:t,repeatType:n="loop"}){let r=t&&"loop"!==n&&t%2==1?0:e.length-1;return e[r]}(w,d)),o&&o(),I())},{then:(e,t)=>h.then(e,t),attachTimeline:e=>(D.timeline=e,D.onfinish=null,s.Z),get time(){return(0,u.X)(D.currentTime||0)},set time(newTime){D.currentTime=(0,u.w)(newTime)},get speed(){return D.playbackRate},set speed(newSpeed){D.playbackRate=newSpeed},get duration(){return(0,u.X)(b)},play:()=>{v||(D.play(),(0,c.Pn)(M))},pause:()=>D.pause(),stop:()=>{if(v=!0,"idle"===D.playState)return;let{currentTime:t}=D;if(t){let n=(0,i.y)({...d,autoplay:!1});e.setWithVelocity(n.sample(t-10).value,n.sample(t).value,10)}I()},complete:()=>{g||D.finish()},cancel:I}}}}]);