(()=>{var e={};e.id=4335,e.ids=[4335],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},96729:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>c.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>i}),r(22332),r(41783),r(12523);var o=r(23191),s=r(88716),a=r(37922),c=r.n(a),n=r(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let i=["",{children:["woocommerce-checkout-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,22332)),"E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-checkout-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,41783)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-checkout-test\\page.tsx"],u="/woocommerce-checkout-test/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/woocommerce-checkout-test/page",pathname:"/woocommerce-checkout-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},67682:(e,t,r)=>{Promise.resolve().then(r.bind(r,73440))},73440:(e,t,r)=>{"use strict";r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{default:()=>l});var s=r(10326),a=r(17577),c=r(15725),n=e([c]);function l(){let[e,t]=(0,a.useState)(!1),[r,o]=(0,a.useState)("https://maroon-lapwing-781450.hostingersite.com"),[n,l]=(0,a.useState)(""),[i,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)("guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0"),[h,x]=(0,a.useState)("");return(0,s.jsxs)("div",{className:"container mx-auto p-6 max-w-4xl",children:[s.jsx("h1",{className:"text-3xl font-bold mb-6",children:"WooCommerce Checkout Test"}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md mb-6",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Generate Checkout URL"}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium mb-1",children:"Base URL"}),s.jsx("input",{type:"text",value:r,onChange:e=>o(e.target.value),className:"w-full p-2 border border-gray-300 rounded",placeholder:"https://your-woocommerce-site.com"})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium mb-1",children:"Cart ID (optional)"}),s.jsx("input",{type:"text",value:n,onChange:e=>l(e.target.value),className:"w-full p-2 border border-gray-300 rounded",placeholder:"cart123"})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",checked:i,onChange:e=>d(e.target.checked),className:"mr-2"}),s.jsx("span",{children:"Is User Logged In?"})]}),s.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"If checked, guest checkout parameters will not be added"})]}),(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium mb-1",children:"Additional Parameters"}),s.jsx("input",{type:"text",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border border-gray-300 rounded",placeholder:"guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0"})]}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[s.jsx("button",{onClick:()=>{try{let e=(0,c.ML)(n,i);x(e)}catch(e){console.error("Error generating URL:",e),alert("Error generating URL. Check console for details.")}},className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Generate Using Library Function"}),s.jsx("button",{onClick:()=>{try{let e=`${r}/checkout/?${u}`;x(e)}catch(e){console.error("Error generating direct URL:",e),alert("Error generating direct URL. Check console for details.")}},className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Generate Direct URL"})]})]}),h&&(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Generated URL"}),s.jsx("div",{className:"mb-4 p-3 bg-gray-100 rounded overflow-x-auto",children:s.jsx("code",{className:"text-sm break-all",children:h})}),s.jsx("button",{onClick:()=>{if(!h){alert("Please generate a URL first");return}t(!0),window.location.href=h},disabled:e,className:`px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 ${e?"opacity-50 cursor-not-allowed":""}`,children:e?"Redirecting...":"Test This URL"})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded",children:[s.jsx("h3",{className:"font-semibold text-yellow-800",children:"Troubleshooting Tips"}),(0,s.jsxs)("ul",{className:"list-disc pl-5 mt-2 text-sm",children:[(0,s.jsxs)("li",{children:["If you're redirected to login, check your WooCommerce settings under ",s.jsx("strong",{children:"WooCommerce > Settings > Accounts & Privacy"})]}),s.jsx("li",{children:'Ensure "Allow customers to place orders without an account" is checked'}),s.jsx("li",{children:"Try using an incognito/private browser window to avoid session conflicts"}),s.jsx("li",{children:"Check browser console for any errors during redirection"})]})]})]})}c=(n.then?(await n)():n)[0],o()}catch(e){o(e)}})},22332:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\woocommerce-checkout-test\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,7499,1651],()=>r(96729));module.exports=o})();