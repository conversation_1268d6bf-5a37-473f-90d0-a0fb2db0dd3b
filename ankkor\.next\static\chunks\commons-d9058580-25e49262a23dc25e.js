"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6758],{3371:function(e,t,a){a.d(t,{CustomerProvider:function(){return f},O:function(){return m}});var s=a(57437),r=a(2265),n=a(99376),o=a(77690),i=a(68123),l=a(71917),c=a(67111),u=a(32898);let d=(0,r.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),m=()=>(0,r.useContext)(d);function f(e){let{children:t}=e,[a,m]=(0,r.useState)(null),[f,h]=(0,r.useState)(!0),[g,x]=(0,r.useState)(null),[v,y]=(0,r.useState)(null),p=(0,n.useRouter)(),{addToast:b}=(0,l.p)();(0,u.j)();let N=e=>e?{...e,displayName:e.displayName||e.username||"".concat(e.firstName||""," ").concat(e.lastName||"").trim()||"User"}:null,j=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let t=await e.json();if(console.log("CustomerProvider: Auth API result:",t),!t.success||!t.customer)return y(null),{success:!1,message:t.message||"Not authenticated"};{let e=t.token;return console.log("CustomerProvider: Token from API response:",!!e),y(e||null),{success:!0,customer:t.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),y(null),{success:!1,message:"Network error"}}},k=async()=>{try{let e=await j();if(e.success){let t={...e.customer,token:e.token};m(N(t)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),m(null),y(null)}catch(e){console.error("Error refreshing customer data:",e),m(null),y(null)}};(0,r.useEffect)(()=>{(async()=>{try{h(!0);let e=await j();if(e.success){console.log("Found valid authentication, customer data loaded"),console.log("Token available on mount:",!!e.token);let t={...e.customer,token:e.token};m(N(t))}else console.log("No valid authentication found:",e.message),m(null),y(null)}catch(e){console.error("Error checking customer session:",e),(0,o.kS)(),m(null),y(null)}finally{h(!1)}})()},[]);let w=e=>{if(!e)return"An unknown error occurred";let t="string"==typeof e?e:e.message||JSON.stringify(e);return t.includes("Unidentified customer")?"The email or password you entered is incorrect. Please try again.":t.includes("already associated")?"An account with this email already exists. Please sign in instead.":t.includes("password")&&t.includes("too short")?"Your password must be at least 8 characters. Please try again.":t.includes("token")&&(t.includes("expired")||t.includes("invalid"))?"Your login session has expired. Please sign in again.":t.includes("network")||t.includes("failed to fetch")?"Network connection issue. Please check your internet connection and try again.":t},C=async e=>{h(!0),x(null);try{let t=await (0,o.x4)(e.email,e.password);if(!t||!t.success||!t.user)throw Error("Login failed: No user data returned");let a={id:t.user.id,databaseId:t.user.databaseId,email:t.user.email,firstName:t.user.firstName,lastName:t.user.lastName,token:t.token};m(N(a));let s=t.token;console.log("Login successful, token from API:",!!s),y(s||null);let r=c.xS.getState();try{await r.clearCart(),await r.initializeCart()}catch(e){console.error("Error initializing cart after login:",e)}b("Welcome back, ".concat((null==a?void 0:a.firstName)||"there","!"),"success"),p.push("/")}catch(t){let e=w(t);throw x(e),b(e,"error"),t}finally{h(!1)}},S=async e=>{h(!0),x(null);try{var t;let a=await (0,o.z2)(e.email,e.firstName,e.lastName,e.password);if(!a||!a.success||!a.customer)throw Error("Registration failed: No customer data returned");let s={...a.customer,token:a.token};m(N(s));let r=a.token;console.log("Registration successful, token from API:",!!r),y(r||null);let n=c.xS.getState();try{await n.clearCart(),await n.initializeCart()}catch(e){console.error("Error initializing cart after registration:",e)}b("Welcome to Ankkor, ".concat(null===(t=a.customer)||void 0===t?void 0:t.firstName,"!"),"success"),p.push("/")}catch(t){let e=w(t);throw x(e),b(e,"error"),t}finally{h(!1)}},E=async e=>{h(!0),x(null);try{let t=await (0,i.lG)(e);if(!t||!t.customer)throw Error("Profile update failed: No customer data returned");return m(N(t.customer)),b("Your profile has been updated successfully","success"),t}catch(t){let e=w(t);throw x(e),b(e,"error"),t}finally{h(!1)}};return(0,s.jsx)(d.Provider,{value:{customer:a,isLoading:f,isAuthenticated:!!a,token:v,login:C,register:S,logout:()=>{(0,o.kS)(),m(null),y(null),console.log("Logout completed, token cleared"),c.xS.getState().clearCart().catch(e=>{console.error("Error clearing cart during logout:",e)}),sessionStorage.removeItem("cartInitialized"),sessionStorage.removeItem("cartInitializationAttempts"),b("You have been signed out successfully","info"),p.push("/"),p.refresh()},updateProfile:E,error:g,refreshCustomer:k},children:t})}},64528:function(e,t,a){a.d(t,{Gd:function(){return l}});var s=a(57437),r=a(2265),n=a(59625),o=a(89134),i=a(40257);let l=(0,n.Ue)()((0,o.tJ)(e=>({isLaunchingSoon:"true"===i.env.NEXT_PUBLIC_LAUNCHING_SOON,setIsLaunchingSoon:e=>{console.warn("Changing launch state is disabled in production.")}}),{name:"ankkor-launch-state"})),c=(0,r.createContext)(void 0);t.default=e=>{let{children:t}=e,a=l(),[n,o]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{o(!0);{let e="true"===i.env.NEXT_PUBLIC_LAUNCHING_SOON;a.isLaunchingSoon!==e&&l.setState({isLaunchingSoon:e})}},[a]),(0,s.jsx)(c.Provider,{value:a,children:n?t:null})}},11658:function(e,t,a){a.d(t,{default:function(){return g},r:function(){return d}});var s=a(57437),r=a(2265),n=a(99376),o=a(48131),i=a(43886),l=e=>{let{size:t="md",variant:a="thread",className:r=""}=e,n={sm:{container:"w-16 h-16",text:"text-xs"},md:{container:"w-24 h-24",text:"text-sm"},lg:{container:"w-32 h-32",text:"text-base"}};return"thread"===a?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsxs)("div",{className:"relative ".concat(n[t].container),children:[(0,s.jsx)(i.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27",borderRightColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1.5,repeat:1/0,ease:"linear"}}),(0,s.jsx)(i.E.div,{className:"absolute inset-2 rounded-full border-2 border-[#e5e2d9]",style:{borderBottomColor:"#8a8778",borderLeftColor:"#8a8778"},animate:{rotate:-360},transition:{duration:2,repeat:1/0,ease:"linear"}}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-[#2c2c27]"})})]}),(0,s.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(n[t].text),children:"Loading Collection"})]}):"fabric"===a?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsxs)("div",{className:"relative ".concat(n[t].container," flex items-center justify-center"),children:[(0,s.jsx)(i.E.div,{className:"absolute w-1/3 h-1/3 bg-[#e5e2d9]",animate:{rotate:360,scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),(0,s.jsx)(i.E.div,{className:"absolute w-1/3 h-1/3 bg-[#8a8778]",animate:{rotate:-360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.3}}),(0,s.jsx)(i.E.div,{className:"absolute w-1/3 h-1/3 bg-[#2c2c27]",animate:{rotate:360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.6}})]}),(0,s.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(n[t].text),children:"Preparing Your Style"})]}):"button"===a?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsx)("div",{className:"relative ".concat(n[t].container," flex items-center justify-center"),children:(0,s.jsx)("div",{className:"relative flex",children:[0,1,2,3].map(e=>(0,s.jsx)(i.E.div,{className:"w-3 h-3 mx-1 rounded-full bg-[#2c2c27] border border-[#8a8778]",animate:{y:[0,-10,0],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,ease:"easeInOut",delay:.2*e}},e))})}),(0,s.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(n[t].text),children:"Tailoring Experience"})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsx)("div",{className:"relative ".concat(n[t].container),children:(0,s.jsx)(i.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}),(0,s.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(n[t].text),children:"Loading"})]})},c=e=>{let{isLoading:t,variant:a="thread"}=e;return(0,s.jsx)(o.M,{children:t&&(0,s.jsx)(i.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"fixed inset-0 z-[200] flex items-center justify-center bg-[#f8f8f5]/90 backdrop-blur-sm",children:(0,s.jsx)(l,{variant:a,size:"lg"})})})};let u=(0,r.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),d=()=>(0,r.useContext)(u),m={"/collection":"fabric","/collection/shirts":"fabric","/collection/polos":"fabric","/product":"thread","/about":"button","/customer-service":"button","/account":"thread","/wishlist":"thread"},f=e=>{let{setIsLoading:t,setVariant:s}=e,o=(0,n.usePathname)(),{useSearchParams:i}=a(99376),l=i();return(0,r.useEffect)(()=>{t(!0),s(m["/"+o.split("/")[1]]||m[o]||"thread");let e=setTimeout(()=>{t(!1)},1200);return()=>clearTimeout(e)},[o,l,t,s]),null},h=()=>(0,s.jsx)("div",{className:"hidden",children:"Loading route..."});var g=e=>{let{children:t}=e,[a,n]=(0,r.useState)(!1),[o,i]=(0,r.useState)("thread");return(0,s.jsxs)(u.Provider,{value:{isLoading:a,setLoading:n,variant:o,setVariant:i},children:[(0,s.jsx)(r.Suspense,{fallback:(0,s.jsx)(h,{}),children:(0,s.jsx)(f,{setIsLoading:n,setVariant:i})}),t,(0,s.jsx)(c,{isLoading:a,variant:o})]})}},57152:function(e,t,a){var s=a(57437),r=a(2265),n=a(33145),o=a(43886);t.Z=e=>{let{src:t,alt:a,width:i,height:l,fill:c=!1,sizes:u=c?"(max-width: 768px) 100vw, 50vw":void 0,priority:d=!1,className:m="",animate:f=!0,style:h={}}=e,[g,x]=(0,r.useState)(!0),[v,y]=(0,r.useState)(!1);return(0,s.jsxs)("div",{className:"relative overflow-hidden ".concat(m),style:{minHeight:c?"100%":void 0,height:c?"100%":void 0,...h},onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:[g&&(0,s.jsx)(o.E.div,{className:"absolute inset-0 bg-[#f4f3f0]",initial:{opacity:1},animate:{opacity:[.5,.8,.5],backgroundPosition:["0% 0%","100% 100%"]},transition:{opacity:{duration:1.5,repeat:1/0,ease:"easeInOut"},backgroundPosition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},style:{background:"linear-gradient(90deg, #f4f3f0, #e5e2d9, #f4f3f0)",backgroundSize:"200% 100%"}}),(0,s.jsx)(o.E.div,{className:"w-full h-full",animate:f&&v?{scale:1.05,filter:"brightness(1.1)"}:{scale:1,filter:"brightness(1)"},transition:{duration:.7,ease:"easeInOut"},children:(0,s.jsx)(n.default,{src:t,alt:a,width:i,height:l,fill:c,sizes:u,priority:d,className:"\n            ".concat(g?"opacity-0":"opacity-100"," \n            transition-opacity duration-500\n            ").concat(c?"object-cover":"","\n          "),onLoad:()=>x(!1)})})]})}}}]);