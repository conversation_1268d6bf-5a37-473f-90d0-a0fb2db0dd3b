{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "JlOkHYOvGdD_B5Sa5BOpu", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "N29QmlLcWerCNXsaVvmws1RrusZ+F6a7RHf60PN38gg=", "__NEXT_PREVIEW_MODE_ID": "a81ea805dc3fda4750c8b60136093034", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "268950223edf663a27163ac30ee1f3ffb28c8a5183a52868526f603dd8e45cef", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "62979c7bbf70cebb990a936599bf55a2c5dd222f216666a26590f094870b4b6f"}}}, "functions": {}, "sortedMiddleware": ["/"]}