"use strict";(()=>{var t={};t.id=167,t.ids=[167],t.modules={20399:t=>{t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:t=>{t.exports=require("crypto")},93690:t=>{t.exports=import("graphql-request")},6694:(t,a,e)=>{e.a(t,async(t,o)=>{try{e.r(a),e.d(a,{originalPathname:()=>S,patchFetch:()=>c,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>k,staticGenerationAsyncStorage:()=>l});var r=e(49303),s=e(88716),i=e(60670),n=e(64839),d=t([n]);n=(d.then?(await d)():d)[0];let u=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/[id]/stock/route",pathname:"/api/products/[id]/stock",filename:"route",bundlePath:"app/api/products/[id]/stock/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\products\\[id]\\stock\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:p,staticGenerationAsyncStorage:l,serverHooks:k}=u,S="/api/products/[id]/stock/route";function c(){return(0,i.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:l})}o()}catch(t){o(t)}})},64839:(t,a,e)=>{e.a(t,async(t,o)=>{try{e.r(a),e.d(a,{GET:()=>d,POST:()=>c});var r=e(87070),s=e(93690),i=e(94868),n=t([s]);s=(n.then?(await n)():n)[0];let u=new i.s({url:process.env.UPSTASH_REDIS_REST_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||""}),p=process.env.WOOCOMMERCE_GRAPHQL_URL||"",l=new s.GraphQLClient(p),k=(0,s.gql)`
  query GetProductStock($id: ID!, $idType: ProductIdType = DATABASE_ID) {
    product(id: $id, idType: $idType) {
      id
      databaseId
      stockStatus
      stockQuantity
      manageStock
      ... on VariableProduct {
        variations {
          nodes {
            id
            databaseId
            stockStatus
            stockQuantity
            manageStock
          }
        }
      }
    }
  }
`,S=(0,s.gql)`
  query GetVariationStock($id: ID!) {
    productVariation(id: $id, idType: DATABASE_ID) {
      id
      databaseId
      stockStatus
      stockQuantity
      manageStock
      parent {
        node {
          id
          databaseId
        }
      }
    }
  }
`;async function d(t,{params:a}){try{let e;let{searchParams:o}=new URL(t.url),s=o.get("variation_id"),i=a.id,n=s?`stock:variation:${s}`:`stock:product:${i}`,d=await u.get(n);if(d)return console.log("Returning cached stock data for:",n),r.NextResponse.json(d);if(s){let t=await l.request(S,{id:s});if(!t.productVariation)return r.NextResponse.json({error:"Variation not found"},{status:404});let a=t.productVariation;e={id:a.databaseId,type:"variation",stockStatus:a.stockStatus,stockQuantity:a.stockQuantity,manageStock:a.manageStock,parentId:a.parent?.node?.databaseId,lastUpdated:new Date().toISOString()}}else{let t=await l.request(k,{id:i,idType:"DATABASE_ID"});if(!t.product)return r.NextResponse.json({error:"Product not found"},{status:404});let a=t.product;e={id:a.databaseId,type:"product",stockStatus:a.stockStatus,stockQuantity:a.stockQuantity,manageStock:a.manageStock,variations:a.variations?.nodes?.map(t=>({id:t.databaseId,stockStatus:t.stockStatus,stockQuantity:t.stockQuantity,manageStock:t.manageStock})),lastUpdated:new Date().toISOString()}}return await u.set(n,e,30),r.NextResponse.json(e)}catch(t){return console.error("Stock check error:",t),r.NextResponse.json({error:"Failed to check stock",details:t instanceof Error?t.message:"Unknown error"},{status:500})}}async function c(t,{params:a}){try{let{items:e}=await t.json(),o=await Promise.all(e.map(async e=>{try{let o=await d(new r.NextRequest(`${t.url}?${e.variationId?`variation_id=${e.variationId}`:""}`),{params:a}),s=await o.json();if(200!==o.status)return{productId:e.productId,variationId:e.variationId,available:!1,error:s.error};let i="IN_STOCK"===s.stockStatus||"instock"===s.stockStatus,n=!s.manageStock||null===s.stockQuantity||s.stockQuantity>=e.quantity;return{productId:e.productId,variationId:e.variationId,available:i&&n,stockStatus:s.stockStatus,stockQuantity:s.stockQuantity,requestedQuantity:e.quantity,message:i?n?"Available":`Only ${s.stockQuantity} items available`:"Product is out of stock"}}catch(t){return{productId:e.productId,variationId:e.variationId,available:!1,error:"Stock validation failed"}}}));return r.NextResponse.json({validations:o,allAvailable:o.every(t=>t.available)})}catch(t){return console.error("Bulk stock validation error:",t),r.NextResponse.json({error:"Bulk stock validation failed"},{status:500})}}o()}catch(t){o(t)}})}};var a=require("../../../../../webpack-runtime.js");a.C(t);var e=t=>a(a.s=t),o=a.X(0,[8948,5972,4766,4868],()=>e(6694));module.exports=o})();