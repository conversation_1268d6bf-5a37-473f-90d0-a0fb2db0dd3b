# WooCommerce Guest Checkout Fix

## Quick-Start Guide

We've made the following changes to fix the WooCommerce guest checkout redirection issue:

1. **Updated checkout URL parameters in frontend code**
   - Added comprehensive parameters to bypass login and registration
   - Implemented fallback methods for scenarios where the primary approach fails

2. **Created helper scripts and documentation**
   - `scripts/verify-guest-checkout.js` - Guides you through checking WooCommerce settings
   - `scripts/test-guest-checkout.js` - Tests guest checkout functionality directly
   - `force-guest-checkout.php` - WordPress plugin to force guest checkout server-side
   - `docs/fix-woocommerce-guest-checkout.md` - Detailed documentation

## Essential Steps to Fix

### 1. Apply Frontend Code Changes

✅ **Already Done**: We've already updated the code to include comprehensive guest checkout parameters.

### 2. Server-Side Fix (Required)

Choose one option:

#### Option A: Upload the Plugin (Recommended)
1. Create folder: `wp-content/plugins/ankkor-force-guest-checkout/`
2. Upload `force-guest-checkout.php` to that folder
3. Activate the plugin in WordPress admin

#### Option B: Add Code to Theme
Add the code from `force-guest-checkout.php` to your theme's `functions.php` file.

### 3. Test the Fix

Run the test script:
```
node scripts/test-guest-checkout.js
```

Or manually test by:
1. Adding products to cart
2. Click checkout
3. Verify you can proceed without logging in

## Troubleshooting

If issues persist:

1. Check WooCommerce settings:
   - WooCommerce > Settings > Accounts & Privacy
   - Ensure "Allow customers to place orders without an account" is checked

2. Check WPGraphQL settings:
   - GraphQL > Settings > WooCommerce
   - Ensure "Disable GQL Session Handler" is UNCHECKED

3. Clear all caches:
   - WordPress cache
   - Browser cache
   - WooCommerce cache
   - CDN cache

For detailed troubleshooting steps, see `docs/fix-woocommerce-guest-checkout.md`. 