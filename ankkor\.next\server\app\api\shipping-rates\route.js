"use strict";(()=>{var e={};e.id=7546,e.ids=[7546],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45163:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>h,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{POST:()=>p});var i=r(49303),a=r(88716),n=r(60670),o=r(87070);async function p(e){try{let{pincode:t,cartItems:r}=await e.json();if(!t||!r||!Array.isArray(r))return o.NextResponse.json({error:"Invalid request data"},{status:400});if(!/^[0-9]{6}$/.test(t))return o.NextResponse.json({error:"Invalid pincode format"},{status:400});let s=process.env.SHIPPING_PROVIDER||"woocommerce",i=[];return i="woocommerce"===s?await d(t,r):"delhivery"===s?await c(t,r):await u(t,r),o.NextResponse.json(i)}catch(e){return console.error("Shipping rates error:",e),o.NextResponse.json({error:e.message||"Failed to calculate shipping rates"},{status:500})}}async function d(e,t){try{let r="https://maroon-lapwing-781450.hostingersite.com",s=process.env.WOOCOMMERCE_CONSUMER_KEY,i=process.env.WOOCOMMERCE_CONSUMER_SECRET;if(!r||!s||!i)throw Error("WooCommerce credentials not configured");let a=t.reduce((e,t)=>{let r="string"==typeof t.price?parseFloat(t.price):t.price;return e+r*t.quantity},0);t.reduce((e,t)=>e+(t.weight||.5)*t.quantity,0);let n=Buffer.from(`${s}:${i}`).toString("base64"),o=await fetch(`${r}/wp-json/wc/v3/shipping/zones`,{headers:{Authorization:`Basic ${n}`}});if(!o.ok)throw Error("Failed to fetch shipping zones");let p=await o.json(),d=[];for(let t of p){if(0===t.id)continue;let s=await fetch(`${r}/wp-json/wc/v3/shipping/zones/${t.id}/methods`,{headers:{Authorization:`Basic ${n}`}});if(s.ok){for(let r of(await s.json()))if(r.enabled){let s=0;if("flat_rate"===r.method_id)s=parseFloat(r.settings?.cost?.value||"0");else if("free_shipping"===r.method_id){let e=parseFloat(r.settings?.min_amount?.value||"0");s=a>=e?0:parseFloat(r.settings?.cost?.value||"50")}else"local_pickup"===r.method_id&&(s=parseFloat(r.settings?.cost?.value||"0"));d.push({id:`${t.id}_${r.instance_id}`,name:r.title,cost:s,description:r.settings?.description?.value||"",estimatedDays:function(e,t){let r=["110001","400001","560001","600001","700001"].includes(t);switch(e){case"free_shipping":case"flat_rate":return r?"3-5 days":"5-7 days";case"local_pickup":return"Same day";default:return"5-7 days"}}(r.method_id,e)})}}}if(0===d.length)return u(e,t);return d}catch(r){return console.error("WooCommerce shipping error:",r),u(e,t)}}async function c(e,t){try{let e=t.reduce((e,t)=>e+(t.weight||.5)*t.quantity,0);return[{id:"delhivery_surface",name:"Delhivery Surface",cost:Math.max(50,10*e),description:"Standard delivery via Delhivery",estimatedDays:"5-7 days"},{id:"delhivery_express",name:"Delhivery Express",cost:Math.max(100,20*e),description:"Express delivery via Delhivery",estimatedDays:"2-3 days"}]}catch(r){return console.error("Delhivery shipping error:",r),u(e,t)}}async function u(e,t){let r=t.reduce((e,t)=>e+("string"==typeof t.price?parseFloat(t.price):t.price)*t.quantity,0),s=[];return s.push({id:"standard",name:"Standard Shipping",cost:r>1e3?0:50,description:"Delivered in 5-7 business days",estimatedDays:"5-7 days"}),["110001","400001","560001","600001","700001"].includes(e)?(s.push({id:"express",name:"Express Shipping",cost:150,description:"Delivered in 2-3 business days",estimatedDays:"2-3 days"}),s.push({id:"same_day",name:"Same Day Delivery",cost:300,description:"Delivered today before 9 PM",estimatedDays:"Today"})):s.push({id:"express",name:"Express Shipping",cost:200,description:"Delivered in 3-4 business days",estimatedDays:"3-4 days"}),s}let l=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/shipping-rates/route",pathname:"/api/shipping-rates",filename:"route",bundlePath:"app/api/shipping-rates/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\shipping-rates\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:y,serverHooks:m}=l,f="/api/shipping-rates/route";function g(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:y})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(45163));module.exports=s})();