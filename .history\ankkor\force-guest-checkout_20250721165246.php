<?php
/**
 * Plugin Name: Ankkor Force Guest Checkout
 * Description: Forces WooCommerce to allow guest checkout and bypasses login/registration
 * Version: 1.0
 * Author: Ankkor
 * Text Domain: ankkor-force-guest-checkout
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Force guest checkout and bypass login/registration
 */
add_filter('woocommerce_checkout_registration_required', '__return_false');
add_filter('woocommerce_checkout_registration_enabled', '__return_false');
add_filter('woocommerce_checkout_is_registration_required', '__return_false');

/**
 * Skip login/registration process completely
 */
add_action('template_redirect', function() {
    if (isset($_GET['force_guest_checkout']) && $_GET['force_guest_checkout'] == '1') {
        // Force guest checkout
        if (function_exists('WC') && WC()->session) {
            WC()->session->set('force_guest_checkout', true);
        }
    }
}, 5);

/**
 * Allow checkout without login even for registered users
 */
add_filter('woocommerce_checkout_registration_required', function($registration_required) {
    if (isset($_GET['force_guest_checkout']) && $_GET['force_guest_checkout'] == '1') {
        return false;
    }
    return $registration_required;
});

/**
 * Force guest checkout mode
 */
add_filter('pre_option_woocommerce_enable_guest_checkout', function($value) {
    if (isset($_GET['guest_checkout']) && $_GET['guest_checkout'] == 'yes') {
        return 'yes';
    }
    return $value;
});

/**
 * Disable checkout login form
 */
add_filter('woocommerce_checkout_registration_enabled', function($enabled) {
    if (isset($_GET['checkout_woocommerce_checkout_login_reminder']) && $_GET['checkout_woocommerce_checkout_login_reminder'] == '0') {
        return false;
    }
    return $enabled;
});

/**
 * Skip login for existing customers
 */
add_filter('woocommerce_checkout_must_be_logged_in', function($must_login) {
    if (isset($_GET['skip_login']) && $_GET['skip_login'] == '1') {
        return false;
    }
    return $must_login;
});

/**
 * Disable customer account creation during checkout
 */
add_filter('pre_option_woocommerce_enable_signup_and_login_from_checkout', function($value) {
    if (isset($_GET['create_account']) && $_GET['create_account'] == '0') {
        return 'no';
    }
    return $value;
});

/**
 * Add notice for admin about guest checkout
 */
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && is_admin() && get_option('woocommerce_enable_guest_checkout') !== 'yes') {
        ?>
        <div class="notice notice-warning is-dismissible">
            <p><strong>Ankkor Force Guest Checkout:</strong> WooCommerce guest checkout is currently disabled in your settings. This plugin will force-enable it via URL parameters, but we recommend enabling it in <a href="<?php echo admin_url('admin.php?page=wc-settings&tab=account'); ?>">WooCommerce Settings &gt; Accounts &amp; Privacy</a> for the best experience.</p>
        </div>
        <?php
    }
}); 