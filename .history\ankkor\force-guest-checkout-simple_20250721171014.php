<?php
/**
 * Plugin Name: Ankkor Force Guest Checkout Simple
 * Description: Simple plugin to force guest checkout in WooCommerce
 * Version: 1.0.0
 * Author: Ankkor
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Force guest checkout and bypass login/registration
 */
add_filter('woocommerce_checkout_registration_required', '__return_false');
add_filter('woocommerce_checkout_registration_enabled', '__return_false');
add_filter('woocommerce_checkout_is_registration_required', '__return_false');

/**
 * Skip login/registration process completely
 */
add_action('template_redirect', function() {
    if (isset($_GET['force_guest_checkout']) && $_GET['force_guest_checkout'] == '1') {
        // Force guest checkout
        if (function_exists('WC') && WC()->session) {
            WC()->session->set('force_guest_checkout', true);
        }
    }
    
    // Remove admin access prevention for checkout
    if (is_checkout() || isset($_GET['guest_checkout'])) {
        remove_action('template_redirect', 'wc_prevent_admin_access');
    }
}, 5);

/**
 * Allow checkout without login even for registered users
 */
add_filter('woocommerce_checkout_registration_required', function($registration_required) {
    if (isset($_GET['force_guest_checkout']) && $_GET['force_guest_checkout'] == '1') {
        return false;
    }
    return $registration_required;
});

/**
 * Force guest checkout mode
 */
add_filter('pre_option_woocommerce_enable_guest_checkout', function($value) {
    if (isset($_GET['guest_checkout']) && $_GET['guest_checkout'] == 'yes') {
        return 'yes';
    }
    return $value;
});

/**
 * Disable checkout login form
 */
add_filter('woocommerce_checkout_registration_enabled', function($enabled) {
    if (isset($_GET['checkout_woocommerce_checkout_login_reminder']) && $_GET['checkout_woocommerce_checkout_login_reminder'] == '0') {
        return false;
    }
    return $enabled;
});

/**
 * Skip login for existing customers
 */
add_filter('woocommerce_checkout_must_be_logged_in', function($must_login) {
    if (isset($_GET['skip_login']) && $_GET['skip_login'] == '1') {
        return false;
    }
    return $must_login;
});

/**
 * Modify checkout URL to add guest checkout parameters
 */
add_filter('woocommerce_get_checkout_url', function($url) {
    return add_query_arg(array(
        'guest_checkout' => 'yes',
        'checkout_woocommerce_checkout_login_reminder' => '0',
        'create_account' => '0',
        'skip_login' => '1',
        'force_guest_checkout' => '1'
    ), $url);
});

/**
 * Disable admin access prevention for checkout and API requests
 */
add_filter('woocommerce_prevent_admin_access', function($prevent_access) {
    // Check if this is a REST API or GraphQL request
    if (
        (defined('REST_REQUEST') && REST_REQUEST) ||
        (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/graphql') !== false) ||
        (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/checkout') !== false) ||
        (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/cart') !== false) ||
        (isset($_GET['guest_checkout']) && $_GET['guest_checkout'] == 'yes')
    ) {
        return false; // Don't prevent access for these requests
    }
    
    return $prevent_access;
});

/**
 * Add CORS headers for API requests
 */
add_action('rest_api_init', function() {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Allow-Headers: Authorization, Content-Type, X-WP-Nonce, X-WC-Store-API-Nonce');
    
    // Handle preflight OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        status_header(200);
        exit;
    }
});

/**
 * Add hidden fields to checkout form
 */
add_action('woocommerce_before_checkout_form', function() {
    if (!is_user_logged_in()) {
        echo '<input type="hidden" name="guest_checkout" value="yes">';
        echo '<input type="hidden" name="checkout_woocommerce_checkout_login_reminder" value="0">';
        echo '<input type="hidden" name="create_account" value="0">';
        echo '<input type="hidden" name="skip_login" value="1">';
        echo '<input type="hidden" name="force_guest_checkout" value="1">';
    }
}, 5);

/**
 * Enable guest checkout on plugin activation
 */
register_activation_hook(__FILE__, function() {
    update_option('woocommerce_enable_guest_checkout', 'yes');
    update_option('woocommerce_enable_checkout_login_reminder', 'no');
}); 