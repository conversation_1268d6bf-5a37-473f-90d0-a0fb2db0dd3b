<?php
/**
 * Plugin Name: WooCommerce Guest Checkout Fix
 * Description: Fixes CORS issues with WooCommerce guest checkout and prevents login redirects
 * Version: 1.0.0
 * Author: Ankkor
 * Text Domain: woo-guest-checkout-fix
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class WooCommerce_Guest_Checkout_Fix {

    /**
     * Initialize the plugin
     */
    public function __construct() {
        // Add CORS headers
        add_action('init', array($this, 'add_cors_headers'));
        
        // <PERSON>le preflight requests
        add_action('init', array($this, 'handle_preflight_requests'));
        
        // Disable login redirects
        add_action('init', array($this, 'disable_login_redirects'), 5);
        
        // Disable admin access prevention for checkout
        add_filter('woocommerce_prevent_admin_access', array($this, 'disable_admin_access_prevention'), 10, 1);
        
        // Force guest checkout
        add_filter('pre_option_woocommerce_enable_guest_checkout', array($this, 'enable_guest_checkout'));
        add_filter('pre_option_woocommerce_enable_checkout_login_reminder', array($this, 'disable_checkout_login_reminder'));
        
        // Add guest checkout parameters to checkout URL
        add_filter('woocommerce_get_checkout_url', array($this, 'add_guest_params_to_checkout_url'));
        
        // Add custom REST API endpoints
        add_action('rest_api_init', array($this, 'register_rest_routes'));
    }
    
    /**
     * Add CORS headers to all responses
     */
    public function add_cors_headers() {
        // Get the origin
        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '*';
        
        // Allow requests from any origin for development
        // In production, you might want to restrict this to specific domains
        header("Access-Control-Allow-Origin: $origin");
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
        header("Access-Control-Allow-Credentials: true");
        header("Access-Control-Allow-Headers: Authorization, Content-Type, X-WP-Nonce, X-WC-Store-API-Nonce, woocommerce-session");
        
        // Add Vary header when using specific origin
        if ($origin !== '*') {
            header('Vary: Origin');
        }
    }
    
    /**
     * Handle preflight OPTIONS requests
     */
    public function handle_preflight_requests() {
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            status_header(200);
            exit;
        }
    }
    
    /**
     * Disable login redirects
     */
    public function disable_login_redirects() {
        // Remove login redirect filters
        remove_filter('template_redirect', 'wc_auth_check_login_redirect', 10);
        
        // Skip login for existing customers
        add_filter('woocommerce_checkout_must_be_logged_in', '__return_false');
        
        // Disable checkout login form and registration requirements
        add_filter('woocommerce_checkout_registration_required', '__return_false');
        add_filter('woocommerce_checkout_registration_enabled', '__return_false');
        add_filter('woocommerce_checkout_is_registration_required', '__return_false');
    }
    
    /**
     * Disable admin access prevention for checkout and API requests
     */
    public function disable_admin_access_prevention($prevent_access) {
        // Check if this is a REST API, GraphQL, or checkout request
        if (
            (defined('REST_REQUEST') && REST_REQUEST) ||
            (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/graphql') !== false) ||
            (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/checkout') !== false) ||
            (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/cart') !== false) ||
            (isset($_GET['guest_checkout']) && $_GET['guest_checkout'] == 'yes')
        ) {
            return false; // Don't prevent access for these requests
        }
        
        return $prevent_access;
    }
    
    /**
     * Force enable guest checkout
     */
    public function enable_guest_checkout($value) {
        return 'yes';
    }
    
    /**
     * Disable checkout login reminder
     */
    public function disable_checkout_login_reminder($value) {
        return 'no';
    }
    
    /**
     * Add guest checkout parameters to checkout URL
     */
    public function add_guest_params_to_checkout_url($url) {
        // Only add parameters if user is not logged in
        if (!is_user_logged_in()) {
            return add_query_arg(array(
                'guest_checkout' => 'yes',
                'checkout_woocommerce_checkout_login_reminder' => '0',
                'create_account' => '0',
                'skip_login' => '1',
                'force_guest_checkout' => '1'
            ), $url);
        }
        
        return $url;
    }
    
    /**
     * Register custom REST API endpoints
     */
    public function register_rest_routes() {
        register_rest_route('ankkor/v1', '/guest-checkout', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_guest_checkout_url'),
            'permission_callback' => '__return_true',
        ));
        
        register_rest_route('ankkor/v1', '/fix-checkout', array(
            'methods' => 'POST',
            'callback' => array($this, 'fix_checkout_session'),
            'permission_callback' => '__return_true',
        ));
    }
    
    /**
     * Get guest checkout URL endpoint
     */
    public function get_guest_checkout_url() {
        // Get the checkout URL with guest parameters
        $checkout_url = wc_get_checkout_url();
        $checkout_url = add_query_arg(array(
            'guest_checkout' => 'yes',
            'checkout_woocommerce_checkout_login_reminder' => '0',
            'create_account' => '0',
            'skip_login' => '1',
            'force_guest_checkout' => '1'
        ), $checkout_url);
        
        return rest_ensure_response(array(
            'success' => true,
            'checkout_url' => $checkout_url,
        ));
    }
    
    /**
     * Fix checkout session endpoint
     */
    public function fix_checkout_session($request) {
        // Force guest checkout in session
        if (function_exists('WC') && WC()->session) {
            WC()->session->set('force_guest_checkout', true);
        }
        
        // Return success response
        return rest_ensure_response(array(
            'success' => true,
            'message' => 'Checkout session fixed for guest checkout',
        ));
    }
}

// Initialize the plugin
$woocommerce_guest_checkout_fix = new WooCommerce_Guest_Checkout_Fix(); 