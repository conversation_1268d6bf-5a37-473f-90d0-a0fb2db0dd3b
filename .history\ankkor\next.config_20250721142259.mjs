/**
 * @type {import('next').NextConfig}
 */
const nextConfig = {
  /* config options here */
  images: {
    domains: ['images.unsplash.com', 'your-wordpress-site.com', 'deepskyblue-penguin-370791.hostingersite.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'plus.unsplash.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lightpink-eagle-376738.hostingersite.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'deepskyblue-penguin-370791.hostingersite.com',
        pathname: '/**',
      },
      {
        // Generic pattern for WordPress media files
        protocol: 'https',
        hostname: '**.wp.com',
        pathname: '/**',
      }
    ],
  },
  
  // Production optimizations
  poweredByHeader: false,
  compress: true,
  reactStrictMode: true,
  
  // Experimental features
  experimental: {
    // External packages to optimize with server components
    serverComponentsExternalPackages: [
      'sharp',
      'graphql-request',
    ],
    
    // Optimization features
    optimizeCss: true,
    serverMinification: true,
    instrumentationHook: true,
  },
  
  // Disable ESLint during build for production deployment
  eslint: {
    ignoreDuringBuilds: true,
  },
  
  // Disable TypeScript checking during builds
  typescript: {
    ignoreBuildErrors: true,
  },
  
  // Configure output settings
  output: 'standalone',
  
  // Set a higher timeout for static page generation
  staticPageGenerationTimeout: 180,
  
  // Expose environment variables to the browser
  env: {
    LAUNCHING_SOON: process.env.LAUNCHING_SOON || 'true',
    QSTASH_CURRENT_SIGNING_KEY: process.env.QSTASH_CURRENT_SIGNING_KEY,
    QSTASH_NEXT_SIGNING_KEY: process.env.QSTASH_NEXT_SIGNING_KEY,
    QSTASH_TOKEN: process.env.QSTASH_TOKEN,
  },
  
  // Configure routes that should be dynamic
  async headers() {
    return [
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
    ];
  },
};

export default nextConfig; 