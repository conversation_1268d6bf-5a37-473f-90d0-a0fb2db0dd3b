(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4822],{54278:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(19506),(0,r(75266).appBootstrap)(()=>{let{hydrate:e}=r(92304);r(12846),r(4707),e()}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19506:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(65157);{let e=r.u;r.u=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return encodeURI(e(...r))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90295:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"actionAsyncStorage",{enumerable:!0,get:function(){return n.actionAsyncStorage}});let n=r(47149);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6495:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return u}});let n=r(2265),o=r(54887),l="next-route-announcer";function u(e){let{tree:t}=e,[r,u]=(0,n.useState)(null);(0,n.useEffect)(()=>(u(function(){var e;let t=document.getElementsByName(l)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(l);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(l)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[i,a]=(0,n.useState)(""),s=(0,n.useRef)();return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==s.current&&s.current!==e&&a(e),s.current=e},[t]),r?(0,o.createPortal)(i,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6866:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION:function(){return n},FLIGHT_PARAMETERS:function(){return a},NEXT_DID_POSTPONE_HEADER:function(){return d},NEXT_ROUTER_PREFETCH_HEADER:function(){return l},NEXT_ROUTER_STATE_TREE:function(){return o},NEXT_RSC_UNION_QUERY:function(){return s},NEXT_URL:function(){return u},RSC_CONTENT_TYPE_HEADER:function(){return i},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",o="Next-Router-State-Tree",l="Next-Router-Prefetch",u="Next-Url",i="text/x-component",a=[[r],[o],[l]],s="_rsc",d="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12846:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return A},default:function(){return L},getServerActionDispatcher:function(){return S},urlToUrlWithoutFlightMarker:function(){return T}});let n=r(53099),o=r(57437),l=n._(r(2265)),u=r(61956),i=r(24673),a=r(33456),s=r(79060),d=r(47744),c=r(61060),f=r(82952),p=r(86146),_=r(1634),h=r(6495),y=r(4123),v=r(39320),E=r(38137),w=r(6866),b=r(35076),j=r(11283),R=r(84541),m="undefined"==typeof window,g=m?null:new Map,P=null;function S(){return P}let x={};function T(e){let t=new URL(e,location.origin);return t.searchParams.delete(w.NEXT_RSC_UNION_QUERY),t}function N(e){return e.origin!==window.location.origin}function O(e){let{appRouterState:t,sync:r}=e;return(0,l.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:o}=t,l={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,a.createHrefFromUrl)(new URL(window.location.href))!==o?(n.pendingPush=!1,window.history.pushState(l,"",o)):window.history.replaceState(l,"",o),r(t)},[t,r]),null}function A(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null}}function M(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function C(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,o=null!==n?n:r;return(0,l.useDeferredValue)(r,o)}function I(e){let t,{buildId:r,initialHead:n,initialTree:a,urlParts:c,initialSeedData:w,couldBeIntercepted:S,assetPrefix:T,missingSlots:A}=e,I=(0,l.useMemo)(()=>(0,f.createInitialRouterState)({buildId:r,initialSeedData:w,urlParts:c,initialTree:a,initialParallelRoutes:g,location:m?null:window.location,initialHead:n,couldBeIntercepted:S}),[r,w,c,a,n,S]),[L,U,D]=(0,d.useReducerWithReduxDevtools)(I);(0,l.useEffect)(()=>{g=null},[]);let{canonicalUrl:H}=(0,d.useUnwrapState)(L),{searchParams:F,pathname:k}=(0,l.useMemo)(()=>{let e=new URL(H,"undefined"==typeof window?"http://n":window.location.href);return{searchParams:e.searchParams,pathname:(0,j.hasBasePath)(e.pathname)?(0,b.removeBasePath)(e.pathname):e.pathname}},[H]),B=(0,l.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,l.startTransition)(()=>{U({type:i.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[U]),X=(0,l.useCallback)((e,t,r)=>{let n=new URL((0,_.addBasePath)(e),location.href);return U({type:i.ACTION_NAVIGATE,url:n,isExternalUrl:N(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t})},[U]);P=(0,l.useCallback)(e=>{(0,l.startTransition)(()=>{U({...e,type:i.ACTION_SERVER_ACTION})})},[U]);let V=(0,l.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r;if(!(0,p.isBot)(window.navigator.userAgent)){try{r=new URL((0,_.addBasePath)(e),window.location.href)}catch(t){throw Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL.")}N(r)||(0,l.startTransition)(()=>{var e;U({type:i.ACTION_PREFETCH,url:r,kind:null!=(e=null==t?void 0:t.kind)?e:i.PrefetchKind.FULL})})}},replace:(e,t)=>{void 0===t&&(t={}),(0,l.startTransition)(()=>{var r;X(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,l.startTransition)(()=>{var r;X(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,l.startTransition)(()=>{U({type:i.ACTION_REFRESH,origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}}),[U,X]);(0,l.useEffect)(()=>{window.next&&(window.next.router=V)},[V]),(0,l.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(x.pendingMpaPath=void 0,U({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[U]);let{pushRef:G}=(0,d.useUnwrapState)(L);if(G.mpaNavigation){if(x.pendingMpaPath!==H){let e=window.location;G.pendingPush?e.assign(H):e.replace(H),x.pendingMpaPath=H}(0,l.use)(E.unresolvedThenable)}(0,l.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,l.startTransition)(()=>{U({type:i.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=M(t),o&&r(o)),e(t,n,o)},window.history.replaceState=function(e,n,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=M(e),o&&r(o)),t(e,n,o)};let n=e=>{let{state:t}=e;if(t){if(!t.__NA){window.location.reload();return}(0,l.startTransition)(()=>{U({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:t.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[U]);let{cache:J,tree:W,nextUrl:Y,focusAndScrollRef:z}=(0,d.useUnwrapState)(L),K=(0,l.useMemo)(()=>(0,v.findHeadInCache)(J,W[1]),[J,W]),Q=(0,l.useMemo)(()=>(function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],o=Array.isArray(t),l=o?t[1]:t;!l||l.startsWith(R.PAGE_SEGMENT_KEY)||(o&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):o&&(r[t[0]]=t[1]),r=e(n,r))}return r})(W),[W]);if(null!==K){let[e,r]=K;t=(0,o.jsx)(C,{headCacheNode:e},r)}else t=null;let q=(0,o.jsxs)(y.RedirectBoundary,{children:[t,J.rsc,(0,o.jsx)(h.AppRouterAnnouncer,{tree:W})]});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(O,{appRouterState:(0,d.useUnwrapState)(L),sync:D}),(0,o.jsx)(s.PathParamsContext.Provider,{value:Q,children:(0,o.jsx)(s.PathnameContext.Provider,{value:k,children:(0,o.jsx)(s.SearchParamsContext.Provider,{value:F,children:(0,o.jsx)(u.GlobalLayoutRouterContext.Provider,{value:{buildId:r,changeByServerResponse:B,tree:W,focusAndScrollRef:z,nextUrl:Y},children:(0,o.jsx)(u.AppRouterContext.Provider,{value:V,children:(0,o.jsx)(u.LayoutRouterContext.Provider,{value:{childNodes:J.parallelRoutes,tree:W,url:H,loading:J.loading},children:q})})})})})})]})}function L(e){let{globalErrorComponent:t,...r}=e;return(0,o.jsx)(c.ErrorBoundary,{errorComponent:t,children:(0,o.jsx)(I,{...r})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96149:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return l}});let n=r(18993),o=r(51845);function l(e){let t=o.staticGenerationAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw new n.BailoutToCSRError(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19107:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return l}});let n=r(57437),o=r(54535);function l(e){let{Component:t,props:r}=e;return r.searchParams=(0,o.createDynamicallyTrackedSearchParams)(r.searchParams||{}),(0,n.jsx)(t,{...r})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61060:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundary:function(){return _},ErrorBoundaryHandler:function(){return c},GlobalError:function(){return f},default:function(){return p}});let n=r(47043),o=r(57437),l=n._(r(2265)),u=r(35475),i=r(89721),a=r(51845),s={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function d(e){let{error:t}=e,r=a.staticGenerationAsyncStorage.getStore();if((null==r?void 0:r.isRevalidate)||(null==r?void 0:r.isStaticGeneration))throw console.error(t),t;return null}class c extends l.default.Component{static getDerivedStateFromError(e){if((0,i.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(d,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,o.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,o.jsxs)("html",{id:"__next_error__",children:[(0,o.jsx)("head",{}),(0,o.jsxs)("body",{children:[(0,o.jsx)(d,{error:t}),(0,o.jsx)("div",{style:s.error,children:(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{style:s.text,children:"Application error: a "+(r?"server":"client")+"-side exception has occurred (see the "+(r?"server logs":"browser console")+" for more information)."}),r?(0,o.jsx)("p",{style:s.text,children:"Digest: "+r}):null]})})]})]})}let p=f;function _(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:l}=e,i=(0,u.usePathname)();return t?(0,o.jsx)(c,{pathname:i,errorComponent:t,errorStyles:r,errorScripts:n,children:l}):(0,o.jsx)(o.Fragment,{children:l})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46177:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return o}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89721:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return l}});let n=r(98200),o=r(88968);function l(e){return e&&e.digest&&((0,o.isRedirectError)(e)||(0,n.isNotFoundError)(e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47149:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"actionAsyncStorage",{enumerable:!0,get:function(){return n}});let n=(0,r(54832).createAsyncLocalStorage)();("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54832:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createAsyncLocalStorage",{enumerable:!0,get:function(){return l}});let r=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class n{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}}let o=globalThis.AsyncLocalStorage;function l(){return o?new o:new n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);