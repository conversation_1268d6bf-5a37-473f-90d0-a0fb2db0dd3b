(()=>{var e={};e.id=5212,e.ids=[5212],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},56614:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(21042),t(41783),t(12523);var r=t(23191),a=t(88716),i=t(37922),o=t.n(i),l=t(95231),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(s,n);let d=["",{children:["test-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21042)),"E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,41783)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\page.tsx"],m="/test-auth/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-auth/page",pathname:"/test-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9332:(e,s,t)=>{Promise.resolve().then(t.bind(t,32457))},32457:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.d(s,{default:()=>m});var a=t(10326),i=t(17577),o=t(35047),l=t(74723),n=t(75290),d=t(68897),c=e([d]);d=(c.then?(await c)():c)[0];let m=({mode:e,redirectUrl:s="/"})=>{let t=(0,o.useRouter)(),{refreshCustomer:r}=(0,d.O)(),[c,m]=(0,i.useState)(!1),[u,x]=(0,i.useState)(null),[p,h]=(0,i.useState)(null),[g,b]=(0,i.useState)(null),f="login"===e,{register:w,handleSubmit:N,watch:j,formState:{errors:y}}=(0,l.cI)({mode:"onBlur"}),v=j("password",""),k=async e=>{m(!0),x(null),h(null),b(null);try{if(f){console.log("Attempting login with:",e.email);let a=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e.email,password:e.password})}),i=await a.json();i.success?(h("Login successful! Redirecting..."),setTimeout(async()=>{await r(),t.push(s),t.refresh()},500)):x(i.message||"Login failed. Please check your credentials.")}else{console.log("Attempting registration for:",e.email);let a=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password})}),i=await a.json();i.success?(h("Registration successful! Redirecting..."),await r(),setTimeout(()=>{t.push(s),t.refresh()},1e3)):x(i.message||"Registration failed. Please try again.")}}catch(e){console.error("Authentication error:",e),x(e.message||"An error occurred during authentication"),h(null)}finally{m(!1)}};return(0,a.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[a.jsx("h2",{className:"text-2xl font-serif mb-6 text-center",children:f?"Sign In to Your Account":"Create an Account"}),u&&a.jsx("div",{className:"mb-4 p-3 bg-red-50 text-red-700 text-sm border border-red-200",children:u}),p&&a.jsx("div",{className:"mb-4 p-3 bg-green-50 text-green-700 text-sm border border-green-200",children:p}),g&&!1,(0,a.jsxs)("form",{onSubmit:N(k),className:"space-y-4",children:[!f&&a.jsx(a.Fragment,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),a.jsx("input",{id:"firstName",type:"text",className:`w-full p-2 border ${y.firstName?"border-red-500":"border-gray-300"}`,...w("firstName",{required:"First name is required"})}),y.firstName&&a.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.firstName.message})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),a.jsx("input",{id:"lastName",type:"text",className:`w-full p-2 border ${y.lastName?"border-red-500":"border-gray-300"}`,...w("lastName",{required:"Last name is required"})}),y.lastName&&a.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.lastName.message})]})]})}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),a.jsx("input",{id:"email",type:"email",className:`w-full p-2 border ${y.email?"border-red-500":"border-gray-300"}`,...w("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})}),y.email&&a.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.email.message})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),a.jsx("input",{id:"password",type:"password",className:`w-full p-2 border ${y.password?"border-red-500":"border-gray-300"}`,...w("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}})}),y.password&&a.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.password.message})]}),!f&&(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),a.jsx("input",{id:"confirmPassword",type:"password",className:`w-full p-2 border ${y.confirmPassword?"border-red-500":"border-gray-300"}`,...w("confirmPassword",{required:"Please confirm your password",validate:e=>e===v||"Passwords do not match"})}),y.confirmPassword&&a.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.confirmPassword.message})]}),a.jsx("button",{type:"submit",disabled:c,className:"w-full bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#4c4c47] transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed",children:c?(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[a.jsx(n.Z,{className:"animate-spin mr-2 h-4 w-4"}),f?"Signing in...":"Creating account..."]}):f?"Sign In":"Create Account"})]}),f?a.jsx("div",{className:"mt-4 text-center",children:a.jsx("a",{href:"/forgot-password",className:"text-sm text-[#2c2c27] hover:text-[#8a8778] underline",children:"Forgot your password?"})}):null]})};r()}catch(e){r(e)}})},21042:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510);t(71159);var a=t(55361);function i(){return(0,r.jsxs)("div",{className:"container mx-auto py-12",children:[r.jsx("h1",{className:"text-3xl font-bold mb-8 text-center",children:"WooCommerce Authentication Test"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"bg-white p-6 shadow-md",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Login Form"}),r.jsx(a.Z,{mode:"login",redirectUrl:"/test-auth/success"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 shadow-md",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Register Form"}),r.jsx(a.Z,{mode:"register",redirectUrl:"/test-auth/success"})]})]}),(0,r.jsxs)("div",{className:"mt-12 bg-gray-50 p-6 border border-gray-200",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Note"}),r.jsx("p",{children:"This page is used to test the WooCommerce authentication with Next.js 14's Server Components architecture."}),r.jsx("p",{className:"mt-2",children:"We've separated the client and server authentication logic to make it work with Next.js 14."})]})]})}},55361:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\auth\AuthForm.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,7499,5010,1651],()=>t(56614));module.exports=r})();