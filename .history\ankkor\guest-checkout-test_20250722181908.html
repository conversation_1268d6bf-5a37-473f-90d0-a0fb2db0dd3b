
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WooCommerce Guest Checkout Test</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    h1 { color: #7f54b3; }
    .test-group { margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; border-radius: 4px; }
    h2 { margin-top: 0; color: #2c3e50; }
    button { background-color: #7f54b3; color: white; border: none; padding: 10px 15px; margin: 5px 0; cursor: pointer; border-radius: 4px; }
    button:hover { background-color: #6b4a99; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    .note { background-color: #fffbea; padding: 10px; border-left: 4px solid #f0c674; margin-top: 20px; }
  </style>
</head>
<body>
  <h1>WooCommerce Guest Checkout Test</h1>
  <p>This page helps you test different approaches for guest checkout. Click each button to try a different method.</p>
  
  <div class="test-group">
    <h2>Test 1: Direct Guest Checkout URL</h2>
    <button onclick="window.open('https://deepskyblue-penguin-370791.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1')">
      Test Guest Checkout Parameters
    </button>
    <p>This test uses URL parameters to force guest checkout.</p>
  </div>

  <div class="test-group">
    <h2>Test 2: Custom API Endpoints</h2>
    <button onclick="window.open('https://deepskyblue-penguin-370791.hostingersite.com/wp-json/ankkor/v1/guest-checkout')">
      Test /guest-checkout API
    </button>
    <button onclick="testFixCheckout()">
      Test /fix-checkout API
    </button>
    <p>These tests check if the custom API endpoints are working.</p>
  </div>

  <div class="test-group">
    <h2>Test 3: Add Product & Checkout</h2>
    <button onclick="window.open('https://deepskyblue-penguin-370791.hostingersite.com/shop')">
      Go to Shop
    </button>
    <p>Go to the shop, add a product to cart, and proceed to checkout.</p>
  </div>

  <div class="note">
    <strong>Important:</strong> For best testing, use an incognito/private browser window to ensure you're not already logged in.
  </div>

  <div class="test-group">
    <h2>Troubleshooting</h2>
    <p>If you're still having issues:</p>
    <ol>
      <li>Make sure both plugins are properly activated</li>
      <li>Clear all caches (WordPress, browser, CDN)</li>
      <li>Check WooCommerce settings at https://deepskyblue-penguin-370791.hostingersite.com/wp-admin/admin.php?page=wc-settings&tab=account</li>
      <li>Check the browser console for any errors</li>
    </ol>
  </div>

  <script>
    async function testFixCheckout() {
      try {
        const response = await fetch('https://deepskyblue-penguin-370791.hostingersite.com/wp-json/ankkor/v1/fix-checkout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ force_guest_checkout: true })
        });
        
        const result = await response.json();
        alert('API Response: ' + JSON.stringify(result));
        
        if (result.success) {
          window.open('https://deepskyblue-penguin-370791.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1');
        }
      } catch (error) {
        alert('Error testing fix-checkout API: ' + error.message);
      }
    }
  </script>
</body>
</html>
  