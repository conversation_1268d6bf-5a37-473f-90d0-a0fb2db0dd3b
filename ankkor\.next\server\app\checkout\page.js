(()=>{var e={};e.id=285,e.ids=[285],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},70117:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>l}),r(51324),r(41783),r(12523);var t=r(23191),i=r(88716),a=r(37922),n=r.n(a),d=r(95231),o={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);r.d(s,o);let l=["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,51324)),"E:\\ankkorwoo\\ankkor\\src\\app\\checkout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,41783)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\checkout\\page.tsx"],p="/checkout/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91860:(e,s,r)=>{Promise.resolve().then(r.bind(r,37758))},28916:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},14228:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Truck",[["path",{d:"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11",key:"hs4xqm"}],["path",{d:"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2",key:"11fp61"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}]])},37758:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{default:()=>v});var i=r(10326),a=r(17577),n=r(35047),d=r(74723),o=r(86806),l=r(26017),c=r(68897),p=r(42434),m=r(91664),h=r(41190),u=r(27299),g=r(75290),x=r(14228),f=r(28916),y=e([c]);function v(){let e=(0,n.useRouter)(),{isAuthenticated:s,isLoading:r}=(0,c.O)(),t=(0,o.rY)(),y=(0,l.p)(),[v,j]=(0,a.useState)(!1),{register:b,handleSubmit:N,watch:w,formState:{errors:P}}=(0,d.cI)(),k=w("pincode"),S=async e=>{let s={firstName:e.firstName,lastName:e.lastName,address1:e.address1,address2:e.address2,city:e.city,state:e.state,pincode:e.pincode,phone:e.phone};y.setShippingAddress(s)},_=async()=>{if(!y.shippingAddress){y.setError("Please fill in your shipping address");return}if(!y.selectedShipping){y.setError("Please select a shipping method");return}if(0===y.cart.length){y.setError("Your cart is empty");return}if(y.finalAmount<=0){y.setError("Invalid order amount");return}j(!0),y.setProcessingPayment(!0),y.setError(null);try{let s="rzp_live_H1Iyl4j48eSFYj";if(!s)throw Error("Payment gateway not configured. Please contact support.");console.log("Creating Razorpay order for amount:",y.finalAmount);let r=await (0,p.wi)(y.finalAmount,`order_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,{customer_phone:y.shippingAddress.phone,customer_name:`${y.shippingAddress.firstName} ${y.shippingAddress.lastName}`,shipping_method:y.selectedShipping.name});console.log("Razorpay order created:",r.id),await (0,p.Jr)({key:s,amount:r.amount,currency:r.currency,name:"Ankkor",description:`Order Payment - ${y.cart.length} item(s)`,order_id:r.id,handler:async s=>{console.log("Payment successful, verifying...",s),y.setError(null);try{let r=await (0,p.t6)(s,{address:y.shippingAddress,cartItems:y.cart,shipping:y.selectedShipping});if(console.log("Payment verification result:",r),r.success)t.clearCart(),y.clearCheckout(),e.push(`/order-confirmed?id=${r.orderId}`);else throw Error(r.message||"Payment verification failed")}catch(e){console.error("Payment verification error:",e),y.setError(e instanceof Error?e.message:"Payment verification failed. Please contact support if amount was deducted.")}finally{j(!1),y.setProcessingPayment(!1)}},prefill:{name:`${y.shippingAddress.firstName} ${y.shippingAddress.lastName}`,contact:y.shippingAddress.phone},theme:{color:"#2c2c27"},modal:{ondismiss:()=>{console.log("Payment modal dismissed"),j(!1),y.setProcessingPayment(!1)}}})}catch(s){console.error("Payment error:",s);let e="Payment failed. Please try again.";s.message?.includes("not configured")?e=s.message:s.message?.includes("network")||s.message?.includes("fetch")?e="Network error. Please check your connection and try again.":s.message?.includes("amount")?e="Invalid amount. Please refresh and try again.":s.message&&(e=s.message),y.setError(e)}finally{j(!1),y.setProcessingPayment(!1)}};return r?i.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[i.jsx(g.Z,{className:"h-8 w-8 animate-spin"}),i.jsx("span",{className:"ml-2",children:"Loading..."})]})}):s&&0!==t.items.length?(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[i.jsx("h1",{className:"text-3xl font-serif mb-8",children:"Checkout"}),y.error&&i.jsx("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded",children:y.error}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[i.jsx(x.Z,{className:"mr-2 h-5 w-5"}),"Shipping Address"]}),(0,i.jsxs)("form",{onSubmit:N(S),className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"firstName",children:"First Name"}),i.jsx(h.I,{id:"firstName",...b("firstName",{required:"First name is required"}),className:P.firstName?"border-red-300":""}),P.firstName&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:P.firstName.message})]}),(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"lastName",children:"Last Name"}),i.jsx(h.I,{id:"lastName",...b("lastName",{required:"Last name is required"}),className:P.lastName?"border-red-300":""}),P.lastName&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:P.lastName.message})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[i.jsx(u._,{htmlFor:"address1",children:"Address Line 1"}),i.jsx(h.I,{id:"address1",...b("address1",{required:"Address is required"}),className:P.address1?"border-red-300":""}),P.address1&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:P.address1.message})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[i.jsx(u._,{htmlFor:"address2",children:"Address Line 2 (Optional)"}),i.jsx(h.I,{id:"address2",...b("address2")})]}),(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"city",children:"City"}),i.jsx(h.I,{id:"city",...b("city",{required:"City is required"}),className:P.city?"border-red-300":""}),P.city&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:P.city.message})]}),(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"state",children:"State"}),i.jsx(h.I,{id:"state",...b("state",{required:"State is required"}),className:P.state?"border-red-300":""}),P.state&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:P.state.message})]}),(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"pincode",children:"Pincode"}),i.jsx(h.I,{id:"pincode",...b("pincode",{required:"Pincode is required",pattern:{value:/^[0-9]{6}$/,message:"Please enter a valid 6-digit pincode"}}),className:P.pincode?"border-red-300":"",placeholder:"Enter 6-digit pincode"}),P.pincode&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:P.pincode.message})]}),(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"phone",children:"Phone Number"}),i.jsx(h.I,{id:"phone",...b("phone",{required:"Phone number is required"}),className:P.phone?"border-red-300":""}),P.phone&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:P.phone.message})]})]}),i.jsx(m.z,{type:"submit",className:"mt-4 w-full bg-[#2c2c27] hover:bg-[#3c3c37] text-white",children:"Save Address & Continue"})]})]}),(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[i.jsx(x.Z,{className:"mr-2 h-5 w-5"}),"Shipping Options"]}),y.isLoadingShipping?(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[i.jsx(g.Z,{className:"h-6 w-6 animate-spin mr-2"}),i.jsx("span",{children:"Loading shipping options..."})]}):0===y.shippingOptions.length?i.jsx("div",{className:"text-gray-500 py-4",children:k&&6===k.length?"No shipping options available for this pincode":"Enter a valid pincode to see shipping options"}):i.jsx("div",{className:"space-y-3",children:y.shippingOptions.map(e=>i.jsx("div",{className:`border rounded-lg p-4 cursor-pointer transition-colors ${y.selectedShipping?.id===e.id?"border-[#2c2c27] bg-gray-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>y.setSelectedShipping(e),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[i.jsx("div",{children:(0,i.jsxs)("div",{className:"flex items-center",children:[i.jsx("input",{type:"radio",name:"shipping",checked:y.selectedShipping?.id===e.id,onChange:()=>y.setSelectedShipping(e),className:"mr-3"}),(0,i.jsxs)("div",{children:[i.jsx("h3",{className:"font-medium",children:e.name}),e.description&&i.jsx("p",{className:"text-sm text-gray-600",children:e.description}),e.estimatedDays&&(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["Estimated delivery: ",e.estimatedDays]})]})]})}),i.jsx("div",{className:"text-lg font-medium",children:0===e.cost?"Free":`₹${e.cost.toFixed(2)}`})]})},e.id))})]}),(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[i.jsx(f.Z,{className:"mr-2 h-5 w-5"}),"Payment"]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center p-4 border rounded-lg",children:[i.jsx("input",{type:"radio",id:"razorpay",name:"payment",checked:!0,readOnly:!0,className:"mr-3"}),(0,i.jsxs)("div",{children:[i.jsx("label",{htmlFor:"razorpay",className:"font-medium",children:"Razorpay"}),i.jsx("p",{className:"text-sm text-gray-600",children:"Pay securely with credit card, debit card, UPI, or net banking"})]})]}),i.jsx(m.z,{onClick:_,className:"w-full py-6 bg-[#2c2c27] hover:bg-[#3c3c37] text-white",disabled:v||!y.shippingAddress||!y.selectedShipping||y.isProcessingPayment,children:v||y.isProcessingPayment?(0,i.jsxs)(i.Fragment,{children:[i.jsx(g.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing Payment..."]}):`Proceed to Pay - ₹${y.finalAmount.toFixed(2)}`})]})]})]}),i.jsx("div",{className:"lg:col-span-1",children:(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm sticky top-8",children:[i.jsx("h2",{className:"text-xl font-medium mb-4",children:"Order Summary"}),(0,i.jsxs)("div",{className:"space-y-4",children:[y.cart.map(e=>(0,i.jsxs)("div",{className:"flex gap-4 py-2 border-b",children:[e.image?.url&&i.jsx("div",{className:"relative h-16 w-16 bg-gray-100 flex-shrink-0",children:i.jsx("img",{src:e.image.url,alt:e.name,className:"h-full w-full object-cover rounded"})}),(0,i.jsxs)("div",{className:"flex-1",children:[i.jsx("h3",{className:"text-sm font-medium",children:e.name}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["₹","string"==typeof e.price?parseFloat(e.price).toFixed(2):e.price.toFixed(2)," \xd7 ",e.quantity]})]}),(0,i.jsxs)("div",{className:"text-right",children:["₹",("string"==typeof e.price?parseFloat(e.price)*e.quantity:e.price*e.quantity).toFixed(2)]})]},e.id)),(0,i.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-gray-600",children:"Subtotal"}),(0,i.jsxs)("span",{children:["₹",y.subtotal.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-gray-600",children:"Shipping"}),i.jsx("span",{children:y.selectedShipping?0===y.selectedShipping.cost?"Free":`₹${y.selectedShipping.cost.toFixed(2)}`:"TBD"})]}),(0,i.jsxs)("div",{className:"flex justify-between text-lg font-medium pt-2 border-t",children:[i.jsx("span",{children:"Total"}),(0,i.jsxs)("span",{children:["₹",y.finalAmount.toFixed(2)]})]})]})]})]})})]})]}):null}c=(y.then?(await y)():y)[0],t()}catch(e){t(e)}})},91664:(e,s,r)=>{"use strict";r.d(s,{z:()=>o});var t=r(10326);r(17577);var i=r(34214),a=r(79360),n=r(51223);let d=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:s,size:r,asChild:a=!1,...o}){let l=a?i.g7:"button";return t.jsx(l,{"data-slot":"button",className:(0,n.cn)(d({variant:s,size:r,className:e})),...o})}},41190:(e,s,r)=>{"use strict";r.d(s,{I:()=>n});var t=r(10326),i=r(17577),a=r(51223);let n=i.forwardRef(({className:e,type:s,...r},i)=>t.jsx("input",{type:s,"data-slot":"input",className:(0,a.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",e),ref:i,...r}));n.displayName="Input"},27299:(e,s,r)=>{"use strict";r.d(s,{_:()=>o});var t=r(10326),i=r(17577),a=r(45226),n=i.forwardRef((e,s)=>(0,t.jsx)(a.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var d=r(51223);function o({className:e,...s}){return t.jsx(n,{"data-slot":"label",className:(0,d.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},26017:(e,s,r)=>{"use strict";r.d(s,{p:()=>n});var t=r(60114),i=r(85251),a=r(42434);let n=(0,t.Ue)()((0,i.tJ)((e,s)=>({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null,setCart:r=>{let t=r.reduce((e,s)=>e+("string"==typeof s.price?parseFloat(s.price):s.price)*s.quantity,0),{shippingCost:i}=s();e({cart:r,subtotal:t,finalAmount:t+i})},setShippingAddress:s=>{e({shippingAddress:s})},fetchShippingRates:async r=>{let{cart:t,subtotal:i}=s();if(!r||r.length<6){e({error:"Please enter a valid pincode"});return}e({isLoadingShipping:!0,error:null});try{let s=await (0,a.Mq)(r,t);e({shippingOptions:s,isLoadingShipping:!1,selectedShipping:null,shippingCost:0,finalAmount:i+0})}catch(s){console.error("Error fetching shipping rates:",s),e({error:s instanceof Error?s.message:"Failed to fetch shipping rates",isLoadingShipping:!1,shippingOptions:[]})}},setSelectedShipping:r=>{let{subtotal:t}=s(),i=t+r.cost;e({selectedShipping:r,shippingCost:r.cost,finalAmount:i})},calculateFinalAmount:()=>{let{subtotal:r,shippingCost:t,finalAmount:i}=s(),a=r+t;a!==i&&e({finalAmount:a})},setError:s=>{e({error:s})},setProcessingPayment:s=>{e({isProcessingPayment:s})},clearCheckout:()=>{e({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null})}}),{name:"checkout-storage",partialize:e=>({shippingAddress:e.shippingAddress,selectedShipping:e.selectedShipping})}))},42434:(e,s,r)=>{"use strict";r.d(s,{Jr:()=>a,Mq:()=>n,t6:()=>i,wi:()=>t});let t=async(e,s,r={})=>{try{if(!e||e<=0)throw Error("Invalid amount");if(e<1)throw Error("Minimum order amount is ₹1");console.log("Creating Razorpay order:",{amount:e,receipt:s,notes:r});let t=await fetch("/api/razorpay/create-order",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:Math.round(100*e),receipt:s,notes:r})});if(!t.ok){let e=await t.json();if(console.error("Razorpay order creation failed:",e),400===t.status)throw Error(e.error||"Invalid order data");if(500===t.status)throw Error("Payment gateway error. Please try again.");throw Error(e.error||"Failed to create payment order")}let i=await t.json();return console.log("Razorpay order created successfully:",i.id),i}catch(e){if(console.error("Error creating Razorpay order:",e),e instanceof Error)throw e;throw Error("Failed to create payment order")}},i=async(e,s)=>{try{let r=await fetch("/api/razorpay/verify-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({razorpay_payment_id:e.razorpay_payment_id,razorpay_order_id:e.razorpay_order_id,razorpay_signature:e.razorpay_signature,address:s.address,cartItems:s.cartItems,shipping:s.shipping})});if(!r.ok){let e=await r.json();throw Error(e.message||"Payment verification failed")}return await r.json()}catch(e){throw console.error("Error verifying payment:",e),e}},a=e=>new Promise((e,s)=>{try{s(Error("Razorpay SDK not loaded"));return}catch(e){console.error("Error initializing Razorpay:",e),s(e)}}),n=async(e,s)=>{try{let r=await fetch("/api/shipping-rates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pincode:e,cartItems:s})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to get shipping rates")}return await r.json()}catch(e){throw console.error("Error getting shipping rates:",e),e}}},51223:(e,s,r)=>{"use strict";r.d(s,{cn:()=>a});var t=r(41135),i=r(31009);function a(...e){return(0,i.m6)((0,t.W)(e))}},51324:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\checkout\page.tsx#default`)},45226:(e,s,r)=>{"use strict";r.d(s,{WV:()=>n});var t=r(17577);r(60962);var i=r(34214),a=r(10326),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,s)=>{let r=t.forwardRef((e,r)=>{let{asChild:t,...n}=e,d=t?i.g7:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(d,{...n,ref:r})});return r.displayName=`Primitive.${s}`,{...e,[s]:r}},{})}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,7499,2325,5010,1651],()=>r(70117));module.exports=t})();