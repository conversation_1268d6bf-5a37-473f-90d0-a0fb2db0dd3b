# WooCommerce Guest Checkout Fix

## Quick Installation Guide

This guide will help you fix the WooCommerce guest checkout redirection issue where users are redirected to the login page instead of proceeding to checkout.

### Step 1: Install the Plugin on Word<PERSON>ress

Choose one of these options:

#### Option A: Full Plugin (Recommended)

1. Create a folder named `ankkor-woo-guest-checkout` in your WordPress plugins directory:
   ```
   /wp-content/plugins/ankkor-woo-guest-checkout/
   ```

2. Upload these files to the folder:
   - `ankkor-woo-guest-checkout.php` (main plugin file)
   - `includes/class-ankkor-woo-guest-checkout.php`
   - `includes/class-ankkor-woo-api-handler.php`
   - `README.md`

3. Log in to your WordPress admin dashboard
4. Go to Plugins > Installed Plugins
5. Find "Ankkor WooCommerce Guest Checkout" and click "Activate"

#### Option B: Simple Plugin

If you prefer a simpler solution:

1. Upload `force-guest-checkout-simple.php` to your WordPress plugins directory:
   ```
   /wp-content/plugins/force-guest-checkout-simple.php
   ```

2. Log in to your WordPress admin dashboard
3. Go to Plugins > Installed Plugins
4. Find "Ankkor Force Guest Checkout Simple" and click "Activate"

### Step 2: Configure WooCommerce Settings

1. Go to WooCommerce > Settings > Accounts & Privacy
2. Make sure "Allow customers to place orders without an account" is checked
3. Click "Save changes"

### Step 3: Clear All Caches

1. Clear WordPress cache
2. Clear browser cache and cookies
3. Clear WooCommerce cache
4. If using a caching plugin (WP Super Cache, W3 Total Cache, etc.), purge all caches
5. If using a CDN, purge CDN cache

### Step 4: Test the Fix

1. Open a private/incognito browser window
2. Visit your store and add a product to cart
3. Proceed to checkout
4. Verify you can see the checkout form without being asked to log in

## Troubleshooting

If you're still experiencing issues:

1. **Check for plugin conflicts**:
   - Temporarily deactivate other plugins that might affect checkout
   - Especially security plugins, membership plugins, and checkout customization plugins

2. **Try direct URL access**:
   Open this URL directly (replace with your domain):
   ```
   https://your-domain.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1
   ```

3. **Check server logs**:
   - WordPress debug log
   - WooCommerce logs (WooCommerce > Status > Logs)
   - Server error logs

4. **Add debug code to your theme's functions.php**:
   ```php
   add_action('init', function() {
     error_log('WooCommerce Guest Checkout Debug: ' . 
       'Guest Checkout Enabled: ' . get_option('woocommerce_enable_guest_checkout') . ', ' .
       'Login Reminder: ' . get_option('woocommerce_enable_checkout_login_reminder') . ', ' .
       'Current User: ' . (is_user_logged_in() ? 'Logged In' : 'Guest')
     );
   });
   ```

## What This Fix Does

1. Disables admin access prevention for checkout and API requests
2. Modifies checkout URLs to include guest checkout parameters
3. Disables checkout login requirements
4. Prevents admin redirects for checkout pages
5. Adds CORS headers for API requests
6. Provides custom REST API endpoints for guest checkout

For more detailed information, see the plugin's README.md file. 