"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9290],{60282:function(t,e,i){i.d(e,{l:function(){return T}});var s=i(96674),r=i(34006),n=i(75957),a=i(14004),o=i(34081),h=i(3078),l=i(39593),u=i(23999),p=i(8834),d=i(17743),c=i(74115),v=i(72575),V=i(31297),g=i(35938),m=i(72589),f=i(37003),C=i(58345);let y=Object.keys(g.A),M=y.length,b=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],S=m.V.length;class T{constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,visualState:r},n={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>C.Wi.render(this.render,!1,!0);let{latestValues:a,renderState:o}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=o,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=n,this.isControllingVariants=(0,d.G)(e),this.isVariantNode=(0,d.M)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...p}=this.scrapeMotionValuesFromProps(e,{});for(let t in p){let e=p[t];void 0!==a[t]&&(0,u.i)(e)&&(e.set(a[t],!1),(0,l.L)(h)&&h.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,f.R.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),a.O.current||(0,n.A)(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||a.n.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in f.R.delete(this.current),this.projection&&this.projection.unmount(),(0,C.Pn)(this.notifyUpdate),(0,C.Pn)(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let i=p.G.has(t),s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&C.Wi.update(this.notifyUpdate,!1,!0),i&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{s(),r()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},i,s,n){let a,o;for(let t=0;t<M;t++){let i=y[t],{isEnabled:s,Feature:r,ProjectionNode:n,MeasureLayout:h}=g.A[i];n&&(a=n),s(e)&&(!this.features[i]&&r&&(this.features[i]=new r(this)),h&&(o=h))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&a){this.projection=new a(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:i,drag:s,dragConstraints:o,layoutScroll:h,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:i,alwaysMeasureLayout:!!s||o&&(0,r.I)(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof i?i:"both",initialPromotionConfig:n,layoutScroll:h,layoutRoot:l})}return o}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,s.dO)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<b.length;e++){let i=b[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=(0,v.F)(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<S;t++){let i=m.V[t],s=this.props[i];((0,c.$)(s)||!1===s)&&(e[i]=s)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,h.BX)(e,{owner:this}),this.addValue(t,i)),i}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:i}=this.props,s="string"==typeof i||"object"==typeof i?null===(e=(0,V.o)(this.props,i))||void 0===e?void 0:e[t]:void 0;if(i&&void 0!==s)return s;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||(0,u.i)(r)?void 0!==this.initialValues[t]&&void 0===s?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new o.L),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}}}]);