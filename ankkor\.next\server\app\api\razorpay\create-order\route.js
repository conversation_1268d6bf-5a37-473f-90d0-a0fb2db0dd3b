(()=>{var e={};e.id=1379,e.ids=[1379],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},63734:(e,a,n)=>{"use strict";n.r(a),n.d(a,{originalPathname:()=>m,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>d,staticGenerationAsyncStorage:()=>u});var i={};n.r(i),n.d(i,{POST:()=>c});var t=n(49303),o=n(88716),s=n(60670),r=n(87070);async function c(e){try{let{amount:a,receipt:i,notes:t={}}=await e.json();if(!a||!i)return r.NextResponse.json({error:"Amount and receipt are required"},{status:400});if("number"!=typeof a||a<100)return r.NextResponse.json({error:"Invalid amount"},{status:400});let o="rzp_live_H1Iyl4j48eSFYj",s=process.env.RAZORPAY_KEY_SECRET;if(!o||!s)return console.error("Razorpay credentials not configured"),r.NextResponse.json({error:"Payment gateway not configured"},{status:500});let c=new(n(41212))({key_id:o,key_secret:s}),p=await c.orders.create({amount:a,currency:"INR",receipt:i,notes:t,payment_capture:1});return console.log("Razorpay order created:",p.id),r.NextResponse.json(p)}catch(e){if(console.error("Razorpay order creation error:",e),e.statusCode)return r.NextResponse.json({error:e.error?.description||"Razorpay API error",code:e.error?.code},{status:e.statusCode});return r.NextResponse.json({error:"Failed to create Razorpay order"},{status:500})}}let p=new t.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/razorpay/create-order/route",pathname:"/api/razorpay/create-order",filename:"route",bundlePath:"app/api/razorpay/create-order/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\razorpay\\create-order\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:l,staticGenerationAsyncStorage:u,serverHooks:d}=p,m="/api/razorpay/create-order/route";function f(){return(0,s.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:u})}},16811:(e,a,n)=>{e.exports={parallel:n(65903),serial:n(11610),serialOrdered:n(37854)}},45961:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}};function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},96446:(e,a,n)=>{var i=n(10516);e.exports=function(e){var a=!1;return i(function(){a=!0}),function(n,t){a?e(n,t):i(function(){e(n,t)})}}},10516:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},39262:(e,a,n)=>{var i=n(96446),t=n(45961);e.exports=function(e,a,n,o){var s,r,c=n.keyedList?n.keyedList[n.index]:n.index;n.jobs[c]=(s=e[c],r=function(e,a){c in n.jobs&&(delete n.jobs[c],e?t(n):n.results[c]=a,o(e,n.results))},2==a.length?a(s,i(r)):a(s,c,i(r)))}},66924:e=>{e.exports=function(e,a){var n=!Array.isArray(e),i={index:0,keyedList:n||a?Object.keys(e):null,jobs:{},results:n?{}:[],size:n?Object.keys(e).length:e.length};return a&&i.keyedList.sort(n?a:function(n,i){return a(e[n],e[i])}),i}},3528:(e,a,n)=>{var i=n(45961),t=n(96446);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,i(this),t(e)(null,this.results))}},65903:(e,a,n)=>{var i=n(39262),t=n(66924),o=n(3528);e.exports=function(e,a,n){for(var s=t(e);s.index<(s.keyedList||e).length;)i(e,a,s,function(e,a){if(e){n(e,a);return}if(0===Object.keys(s.jobs).length){n(null,s.results);return}}),s.index++;return o.bind(s,n)}},11610:(e,a,n)=>{var i=n(37854);e.exports=function(e,a,n){return i(e,a,null,n)}},37854:(e,a,n)=>{var i=n(39262),t=n(66924),o=n(3528);function s(e,a){return e<a?-1:e>a?1:0}e.exports=function(e,a,n,s){var r=t(e,n);return i(e,a,r,function n(t,o){if(t){s(t,o);return}if(r.index++,r.index<(r.keyedList||e).length){i(e,a,r,n);return}s(null,r.results)}),o.bind(r,s)},e.exports.ascending=s,e.exports.descending=function(e,a){return -1*s(e,a)}},81421:(e,a,n)=>{"use strict";var i=n(31660),t=n(34006),o=n(43135),s=n(53659);e.exports=s||i.call(o,t)},34006:e=>{"use strict";e.exports=Function.prototype.apply},43135:e=>{"use strict";e.exports=Function.prototype.call},602:(e,a,n)=>{"use strict";var i=n(31660),t=n(17445),o=n(43135),s=n(81421);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new t("a function is required");return s(i,o,e)}},53659:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},59130:(e,a,n)=>{var i=n(21764),t=n(76162).Stream,o=n(90932);function s(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=s,i.inherits(s,t),s.create=function(e){var a=new this;for(var n in e=e||{})a[n]=e[n];return a},s.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},s.prototype.append=function(e){if(s.isStreamLike(e)){if(!(e instanceof o)){var a=o.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},s.prototype.pipe=function(e,a){return t.prototype.pipe.call(this,e,a),this.resume(),e},s.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},s.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){s.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},s.prototype._pipeNext=function(e){if(this._currentStream=e,s.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},s.prototype._handleErrors=function(e){var a=this;e.on("error",function(e){a._emitError(e)})},s.prototype.write=function(e){this.emit("data",e)},s.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},s.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},s.prototype.end=function(){this._reset(),this.emit("end")},s.prototype.destroy=function(){this._reset(),this.emit("close")},s.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},s.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},s.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(a){a.dataSize&&(e.dataSize+=a.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},s.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},93050:(e,a,n)=>{a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let n="color: "+this.color;a.splice(1,0,n,"color: inherit");let i=0,t=0;a[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(i++,"%c"===e&&(t=i))}),a.splice(t,0,n)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=n(30783)(a);let{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},30783:(e,a,n)=>{e.exports=function(e){function a(e){let n,t,o;let s=null;function r(...e){if(!r.enabled)return;let i=Number(new Date),t=i-(n||i);r.diff=t,r.prev=n,r.curr=i,n=i,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(n,i)=>{if("%%"===n)return"%";o++;let t=a.formatters[i];if("function"==typeof t){let a=e[o];n=t.call(r,a),e.splice(o,1),o--}return n}),a.formatArgs.call(r,e),(r.log||a.log).apply(r,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=i,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(t!==a.namespaces&&(t=a.namespaces,o=a.enabled(e)),o),set:e=>{s=e}}),"function"==typeof a.init&&a.init(r),r}function i(e,n){let i=a(this.namespace+(void 0===n?":":n)+e);return i.log=this.log,i}function t(e,a){let n=0,i=0,t=-1,o=0;for(;n<e.length;)if(i<a.length&&(a[i]===e[n]||"*"===a[i]))"*"===a[i]?(t=i,o=n):n++,i++;else{if(-1===t)return!1;i=t+1,n=++o}for(;i<a.length&&"*"===a[i];)i++;return i===a.length}return a.debug=a,a.default=a,a.coerce=function(e){return e instanceof Error?e.stack||e.message:e},a.disable=function(){let e=[...a.names,...a.skips.map(e=>"-"+e)].join(",");return a.enable(""),e},a.enable=function(e){for(let n of(a.save(e),a.namespaces=e,a.names=[],a.skips=[],("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===n[0]?a.skips.push(n.slice(1)):a.names.push(n)},a.enabled=function(e){for(let n of a.skips)if(t(e,n))return!1;for(let n of a.names)if(t(e,n))return!0;return!1},a.humanize=n(13974),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(n=>{a[n]=e[n]}),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let n=0;for(let a=0;a<e.length;a++)n=(n<<5)-n+e.charCodeAt(a)|0;return a.colors[Math.abs(n)%a.colors.length]},a.enable(a.load()),a}},19092:(e,a,n)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=n(93050):e.exports=n(12226)},12226:(e,a,n)=>{let i=n(74175),t=n(21764);a.init=function(e){e.inspectOpts={};let n=Object.keys(a.inspectOpts);for(let i=0;i<n.length;i++)e.inspectOpts[n[i]]=a.inspectOpts[n[i]]},a.log=function(...e){return process.stderr.write(t.formatWithOptions(a.inspectOpts,...e)+"\n")},a.formatArgs=function(n){let{namespace:i,useColors:t}=this;if(t){let a=this.color,t="\x1b[3"+(a<8?a:"8;5;"+a),o=`  ${t};1m${i} \u001B[0m`;n[0]=o+n[0].split("\n").join("\n"+o),n.push(t+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else n[0]=(a.inspectOpts.hideDate?"":new Date().toISOString()+" ")+i+" "+n[0]},a.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},a.load=function(){return process.env.DEBUG},a.useColors=function(){return"colors"in a.inspectOpts?!!a.inspectOpts.colors:i.isatty(process.stderr.fd)},a.destroy=t.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),a.colors=[6,2,3,4,5,1];try{let e=n(67057);e&&(e.stderr||e).level>=2&&(a.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}a.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,a)=>{let n=a.substring(6).toLowerCase().replace(/_([a-z])/g,(e,a)=>a.toUpperCase()),i=process.env[a];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),e[n]=i,e},{}),e.exports=n(30783)(a);let{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,t.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,t.inspect(e,this.inspectOpts)}},90932:(e,a,n)=>{var i=n(76162).Stream,t=n(21764);function o(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=o,t.inherits(o,i),o.create=function(e,a){var n=new this;for(var i in a=a||{})n[i]=a[i];n.source=e;var t=e.emit;return e.emit=function(){return n._handleEmit(arguments),t.apply(e,arguments)},e.on("error",function(){}),n.pauseStream&&e.pause(),n},Object.defineProperty(o.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),o.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},o.prototype.resume=function(){this._released||this.release(),this.source.resume()},o.prototype.pause=function(){this.source.pause()},o.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},o.prototype.pipe=function(){var e=i.prototype.pipe.apply(this,arguments);return this.resume(),e},o.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},o.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},62344:(e,a,n)=>{"use strict";var i,t=n(602),o=n(86737);try{i=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var s=!!i&&o&&o(Object.prototype,"__proto__"),r=Object,c=r.getPrototypeOf;e.exports=s&&"function"==typeof s.get?t([s.get]):"function"==typeof c&&function(e){return c(null==e?e:r(e))}},70091:e=>{"use strict";var a=Object.defineProperty||!1;if(a)try{a({},"a",{value:1})}catch(e){a=!1}e.exports=a},86827:e=>{"use strict";e.exports=EvalError},56718:e=>{"use strict";e.exports=Error},37388:e=>{"use strict";e.exports=RangeError},63684:e=>{"use strict";e.exports=ReferenceError},31209:e=>{"use strict";e.exports=SyntaxError},17445:e=>{"use strict";e.exports=TypeError},76928:e=>{"use strict";e.exports=URIError},15678:e=>{"use strict";e.exports=Object},54265:(e,a,n)=>{"use strict";var i=n(2749)("%Object.defineProperty%",!0),t=n(23757)(),o=n(64995),s=n(17445),r=t?Symbol.toStringTag:null;e.exports=function(e,a){var n=arguments.length>2&&!!arguments[2]&&arguments[2].force,t=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==n&&"boolean"!=typeof n||void 0!==t&&"boolean"!=typeof t)throw new s("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");r&&(n||!o(e,r))&&(i?i(e,r,{configurable:!t,enumerable:!1,value:a,writable:!1}):e[r]=a)}},32291:(e,a,n)=>{"use strict";var i=n(59130),t=n(21764),o=n(55315),s=n(32615),r=n(35240),c=n(17360).parse,p=n(92048),l=n(76162).Stream,u=n(84770),d=n(89427),m=n(16811),f=n(54265),x=n(64995),v=n(9060);function h(e){if(!(this instanceof h))return new h(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],i.call(this),e=e||{})this[a]=e[a]}t.inherits(h,i),h.LINE_BREAK="\r\n",h.DEFAULT_CONTENT_TYPE="application/octet-stream",h.prototype.append=function(e,a,n){"string"==typeof(n=n||{})&&(n={filename:n});var t=i.prototype.append.bind(this);if(("number"==typeof a||null==a)&&(a=String(a)),Array.isArray(a)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,a,n),s=this._multiPartFooter();t(o),t(a),t(s),this._trackLength(o,a,n)},h.prototype._trackLength=function(e,a,n){var i=0;null!=n.knownLength?i+=Number(n.knownLength):Buffer.isBuffer(a)?i=a.length:"string"==typeof a&&(i=Buffer.byteLength(a)),this._valueLength+=i,this._overheadLength+=Buffer.byteLength(e)+h.LINE_BREAK.length,a&&(a.path||a.readable&&x(a,"httpVersion")||a instanceof l)&&(n.knownLength||this._valuesToMeasure.push(a))},h.prototype._lengthRetriever=function(e,a){x(e,"fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):p.stat(e.path,function(n,i){if(n){a(n);return}a(null,i.size-(e.start?e.start:0))}):x(e,"httpVersion")?a(null,Number(e.headers["content-length"])):x(e,"httpModule")?(e.on("response",function(n){e.pause(),a(null,Number(n.headers["content-length"]))}),e.resume()):a("Unknown stream")},h.prototype._multiPartHeader=function(e,a,n){if("string"==typeof n.header)return n.header;var i,t=this._getContentDisposition(a,n),o=this._getContentType(a,n),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(t||[]),"Content-Type":[].concat(o||[])};for(var c in"object"==typeof n.header&&v(r,n.header),r)if(x(r,c)){if(null==(i=r[c]))continue;Array.isArray(i)||(i=[i]),i.length&&(s+=c+": "+i.join("; ")+h.LINE_BREAK)}return"--"+this.getBoundary()+h.LINE_BREAK+s+h.LINE_BREAK},h.prototype._getContentDisposition=function(e,a){var n;if("string"==typeof a.filepath?n=o.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e&&(e.name||e.path)?n=o.basename(a.filename||e&&(e.name||e.path)):e&&e.readable&&x(e,"httpVersion")&&(n=o.basename(e.client._httpMessage.path||"")),n)return'filename="'+n+'"'},h.prototype._getContentType=function(e,a){var n=a.contentType;return!n&&e&&e.name&&(n=d.lookup(e.name)),!n&&e&&e.path&&(n=d.lookup(e.path)),!n&&e&&e.readable&&x(e,"httpVersion")&&(n=e.headers["content-type"]),!n&&(a.filepath||a.filename)&&(n=d.lookup(a.filepath||a.filename)),!n&&e&&"object"==typeof e&&(n=h.DEFAULT_CONTENT_TYPE),n},h.prototype._multiPartFooter=function(){return(function(e){var a=h.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},h.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+h.LINE_BREAK},h.prototype.getHeaders=function(e){var a,n={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)x(e,a)&&(n[a.toLowerCase()]=e[a]);return n},h.prototype.setBoundary=function(e){if("string"!=typeof e)throw TypeError("FormData boundary must be a string");this._boundary=e},h.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},h.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),n=0,i=this._streams.length;n<i;n++)"function"!=typeof this._streams[n]&&(Buffer.isBuffer(this._streams[n])?e=Buffer.concat([e,this._streams[n]]):e=Buffer.concat([e,Buffer.from(this._streams[n])]),("string"!=typeof this._streams[n]||this._streams[n].substring(2,a.length+2)!==a)&&(e=Buffer.concat([e,Buffer.from(h.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},h.prototype._generateBoundary=function(){this._boundary="--------------------------"+u.randomBytes(12).toString("hex")},h.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},h.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},h.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,a));return}m.parallel(this._valuesToMeasure,this._lengthRetriever,function(n,i){if(n){e(n);return}i.forEach(function(e){a+=e}),e(null,a)})},h.prototype.submit=function(e,a){var n,i,t={method:"post"};return"string"==typeof e?i=v({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},t):(i=v(e,t)).port||(i.port="https:"===i.protocol?443:80),i.headers=this.getHeaders(e.headers),n="https:"===i.protocol?r.request(i):s.request(i),this.getLength((function(e,i){if(e&&"Unknown stream"!==e){this._error(e);return}if(i&&n.setHeader("Content-Length",i),this.pipe(n),a){var t,o=function(e,i){return n.removeListener("error",o),n.removeListener("response",t),a.call(this,e,i)};t=o.bind(this,null),n.on("error",o),n.on("response",t)}}).bind(this)),n},h.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},h.prototype.toString=function(){return"[object FormData]"},f(h,"FormData"),e.exports=h},9060:e=>{"use strict";e.exports=function(e,a){return Object.keys(a).forEach(function(n){e[n]=e[n]||a[n]}),e}},49214:e=>{"use strict";var a=Object.prototype.toString,n=Math.max,i=function(e,a){for(var n=[],i=0;i<e.length;i+=1)n[i]=e[i];for(var t=0;t<a.length;t+=1)n[t+e.length]=a[t];return n},t=function(e,a){for(var n=[],i=a||0,t=0;i<e.length;i+=1,t+=1)n[t]=e[i];return n},o=function(e,a){for(var n="",i=0;i<e.length;i+=1)n+=e[i],i+1<e.length&&(n+=a);return n};e.exports=function(e){var s,r=this;if("function"!=typeof r||"[object Function]"!==a.apply(r))throw TypeError("Function.prototype.bind called on incompatible "+r);for(var c=t(arguments,1),p=n(0,r.length-c.length),l=[],u=0;u<p;u++)l[u]="$"+u;if(s=Function("binder","return function ("+o(l,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof s){var a=r.apply(this,i(c,arguments));return Object(a)===a?a:this}return r.apply(e,i(c,arguments))}),r.prototype){var d=function(){};d.prototype=r.prototype,s.prototype=new d,d.prototype=null}return s}},31660:(e,a,n)=>{"use strict";var i=n(49214);e.exports=Function.prototype.bind||i},2749:(e,a,n)=>{"use strict";var i,t=n(15678),o=n(56718),s=n(86827),r=n(37388),c=n(63684),p=n(31209),l=n(17445),u=n(76928),d=n(95175),m=n(22334),f=n(46082),x=n(40430),v=n(24210),h=n(70792),b=n(92615),g=Function,y=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},w=n(86737),k=n(70091),j=function(){throw new l},_=w?function(){try{return arguments.callee,j}catch(e){try{return w(arguments,"callee").get}catch(e){return j}}}():j,E=n(91976)(),O=n(93941),R=n(67209),S=n(32395),A=n(34006),C=n(43135),T={},P="undefined"!=typeof Uint8Array&&O?O(Uint8Array):i,z={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?i:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?i:ArrayBuffer,"%ArrayIteratorPrototype%":E&&O?O([][Symbol.iterator]()):i,"%AsyncFromSyncIteratorPrototype%":i,"%AsyncFunction%":T,"%AsyncGenerator%":T,"%AsyncGeneratorFunction%":T,"%AsyncIteratorPrototype%":T,"%Atomics%":"undefined"==typeof Atomics?i:Atomics,"%BigInt%":"undefined"==typeof BigInt?i:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?i:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?i:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?i:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":o,"%eval%":eval,"%EvalError%":s,"%Float16Array%":"undefined"==typeof Float16Array?i:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?i:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?i:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?i:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":T,"%Int8Array%":"undefined"==typeof Int8Array?i:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?i:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?i:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":E&&O?O(O([][Symbol.iterator]())):i,"%JSON%":"object"==typeof JSON?JSON:i,"%Map%":"undefined"==typeof Map?i:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&E&&O?O(new Map()[Symbol.iterator]()):i,"%Math%":Math,"%Number%":Number,"%Object%":t,"%Object.getOwnPropertyDescriptor%":w,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?i:Promise,"%Proxy%":"undefined"==typeof Proxy?i:Proxy,"%RangeError%":r,"%ReferenceError%":c,"%Reflect%":"undefined"==typeof Reflect?i:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?i:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&E&&O?O(new Set()[Symbol.iterator]()):i,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?i:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":E&&O?O(""[Symbol.iterator]()):i,"%Symbol%":E?Symbol:i,"%SyntaxError%":p,"%ThrowTypeError%":_,"%TypedArray%":P,"%TypeError%":l,"%Uint8Array%":"undefined"==typeof Uint8Array?i:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?i:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?i:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?i:Uint32Array,"%URIError%":u,"%WeakMap%":"undefined"==typeof WeakMap?i:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?i:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?i:WeakSet,"%Function.prototype.call%":C,"%Function.prototype.apply%":A,"%Object.defineProperty%":k,"%Object.getPrototypeOf%":R,"%Math.abs%":d,"%Math.floor%":m,"%Math.max%":f,"%Math.min%":x,"%Math.pow%":v,"%Math.round%":h,"%Math.sign%":b,"%Reflect.getPrototypeOf%":S};if(O)try{null.error}catch(e){var F=O(O(e));z["%Error.prototype%"]=F}var N=function e(a){var n;if("%AsyncFunction%"===a)n=y("async function () {}");else if("%GeneratorFunction%"===a)n=y("function* () {}");else if("%AsyncGeneratorFunction%"===a)n=y("async function* () {}");else if("%AsyncGenerator%"===a){var i=e("%AsyncGeneratorFunction%");i&&(n=i.prototype)}else if("%AsyncIteratorPrototype%"===a){var t=e("%AsyncGenerator%");t&&O&&(n=O(t.prototype))}return z[a]=n,n},U={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},q=n(31660),B=n(64995),L=q.call(C,Array.prototype.concat),I=q.call(A,Array.prototype.splice),D=q.call(C,String.prototype.replace),M=q.call(C,String.prototype.slice),H=q.call(C,RegExp.prototype.exec),W=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,G=/\\(\\)?/g,$=function(e){var a=M(e,0,1),n=M(e,-1);if("%"===a&&"%"!==n)throw new p("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==a)throw new p("invalid intrinsic syntax, expected opening `%`");var i=[];return D(e,W,function(e,a,n,t){i[i.length]=n?D(t,G,"$1"):a||e}),i},J=function(e,a){var n,i=e;if(B(U,i)&&(i="%"+(n=U[i])[0]+"%"),B(z,i)){var t=z[i];if(t===T&&(t=N(i)),void 0===t&&!a)throw new l("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:i,value:t}}throw new p("intrinsic "+e+" does not exist!")};e.exports=function(e,a){if("string"!=typeof e||0===e.length)throw new l("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof a)throw new l('"allowMissing" argument must be a boolean');if(null===H(/^%?[^%]*%?$/,e))throw new p("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=$(e),i=n.length>0?n[0]:"",t=J("%"+i+"%",a),o=t.name,s=t.value,r=!1,c=t.alias;c&&(i=c[0],I(n,L([0,1],c)));for(var u=1,d=!0;u<n.length;u+=1){var m=n[u],f=M(m,0,1),x=M(m,-1);if(('"'===f||"'"===f||"`"===f||'"'===x||"'"===x||"`"===x)&&f!==x)throw new p("property names with quotes must have matching quotes");if("constructor"!==m&&d||(r=!0),i+="."+m,B(z,o="%"+i+"%"))s=z[o];else if(null!=s){if(!(m in s)){if(!a)throw new l("base intrinsic for "+e+" exists, but the property is not available.");return}if(w&&u+1>=n.length){var v=w(s,m);s=(d=!!v)&&"get"in v&&!("originalValue"in v.get)?v.get:s[m]}else d=B(s,m),s=s[m];d&&!r&&(z[o]=s)}}return s}},67209:(e,a,n)=>{"use strict";var i=n(15678);e.exports=i.getPrototypeOf||null},32395:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},93941:(e,a,n)=>{"use strict";var i=n(32395),t=n(67209),o=n(62344);e.exports=i?function(e){return i(e)}:t?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return t(e)}:o?function(e){return o(e)}:null},62980:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},86737:(e,a,n)=>{"use strict";var i=n(62980);if(i)try{i([],"length")}catch(e){i=null}e.exports=i},72616:e=>{"use strict";e.exports=(e,a=process.argv)=>{let n=e.startsWith("-")?"":1===e.length?"-":"--",i=a.indexOf(n+e),t=a.indexOf("--");return -1!==i&&(-1===t||i<t)}},91976:(e,a,n)=>{"use strict";var i="undefined"!=typeof Symbol&&Symbol,t=n(12522);e.exports=function(){return"function"==typeof i&&"function"==typeof Symbol&&"symbol"==typeof i("foo")&&"symbol"==typeof Symbol("bar")&&t()}},12522:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},a=Symbol("test"),n=Object(a);if("string"==typeof a||"[object Symbol]"!==Object.prototype.toString.call(a)||"[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(var i in e[a]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var t=Object.getOwnPropertySymbols(e);if(1!==t.length||t[0]!==a||!Object.prototype.propertyIsEnumerable.call(e,a))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,a);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},23757:(e,a,n)=>{"use strict";var i=n(12522);e.exports=function(){return i()&&!!Symbol.toStringTag}},64995:(e,a,n)=>{"use strict";var i=Function.prototype.call,t=Object.prototype.hasOwnProperty,o=n(31660);e.exports=o.call(i,t)},95175:e=>{"use strict";e.exports=Math.abs},22334:e=>{"use strict";e.exports=Math.floor},61781:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},46082:e=>{"use strict";e.exports=Math.max},40430:e=>{"use strict";e.exports=Math.min},24210:e=>{"use strict";e.exports=Math.pow},70792:e=>{"use strict";e.exports=Math.round},92615:(e,a,n)=>{"use strict";var i=n(61781);e.exports=function(e){return i(e)||0===e?e:e<0?-1:1}},79587:(e,a,n)=>{e.exports=n(2753)},89427:(e,a,n)=>{"use strict";var i=n(79587),t=n(55315).extname,o=/^\s*([^;\s]*)(?:;|\s|$)/,s=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var a=o.exec(e),n=a&&i[a[1].toLowerCase()];return n&&n.charset?n.charset:!!(a&&s.test(a[1]))&&"UTF-8"}a.charset=r,a.charsets={lookup:r},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var n=-1===e.indexOf("/")?a.lookup(e):e;if(!n)return!1;if(-1===n.indexOf("charset")){var i=a.charset(n);i&&(n+="; charset="+i.toLowerCase())}return n},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var n=o.exec(e),i=n&&a.extensions[n[1].toLowerCase()];return!!i&&!!i.length&&i[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var n=t("x."+e).toLowerCase().substr(1);return!!n&&(a.types[n]||!1)},a.types=Object.create(null),function(e,a){var n=["nginx","apache",void 0,"iana"];Object.keys(i).forEach(function(t){var o=i[t],s=o.extensions;if(s&&s.length){e[t]=s;for(var r=0;r<s.length;r++){var c=s[r];if(a[c]){var p=n.indexOf(i[a[c]].source),l=n.indexOf(o.source);if("application/octet-stream"!==a[c]&&(p>l||p===l&&"application/"===a[c].substr(0,12)))continue}a[c]=t}}})}(a.extensions,a.types)},13974:e=>{function a(e,a,n,i){return Math.round(e/n)+" "+i+(a>=1.5*n?"s":"")}e.exports=function(e,n){n=n||{};var i,t,o=typeof e;if("string"===o&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(a){var n=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*n;case"weeks":case"week":case"w":return 6048e5*n;case"days":case"day":case"d":return 864e5*n;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*n;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*n;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}(e);if("number"===o&&isFinite(e))return n.long?(i=Math.abs(e))>=864e5?a(e,i,864e5,"day"):i>=36e5?a(e,i,36e5,"hour"):i>=6e4?a(e,i,6e4,"minute"):i>=1e3?a(e,i,1e3,"second"):e+" ms":(t=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":t>=36e5?Math.round(e/36e5)+"h":t>=6e4?Math.round(e/6e4)+"m":t>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},21180:(e,a,n)=>{"use strict";var i=n(17360).parse,t={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},o=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function s(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.getProxyForUrl=function(e){var a,n,r,c="string"==typeof e?i(e):e||{},p=c.protocol,l=c.host,u=c.port;if("string"!=typeof l||!l||"string"!=typeof p||(p=p.split(":",1)[0],a=l=l.replace(/:\d*$/,""),n=u=parseInt(u)||t[p]||0,!(!(r=(s("npm_config_no_proxy")||s("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var i=e.match(/^(.+):(\d+)$/),t=i?i[1]:e,s=i?parseInt(i[2]):0;return!!s&&s!==n||(/^[.*]/.test(t)?("*"===t.charAt(0)&&(t=t.slice(1)),!o.call(a,t)):a!==t)}))))return"";var d=s("npm_config_"+p+"_proxy")||s(p+"_proxy")||s("npm_config_proxy")||s("all_proxy");return d&&-1===d.indexOf("://")&&(d=p+"://"+d),d}},55599:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=function(){function e(e,a){for(var n=0;n<a.length;n++){var i=a[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(a,n,i){return n&&e(a.prototype,n),i&&e(a,i),a}}(),o=n(44554).default,s=n(70206),r=n(42090).isNonNullObject,c={"X-Razorpay-Account":"","Content-Type":"application/json"};function p(e){throw{statusCode:e.response.status,error:e.response.data.error}}var l=function(){function e(a){(function(e,a){if(!(e instanceof a))throw TypeError("Cannot call a class as a function")})(this,e),this.version="v1",this.rq=o.create(this._createConfig(a))}return t(e,[{key:"_createConfig",value:function(e){var a,n,t={baseURL:e.hostUrl,headers:Object.assign({"User-Agent":e.ua},(a=e.headers,n={},r(a)?Object.keys(a).reduce(function(e,n){return c.hasOwnProperty(n)&&(e[n]=a[n]),e},n):n))};return e.key_id&&e.key_secret&&(t.auth={username:e.key_id,password:e.key_secret}),e.oauthToken&&(t.headers=i({Authorization:"Bearer "+e.oauthToken},t.headers)),t}},{key:"getEntityUrl",value:function(e){return e.hasOwnProperty("version")?"/"+e.version+e.url:"/"+this.version+e.url}},{key:"get",value:function(e,a){return s(this.rq.get(this.getEntityUrl(e),{params:e.data}).catch(p),a)}},{key:"post",value:function(e,a){return s(this.rq.post(this.getEntityUrl(e),e.data).catch(p),a)}},{key:"postFormData",value:function(e,a){return s(this.rq.post(this.getEntityUrl(e),e.formData,{headers:{"Content-Type":"multipart/form-data"}}).catch(p),a)}},{key:"put",value:function(e,a){return s(this.rq.put(this.getEntityUrl(e),e.data).catch(p),a)}},{key:"patch",value:function(e,a){return s(this.rq.patch(this.getEntityUrl(e),e.data).catch(p),a)}},{key:"delete",value:function(e,a){return s(this.rq.delete(this.getEntityUrl(e)).catch(p),a)}}]),e}();e.exports=l},41212:(e,a,n)=>{"use strict";var i=function(){function e(e,a){for(var n=0;n<a.length;n++){var i=a[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(a,n,i){return n&&e(a.prototype,n),i&&e(a,i),a}}(),t=n(55599),o=n(74667),s=n(42090).validateWebhookSignature,r=function(){function e(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(e,a){if(!(e instanceof a))throw TypeError("Cannot call a class as a function")}(this,e);var n=a.key_id,i=a.key_secret,o=a.oauthToken,s=a.headers;if(!n&&!o)throw Error("`key_id` or `oauthToken` is mandatory");this.key_id=n,this.key_secret=i,this.oauthToken=o,this.api=new t({hostUrl:"https://api.razorpay.com",ua:"razorpay-node@"+e.VERSION,key_id:n,key_secret:i,headers:s,oauthToken:o}),this.addResources()}return i(e,null,[{key:"validateWebhookSignature",value:function(){return s.apply(void 0,arguments)}}]),i(e,[{key:"addResources",value:function(){Object.assign(this,{accounts:n(16610)(this.api),stakeholders:n(27996)(this.api),payments:n(83449)(this.api),refunds:n(23011)(this.api),orders:n(32743)(this.api),customers:n(73489)(this.api),transfers:n(42751)(this.api),tokens:n(69080)(this.api),virtualAccounts:n(4757)(this.api),invoices:n(59209)(this.api),iins:n(84847)(this.api),paymentLink:n(36821)(this.api),plans:n(98640)(this.api),products:n(85749)(this.api),subscriptions:n(35051)(this.api),addons:n(69824)(this.api),settlements:n(86303)(this.api),qrCode:n(37525)(this.api),fundAccount:n(63540)(this.api),items:n(11064)(this.api),cards:n(16070)(this.api),webhooks:n(91247)(this.api),documents:n(9178)(this.api),disputes:n(11949)(this.api)})}}]),e}();r.VERSION=o.version,e.exports=r},16610:e=>{"use strict";var a=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};e.exports=function(e){var n="/accounts";return{create:function(a,i){return e.post({version:"v2",url:""+n,data:a},i)},edit:function(a,i,t){return e.patch({version:"v2",url:n+"/"+a,data:i},t)},fetch:function(a,i){return e.get({version:"v2",url:n+"/"+a},i)},delete:function(a,i){return e.delete({version:"v2",url:n+"/"+a},i)},uploadAccountDoc:function(i,t,o){var s=t.file,r=function(e,a){var n={};for(var i in e)!(a.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n}(t,["file"]);return e.postFormData({version:"v2",url:n+"/"+i+"/documents",formData:a({file:s.value},r)},o)},fetchAccountDoc:function(a,i){return e.get({version:"v2",url:n+"/"+a+"/documents"},i)}}}},69824:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090).normalizeDate;e.exports=function(e){var a="/addons",n="Addon ID is mandatory";return{fetch:function(i,t){return i?e.get({url:a+"/"+i},t):Promise.reject(n)},delete:function(i,t){return i?e.delete({url:a+"/"+i},t):Promise.reject(n)},all:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments[1],s=n.from,r=n.to,c=n.count,p=n.skip;return s&&(s=t(s)),r&&(r=t(r)),c=Number(c)||10,p=Number(p)||0,e.get({url:a,data:i({},n,{from:s,to:r,count:c,skip:p})},o)}}}},16070:e=>{"use strict";e.exports=function(e){return{fetch:function(a,n){if(!a)throw Error("`card_id` is mandatory");return e.get({url:"/cards/"+a},n)},requestCardReference:function(a,n){return e.post({url:"/cards/fingerprints",data:a},n)}}}},73489:e=>{"use strict";e.exports=function(e){return{create:function(a,n){return e.post({url:"/customers",data:a},n)},edit:function(a,n,i){return e.put({url:"/customers/"+a,data:n},i)},fetch:function(a,n){return e.get({url:"/customers/"+a},n)},all:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],i=a.count,t=a.skip;return i=Number(i)||10,t=Number(t)||0,e.get({url:"/customers",data:{count:i,skip:t}},n)},fetchTokens:function(a,n){return e.get({url:"/customers/"+a+"/tokens"},n)},fetchToken:function(a,n,i){return e.get({url:"/customers/"+a+"/tokens/"+n},i)},deleteToken:function(a,n,i){return e.delete({url:"/customers/"+a+"/tokens/"+n},i)},addBankAccount:function(a,n,i){return e.post({url:"/customers/"+a+"/bank_account",data:n},i)},deleteBankAccount:function(a,n,i){return e.delete({url:"/customers/"+a+"/bank_account/"+n},i)},requestEligibilityCheck:function(a,n){return e.post({url:"/customers/eligibility",data:a},n)},fetchEligibility:function(a,n){return e.get({url:"/customers/eligibility/"+a},n)}}}},11949:e=>{"use strict";e.exports=function(e){var a="/disputes";return{fetch:function(n,i){return e.get({url:a+"/"+n},i)},all:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1],t=n.count,o=n.skip;return t=Number(t)||10,o=Number(o)||0,e.get({url:""+a,data:{count:t,skip:o}},i)},accept:function(n,i){return e.post({url:a+"/"+n+"/accept"},i)},contest:function(n,i,t){return e.patch({url:a+"/"+n+"/contest",data:i},t)}}}},9178:e=>{"use strict";var a=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};e.exports=function(e){var n="/documents";return{create:function(i,t){var o=i.file,s=function(e,a){var n={};for(var i in e)!(a.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n}(i,["file"]);return e.postFormData({url:""+n,formData:a({file:o.value},s)},t)},fetch:function(a,i){return e.get({url:n+"/"+a},i)}}}},63540:e=>{"use strict";var a=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};e.exports=function(e){return{create:function(n,i){return e.post({url:"/fund_accounts",data:a({},n)},i)},fetch:function(a,n){return a?e.get({url:"/fund_accounts?customer_id="+a},n):Promise.reject("Customer Id is mandatroy")}}}},84847:e=>{"use strict";e.exports=function(e){var a="/iins";return{fetch:function(n,i){return e.get({url:a+"/"+n},i)},all:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1];return e.get({url:a+"/list",data:n},i)}}}},59209:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090).normalizeDate;e.exports=function(e){var a="/invoices",n="Invoice ID is mandatory";return{create:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1];return e.post({url:a,data:n},i)},edit:function(n){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments[2];return n?e.patch({url:a+"/"+n,data:i},t):Promise.reject("Invoice ID is mandatory")},issue:function(i,t){return i?e.post({url:a+"/"+i+"/issue"},t):Promise.reject(n)},delete:function(i,t){return i?e.delete({url:a+"/"+i},t):Promise.reject(n)},cancel:function(i,t){return i?e.post({url:a+"/"+i+"/cancel"},t):Promise.reject(n)},fetch:function(i,t){return i?e.get({url:a+"/"+i},t):Promise.reject(n)},all:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments[1],s=n.from,r=n.to,c=n.count,p=n.skip;return s&&(s=t(s)),r&&(r=t(r)),c=Number(c)||10,p=Number(p)||0,e.get({url:a,data:i({},n,{from:s,to:r,count:c,skip:p})},o)},notifyBy:function(i,t,o){return i?t?e.post({url:a+"/"+i+"/notify_by/"+t},o):Promise.reject("`medium` is required"):Promise.reject(n)}}}},11064:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090).normalizeDate;e.exports=function(e){return{all:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],i=a.from,o=a.to,s=a.count,r=a.skip,c=a.authorized,p=a.receipt;return i&&(i=t(i)),o&&(o=t(o)),s=Number(s)||10,r=Number(r)||0,e.get({url:"/items",data:{from:i,to:o,count:s,skip:r,authorized:c,receipt:p}},n)},fetch:function(a,n){if(!a)throw Error("`item_id` is mandatory");return e.get({url:"/items/"+a},n)},create:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],t=a.amount,o=a.currency,s=function(e,a){var n={};for(var i in e)!(a.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n}(a,["amount","currency"]);if(o=o||"INR",!t)throw Error("`amount` is mandatory");var r=Object.assign(i({currency:o,amount:t},s));return e.post({url:"/items",data:r},n)},edit:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];if(!a)throw Error("`item_id` is mandatory");return e.patch({url:"/items/"+a,data:n},i)},delete:function(a,n){if(!a)throw Error("`item_id` is mandatory");return e.delete({url:"/items/"+a},n)}}}},32743:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090).normalizeDate;e.exports=function(e){return{all:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],i=a.from,o=a.to,s=a.count,r=a.skip,c=a.authorized,p=a.receipt,l=void 0;return i&&(i=t(i)),o&&(o=t(o)),a.hasOwnProperty("expand[]")&&(l={"expand[]":a["expand[]"]}),s=Number(s)||10,r=Number(r)||0,e.get({url:"/orders",data:{from:i,to:o,count:s,skip:r,authorized:c,receipt:p,expand:l}},n)},fetch:function(a,n){if(!a)throw Error("`order_id` is mandatory");return e.get({url:"/orders/"+a},n)},create:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],t=a.currency,o=Object.assign(i({currency:t=t||"INR"},function(e,a){var n={};for(var i in e)!(a.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n}(a,["currency"])));return e.post({url:"/orders",data:o},n)},edit:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];if(!a)throw Error("`order_id` is mandatory");return e.patch({url:"/orders/"+a,data:n},i)},fetchPayments:function(a,n){if(!a)throw Error("`order_id` is mandatory");return e.get({url:"/orders/"+a+"/payments"},n)},fetchTransferOrder:function(a,n){if(!a)throw Error("`order_id` is mandatory");return e.get({url:"/orders/"+a+"/?expand[]=transfers&status"},n)},viewRtoReview:function(a,n){return e.post({url:"/orders/"+a+"/rto_review"},n)},editFulfillment:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return arguments[2],e.post({url:"/orders/"+a+"/fulfillment",data:n})}}}},36821:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090).normalizeDate;e.exports=function(e){var a="/payment_links",n="Payment Link ID is mandatory";return{create:function(n,i){return e.post({url:a,data:n},i)},cancel:function(i,t){return i?e.post({url:a+"/"+i+"/cancel"},t):Promise.reject(n)},fetch:function(i,t){return i?e.get({url:a+"/"+i},t):Promise.reject(n)},all:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments[1],s=n.from,r=n.to,c=n.count,p=n.skip;return s&&(s=t(s)),r&&(r=t(r)),c=Number(c)||10,p=Number(p)||0,e.get({url:a,data:i({},n,{from:s,to:r,count:c,skip:p})},o)},edit:function(n,i,t){return e.patch({url:a+"/"+n,data:i},t)},notifyBy:function(i,t,o){return i?t?e.post({url:a+"/"+i+"/notify_by/"+t},o):Promise.reject("`medium` is required"):Promise.reject(n)}}}},83449:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};function t(e,a){var n={};for(var i in e)!(a.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n}var o=n(42090).normalizeDate,s="`payment_id` is mandatory",r="/payments";e.exports=function(e){return{all:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],i=a.from,t=a.to,s=a.count,c=a.skip,p=void 0;return i&&(i=o(i)),t&&(t=o(t)),a.hasOwnProperty("expand[]")&&(p={"expand[]":a["expand[]"]}),s=Number(s)||10,c=Number(c)||0,e.get({url:""+r,data:{from:i,to:t,count:s,skip:c,expand:p}},n)},fetch:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2],t=void 0;if(!a)throw Error("`payment_id` is mandatory");return n.hasOwnProperty("expand[]")&&(t={"expand[]":n["expand[]"]}),e.get({url:r+"/"+a,data:{expand:t}},i)},capture:function(a,n,i,t){if(!a)throw Error("`payment_id` is mandatory");if(!n)throw Error("`amount` is mandatory");var o={amount:n};return"function"!=typeof i||t?"string"==typeof i&&(o.currency=i):(t=i,i=void 0),e.post({url:r+"/"+a+"/capture",data:o},t)},createPaymentJson:function(a,n){var i=Object.assign(t(a,[]));return e.post({url:r+"/create/json",data:i},n)},createRecurringPayment:function(a,n){return e.post({url:r+"/create/recurring",data:a},n)},edit:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];if(!a)throw Error("`payment_id` is mandatory");return e.patch({url:r+"/"+a,data:n},i)},refund:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];if(!a)throw Error("`payment_id` is mandatory");return e.post({url:r+"/"+a+"/refund",data:n},i)},fetchMultipleRefund:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments[2],o=n.from,s=n.to,c=n.count,p=n.skip;return e.get({url:r+"/"+a+"/refunds",data:i({},n,{from:o,to:s,count:c,skip:p})},t)},fetchRefund:function(a,n,i){if(!a)throw Error("payment Id` is mandatory");if(!n)throw Error("refund Id` is mandatory");return e.get({url:r+"/"+a+"/refunds/"+n},i)},fetchTransfer:function(a,n){if(!a)throw Error("payment Id` is mandatory");return e.get({url:r+"/"+a+"/transfers"},n)},transfer:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];if(!a)throw Error("`payment_id` is mandatory");return e.post({url:r+"/"+a+"/transfers",data:n},i)},bankTransfer:function(a,n){return a?e.get({url:r+"/"+a+"/bank_transfer"},n):Promise.reject(s)},fetchCardDetails:function(a,n){return a?e.get({url:r+"/"+a+"/card"},n):Promise.reject(s)},fetchPaymentDowntime:function(a){return e.get({url:r+"/downtimes"},a)},fetchPaymentDowntimeById:function(a,n){return a?e.get({url:r+"/downtimes/"+a},n):Promise.reject("Downtime Id is mandatory")},otpGenerate:function(a,n){return a?e.post({url:r+"/"+a+"/otp_generate"},n):Promise.reject("payment Id is mandatory")},otpSubmit:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];return a?e.post({url:r+"/"+a+"/otp/submit",data:n},i):Promise.reject("payment Id is mandatory")},otpResend:function(a,n){return a?e.post({url:r+"/"+a+"/otp/resend"},n):Promise.reject("payment Id is mandatory")},createUpi:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],i=Object.assign(t(a,[]));return e.post({url:r+"/create/upi",data:i},n)},validateVpa:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],i=Object.assign(t(a,[]));return e.post({url:r+"/validate/vpa",data:i},n)},fetchPaymentMethods:function(a){return e.get({url:"/methods"},a)}}}},98640:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090).normalizeDate;e.exports=function(e){var a="/plans";return{create:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1];return e.post({url:a,data:n},i)},fetch:function(n,i){return n?e.get({url:a+"/"+n},i):Promise.reject("Plan ID is mandatory")},all:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments[1],s=n.from,r=n.to,c=n.count,p=n.skip;return s&&(s=t(s)),r&&(r=t(r)),c=Number(c)||10,p=Number(p)||0,e.get({url:a,data:i({},n,{from:s,to:r,count:c,skip:p})},o)}}}},85749:e=>{"use strict";e.exports=function(e){var a="/accounts";return{requestProductConfiguration:function(n,i,t){return e.post({version:"v2",url:a+"/"+n+"/products",data:i},t)},edit:function(n,i,t,o){return e.patch({version:"v2",url:a+"/"+n+"/products/"+i,data:t},o)},fetch:function(n,i,t){return e.get({version:"v2",url:a+"/"+n+"/products/"+i},t)},fetchTnc:function(a,n){return e.get({version:"v2",url:"/products/"+a+"/tnc"},n)}}}},37525:e=>{"use strict";var a=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};e.exports=function(e){var n="/payments/qr_codes";return{create:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1];return e.post({url:n,data:a},i)},all:function(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1],o=i.from,s=i.to,r=i.count,c=i.skip;return e.get({url:n,data:a({},i,{from:o,to:s,count:r,skip:c})},t)},fetchAllPayments:function(i){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments[2],s=t.from,r=t.to,c=t.count,p=t.skip;return e.get({url:n+"/"+i+"/payments",data:a({},t,{from:s,to:r,count:c,skip:p})},o)},fetch:function(a,i){return a?e.get({url:n+"/"+a},i):Promise.reject("qrCode Id is mandatroy")},close:function(a,i){return a?e.post({url:n+"/"+a+"/close"},i):Promise.reject("qrCode Id is mandatroy")}}}},23011:(e,a,n)=>{"use strict";var i=n(42090),t=i.normalizeDate;i.normalizeNotes,e.exports=function(e){return{all:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],i=a.from,o=a.to,s=a.count,r=a.skip,c=a.payment_id,p="/refunds";return c&&(p="/payments/"+c+"/refunds"),i&&(i=t(i)),o&&(o=t(o)),s=Number(s)||10,r=Number(r)||0,e.get({url:p,data:{from:i,to:o,count:s,skip:r}},n)},edit:function(a,n,i){if(!a)throw Error("refund Id is mandatory");return e.patch({url:"/refunds/"+a,data:n},i)},fetch:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2],t=n.payment_id;if(!a)throw Error("`refund_id` is mandatory");var o="/refunds/"+a;return t&&(o="/payments/"+t+o),e.get({url:o},i)}}}},86303:e=>{"use strict";var a=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};e.exports=function(e){var n="/settlements";return{createOndemandSettlement:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1];return e.post({url:n+"/ondemand",data:a},i)},all:function(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1],o=i.from,s=i.to,r=i.count,c=i.skip;return e.get({url:n,data:a({},i,{from:o,to:s,count:r,skip:c})},t)},fetch:function(a,i){return a?e.get({url:n+"/"+a},i):Promise.reject("settlement Id is mandatroy")},fetchOndemandSettlementById:function(a){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments[2],o=void 0;return a?(i.hasOwnProperty("expand[]")&&(o={"expand[]":i["expand[]"]}),e.get({url:n+"/ondemand/"+a,data:{expand:o}},t)):Promise.reject("settlment Id is mandatroy")},fetchAllOndemandSettlement:function(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1],o=void 0,s=i.from,r=i.to,c=i.count,p=i.skip;return i.hasOwnProperty("expand[]")&&(o={"expand[]":i["expand[]"]}),e.get({url:n+"/ondemand",data:a({},i,{from:s,to:r,count:c,skip:p,expand:o})},t)},reports:function(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1],o=i.day,s=i.count,r=i.skip;return e.get({url:n+"/recon/combined",data:a({},i,{day:o,count:s,skip:r})},t)}}}},27996:e=>{"use strict";var a=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};e.exports=function(e){var n="/accounts";return{create:function(a,i,t){return e.post({version:"v2",url:n+"/"+a+"/stakeholders",data:i},t)},edit:function(a,i,t,o){return e.patch({version:"v2",url:n+"/"+a+"/stakeholders/"+i,data:t},o)},fetch:function(a,i,t){return e.get({version:"v2",url:n+"/"+a+"/stakeholders/"+i},t)},all:function(a,i){return e.get({version:"v2",url:n+"/"+a+"/stakeholders"},i)},uploadStakeholderDoc:function(i,t,o,s){var r=o.file,c=function(e,a){var n={};for(var i in e)!(a.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n}(o,["file"]);return e.postFormData({version:"v2",url:n+"/"+i+"/stakeholders/"+t+"/documents",formData:a({file:r.value},c)},s)},fetchStakeholderDoc:function(a,i,t){return e.get({version:"v2",url:n+"/"+a+"/stakeholders/"+i+"/documents"},t)}}}},35051:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090).normalizeDate;e.exports=function(e){var a="/subscriptions",n="Subscription ID is mandatory";return{create:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1];return e.post({url:a,data:n},i)},fetch:function(i,t){return i?e.get({url:a+"/"+i},t):Promise.reject(n)},update:function(i,t,o){return i?e.patch({url:a+"/"+i,data:t},o):Promise.reject(n)},pendingUpdate:function(i,t){return i?e.get({url:a+"/"+i+"/retrieve_scheduled_changes"},t):Promise.reject(n)},cancelScheduledChanges:function(n,i){return n?e.post({url:a+"/"+n+"/cancel_scheduled_changes"},i):Promise.reject("Subscription Id is mandatory")},pause:function(n){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments[2];return n?e.post({url:a+"/"+n+"/pause",data:i},t):Promise.reject("Subscription Id is mandatory")},resume:function(n){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments[2];return n?e.post({url:a+"/"+n+"/resume",data:i},t):Promise.reject("Subscription Id is mandatory")},deleteOffer:function(n,i,t){return n?e.delete({url:a+"/"+n+"/"+i},t):Promise.reject("Subscription Id is mandatory")},all:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments[1],s=n.from,r=n.to,c=n.count,p=n.skip;return s&&(s=t(s)),r&&(r=t(r)),c=Number(c)||10,p=Number(p)||0,e.get({url:a,data:i({},n,{from:s,to:r,count:c,skip:p})},o)},cancel:function(t){var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments[2];return t?e.post(i({url:a+"/"+t+"/cancel"},o&&{data:{cancel_at_cycle_end:1}}),s):Promise.reject(n)},createAddon:function(t,o,s){return t?e.post({url:a+"/"+t+"/addons",data:i({},o)},s):Promise.reject(n)},createRegistrationLink:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1];return e.post({url:"/subscription_registration/auth_links",data:a},n)}}}},69080:(e,a,n)=>{"use strict";n(42090).normalizeNotes,e.exports=function(e){var a="/tokens";return{create:function(n,i){return e.post({url:""+a,data:n},i)},fetch:function(n,i){return e.post({url:a+"/fetch",data:n},i)},delete:function(n,i){return e.post({url:a+"/delete",data:n},i)},processPaymentOnAlternatePAorPG:function(n,i){return e.post({url:a+"/service_provider_tokens/token_transactional_data",data:n},i)}}}},42751:(e,a,n)=>{"use strict";var i=n(42090).normalizeDate;e.exports=function(e){return{all:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],t=a.from,o=a.to,s=a.count,r=a.skip,c=a.payment_id,p=a.recipient_settlement_id,l="/transfers";return c&&(l="/payments/"+c+"/transfers"),t&&(t=i(t)),o&&(o=i(o)),s=Number(s)||10,r=Number(r)||0,e.get({url:l,data:{from:t,to:o,count:s,skip:r,recipient_settlement_id:p}},n)},fetch:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];if(n.payment_id,!a)throw Error("`transfer_id` is mandatory");return e.get({url:"/transfers/"+a},i)},create:function(a,n){return e.post({url:"/transfers",data:a},n)},edit:function(a,n,i){return e.patch({url:"/transfers/"+a,data:n},i)},reverse:function(a,n,i){if(!a)throw Error("`transfer_id` is mandatory");return e.post({url:"/transfers/"+a+"/reversals",data:n},i)},fetchSettlements:function(a){return e.get({url:"/transfers?expand[]=recipient_settlement"},a)}}}},4757:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090),o=t.normalizeDate;t.normalizeNotes;var s="/virtual_accounts",r="`virtual_account_id` is mandatory";e.exports=function(e){return{all:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],t=a.from,r=a.to,c=a.count,p=a.skip,l=function(e,a){var n={};for(var i in e)!(a.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n}(a,["from","to","count","skip"]);return t&&(t=o(t)),r&&(r=o(r)),c=Number(c)||10,p=Number(p)||0,e.get({url:s,data:i({from:t,to:r,count:c,skip:p},l)},n)},fetch:function(a,n){return a?e.get({url:s+"/"+a},n):Promise.reject(r)},create:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1];return e.post({url:s,data:a},n)},close:function(a,n){return a?e.post({url:s+"/"+a+"/close"},n):Promise.reject(r)},fetchPayments:function(a,n){return a?e.get({url:s+"/"+a+"/payments"},n):Promise.reject(r)},addReceiver:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];return a?e.post({url:s+"/"+a+"/receivers",data:n},i):Promise.reject(r)},allowedPayer:function(a){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments[2];return a?e.post({url:s+"/"+a+"/allowed_payers",data:n},i):Promise.reject(r)},deleteAllowedPayer:function(a,n,i){return a?n?e.delete({url:s+"/"+a+"/allowed_payers/"+n},i):Promise.reject("allowed payer id is mandatory"):Promise.reject(r)}}}},91247:(e,a,n)=>{"use strict";var i=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t=n(42090).normalizeDate;e.exports=function(e){var a="/accounts";return{create:function(n,i,t){var o={url:"/webhooks",data:n};return i&&(o={version:"v2",url:a+"/"+i+"/webhooks",data:n}),e.post(o,t)},edit:function(n,i,t,o){return t&&i?e.patch({version:"v2",url:a+"/"+t+"/webhooks/"+i,data:n},o):e.put({url:"/webhooks/"+i,data:n},o)},all:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments[1],s=arguments[2],r=n.from,c=n.to,p=n.count,l=n.skip;r&&(r=t(r)),c&&(c=t(c));var u=i({},n,{from:r,to:c,count:p=Number(p)||10,skip:l=Number(l)||0});return o?e.get({version:"v2",url:a+"/"+o+"/webhooks/",data:u},s):e.get({url:"/webhooks",data:u},s)},fetch:function(n,i,t){return e.get({version:"v2",url:a+"/"+i+"/webhooks/"+n},t)},delete:function(n,i,t){return e.delete({version:"v2",url:a+"/"+i+"/webhooks/"+n},t)}}}},70206:e=>{"use strict";e.exports=function(e,a){return a?e.then(function(e){a(null,e.data)}).catch(function(e){a(e,null)}):e.then(function(e){return e.data})}},42090:(e,a,n)=>{"use strict";var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t=n(84770);function o(e){return+new Date(e)/1e3}function s(e){return!isNaN(Number(e))}function r(e){return void 0!==e}function c(e){return JSON.stringify(e,null,2)}function p(e,a,i){var t=n(84770);if(!r(e)||!r(a)||!r(i))throw Error("Invalid Parameters: Please give request body,signature sent in X-Razorpay-Signature header and webhook secret from dashboard as parameters");return e=e.toString(),t.createHmac("sha256",i).update(e).digest("hex")===a}e.exports={normalizeNotes:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a={};for(var n in e)a["notes["+n+"]"]=e[n];return a},normalizeDate:function(e){return s(e)?e:o(e)},normalizeBoolean:function(e){return void 0===e?e:e?1:0},isNumber:s,getDateInSecs:o,prettify:c,isDefined:r,isNonNullObject:function(e){return!!e&&(void 0===e?"undefined":i(e))==="object"&&!Array.isArray(e)},getTestError:function(e,a,n){return Error("\n"+e+"\n"+("Expected("+(void 0===a?"undefined":i(a)))+")\n"+c(a)+"\n\nGot("+(void 0===n?"undefined":i(n))+")\n"+c(n))},validateWebhookSignature:p,validatePaymentVerification:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments[1],n=arguments[2],i=e.payment_id;if(!n)throw Error("secret is mandatory");if(!0===r(e.order_id))var t=e.order_id+"|"+i;else if(!0===r(e.subscription_id))var t=i+"|"+e.subscription_id;else if(!0===r(e.payment_link_id))var t=e.payment_link_id+"|"+e.payment_link_reference_id+"|"+e.payment_link_status+"|"+i;else throw Error("Either order_id or subscription_id is mandatory");return p(t,a,n)},isValidUrl:function(e){try{return new URL(e),!0}catch(e){return!1}},generateOnboardingSignature:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments[1];return function(e,a){try{var n=Buffer.from(a.slice(0,16),"utf8"),i=Buffer.alloc(12);n.copy(i,0,0,12);var o=t.createCipheriv("aes-128-gcm",n,i),s=o.update(e,"utf8");s=Buffer.concat([s,o.final()]);var r=o.getAuthTag();return Buffer.concat([s,r]).toString("hex")}catch(e){throw Error("Encryption failed: "+e.message)}}(JSON.stringify(e),a)}}},8596:(e,a,n)=>{var i;e.exports=function(){if(!i){try{i=n(19092)("follow-redirects")}catch(e){}"function"!=typeof i&&(i=function(){})}i.apply(null,arguments)}},84834:(e,a,n)=>{var i=n(17360),t=i.URL,o=n(32615),s=n(35240),r=n(76162).Writable,c=n(27790),p=n(8596);!function(){var e="undefined"!=typeof process,a="undefined"!=typeof window&&"undefined"!=typeof document,n=C(Error.captureStackTrace);e||!a&&n||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{c(new t(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(a,n,i){this._redirectable.emit(e,a,n,i)}});var f=R("ERR_INVALID_URL","Invalid URL",TypeError),x=R("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),v=R("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",x),h=R("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=R("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||k;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var n=this;this._onNativeResponse=function(e){try{n._processResponse(e)}catch(e){n.emit("error",e instanceof x?e:new x({cause:e}))}},this._performRequest()}function w(e){var a={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(e).forEach(function(i){var o=i+":",s=n[o]=e[i],r=a[i]=Object.create(s);Object.defineProperties(r,{request:{value:function(e,i,s){var r;return(r=e,t&&r instanceof t)?e=E(e):A(e)?e=E(j(e)):(s=i,i=_(e),e={protocol:o}),C(i)&&(s=i,i=null),(i=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,i)).nativeProtocols=n,A(i.host)||A(i.hostname)||(i.hostname="::1"),c.equal(i.protocol,o,"protocol mismatch"),p("options",i),new y(i,s)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,n){var i=r.request(e,a,n);return i.end(),i},configurable:!0,enumerable:!0,writable:!0}})}),a}function k(){}function j(e){var a;if(l)a=new t(e);else if(!A((a=_(i.parse(e))).protocol))throw new f({input:e});return a}function _(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function E(e,a){var n=a||{};for(var i of u)n[i]=e[i];return n.hostname.startsWith("[")&&(n.hostname=n.hostname.slice(1,-1)),""!==n.port&&(n.port=Number(n.port)),n.path=n.search?n.pathname+n.search:n.pathname,n}function O(e,a){var n;for(var i in a)e.test(i)&&(n=a[i],delete a[i]);return null==n?void 0:String(n).trim()}function R(e,a,n){function i(n){C(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,n||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return i.prototype=new(n||Error),Object.defineProperties(i.prototype,{constructor:{value:i,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),i}function S(e,a){for(var n of d)e.removeListener(n,m[n]);e.on("error",k),e.destroy(a)}function A(e){return"string"==typeof e||e instanceof String}function C(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){S(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return S(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,n){if(this._ending)throw new b;if(!A(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(C(a)&&(n=a,a=null),0===e.length){n&&n();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,n)):(this.emit("error",new h),this.abort())},y.prototype.end=function(e,a,n){if(C(e)?(n=e,e=a=null):C(a)&&(n=a,a=null),e){var i=this,t=this._currentRequest;this.write(e,a,function(){i._ended=!0,t.end(null,null,n)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,n)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var n=this;function i(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function t(a){n._timeout&&clearTimeout(n._timeout),n._timeout=setTimeout(function(){n.emit("timeout"),o()},e),i(a)}function o(){n._timeout&&(clearTimeout(n._timeout),n._timeout=null),n.removeListener("abort",o),n.removeListener("error",o),n.removeListener("response",o),n.removeListener("close",o),a&&n.removeListener("timeout",a),n.socket||n._currentRequest.removeListener("socket",t)}return a&&this.on("timeout",a),this.socket?t(this.socket):this._currentRequest.once("socket",t),this.on("socket",i),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(a,n){return this._currentRequest[e](a,n)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var n=e.slice(0,-1);this._options.agent=this._options.agents[n]}var t=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var o of(t._redirectable=this,d))t.on(o,m[o]);if(this._currentUrl=/^\//.test(this._options.path)?i.format(this._options):this._options.path,this._isRedirect){var s=0,r=this,c=this._requestBodyBuffers;!function e(a){if(t===r._currentRequest){if(a)r.emit("error",a);else if(s<c.length){var n=c[s++];t.finished||t.write(n.data,n.encoding,e)}else r._ended&&t.end()}}()}},y.prototype._processResponse=function(e){var a,n,o,s=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:s});var r=e.headers.location;if(!r||!1===this._options.followRedirects||s<300||s>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(S(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new v;var u=this._options.beforeRedirect;u&&(o=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==s&&302!==s||"POST"!==this._options.method)&&(303!==s||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],O(/^content-/i,this._options.headers));var m=O(/^host$/i,this._options.headers),f=j(this._currentUrl),x=m||f.host,h=/^\w+:/.test(r)?this._currentUrl:i.format(Object.assign(f,{host:x})),b=l?new t(r,h):j(i.resolve(h,r));if(p("redirecting to",b.href),this._isRedirect=!0,E(b,this._options),(b.protocol===f.protocol||"https:"===b.protocol)&&(b.host===x||(c(A(a=b.host)&&A(x)),(n=a.length-x.length-1)>0&&"."===a[n]&&a.endsWith(x)))||O(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),C(u)){var g={headers:e.headers,statusCode:s},y={url:h,method:d,headers:o};u(this._options,g,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:o,https:s}),e.exports.wrap=w},67057:(e,a,n)=>{"use strict";let i;let t=n(19801),o=n(74175),s=n(72616),{env:r}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function p(e,a){if(0===i)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!a&&void 0===i)return 0;let n=i||0;if("dumb"===r.TERM)return n;if("win32"===process.platform){let e=t.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in r)||"codeship"===r.CI_NAME?1:n;if("TEAMCITY_VERSION"in r)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION)?1:0;if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){let e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:n}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?i=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(i=1),"FORCE_COLOR"in r&&(i="true"===r.FORCE_COLOR?1:"false"===r.FORCE_COLOR?0:0===r.FORCE_COLOR.length?1:Math.min(parseInt(r.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(p(e,e&&e.isTTY))},stdout:c(p(!0,o.isatty(1))),stderr:c(p(!0,o.isatty(2)))}},44554:(e,a,n)=>{"use strict";var i,t,o;let s,r,c,p,l,u,d;let m=n(32291),f=n(84770),x=n(17360),v=n(21180),h=n(32615),b=n(35240),g=n(21764),y=n(84834),w=n(71568),k=n(76162),j=n(17702);function _(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}let E=_(m),O=_(f),R=_(x),S=_(v),A=_(h),C=_(b),T=_(g),P=_(y),z=_(w),F=_(k);function N(e,a){return function(){return e.apply(a,arguments)}}let{toString:U}=Object.prototype,{getPrototypeOf:q}=Object,{iterator:B,toStringTag:L}=Symbol,I=(s=Object.create(null),e=>{let a=U.call(e);return s[a]||(s[a]=a.slice(8,-1).toLowerCase())}),D=e=>(e=e.toLowerCase(),a=>I(a)===e),M=e=>a=>typeof a===e,{isArray:H}=Array,W=M("undefined");function G(e){return null!==e&&!W(e)&&null!==e.constructor&&!W(e.constructor)&&V(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}let $=D("ArrayBuffer"),J=M("string"),V=M("function"),K=M("number"),Y=e=>null!==e&&"object"==typeof e,X=e=>{if("object"!==I(e))return!1;let a=q(e);return(null===a||a===Object.prototype||null===Object.getPrototypeOf(a))&&!(L in e)&&!(B in e)},Q=D("Date"),Z=D("File"),ee=D("Blob"),ea=D("FileList"),en=D("URLSearchParams"),[ei,et,eo,es]=["ReadableStream","Request","Response","Headers"].map(D);function er(e,a,{allOwnKeys:n=!1}={}){let i,t;if(null!=e){if("object"!=typeof e&&(e=[e]),H(e))for(i=0,t=e.length;i<t;i++)a.call(null,e[i],i,e);else{let t;if(G(e))return;let o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;for(i=0;i<s;i++)t=o[i],a.call(null,e[t],t,e)}}}function ec(e,a){let n;if(G(e))return null;a=a.toLowerCase();let i=Object.keys(e),t=i.length;for(;t-- >0;)if(a===(n=i[t]).toLowerCase())return n;return null}let ep="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,el=e=>!W(e)&&e!==ep,eu=(r="undefined"!=typeof Uint8Array&&q(Uint8Array),e=>r&&e instanceof r),ed=D("HTMLFormElement"),em=(({hasOwnProperty:e})=>(a,n)=>e.call(a,n))(Object.prototype),ef=D("RegExp"),ex=(e,a)=>{let n=Object.getOwnPropertyDescriptors(e),i={};er(n,(n,t)=>{let o;!1!==(o=a(n,t,e))&&(i[t]=o||n)}),Object.defineProperties(e,i)},ev=D("AsyncFunction"),eh=(i="function"==typeof setImmediate,t=V(ep.postMessage),i?setImmediate:t?(u=`axios@${Math.random()}`,d=[],ep.addEventListener("message",({source:e,data:a})=>{e===ep&&a===u&&d.length&&d.shift()()},!1),e=>{d.push(e),ep.postMessage(u,"*")}):e=>setTimeout(e)),eb="undefined"!=typeof queueMicrotask?queueMicrotask.bind(ep):"undefined"!=typeof process&&process.nextTick||eh,eg={isArray:H,isArrayBuffer:$,isBuffer:G,isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||V(e.append)&&("formdata"===(a=I(e))||"object"===a&&V(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&$(e.buffer)},isString:J,isNumber:K,isBoolean:e=>!0===e||!1===e,isObject:Y,isPlainObject:X,isEmptyObject:e=>{if(!Y(e)||G(e))return!1;try{return 0===Object.keys(e).length&&Object.getPrototypeOf(e)===Object.prototype}catch(e){return!1}},isReadableStream:ei,isRequest:et,isResponse:eo,isHeaders:es,isUndefined:W,isDate:Q,isFile:Z,isBlob:ee,isRegExp:ef,isFunction:V,isStream:e=>Y(e)&&V(e.pipe),isURLSearchParams:en,isTypedArray:eu,isFileList:ea,forEach:er,merge:function e(){let{caseless:a}=el(this)&&this||{},n={},i=(i,t)=>{let o=a&&ec(n,t)||t;X(n[o])&&X(i)?n[o]=e(n[o],i):X(i)?n[o]=e({},i):H(i)?n[o]=i.slice():n[o]=i};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&er(arguments[e],i);return n},extend:(e,a,n,{allOwnKeys:i}={})=>(er(a,(a,i)=>{n&&V(a)?e[i]=N(a,n):e[i]=a},{allOwnKeys:i}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,n,i)=>{e.prototype=Object.create(a.prototype,i),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,a,n,i)=>{let t,o,s;let r={};if(a=a||{},null==e)return a;do{for(o=(t=Object.getOwnPropertyNames(e)).length;o-- >0;)s=t[o],(!i||i(s,e,a))&&!r[s]&&(a[s]=e[s],r[s]=!0);e=!1!==n&&q(e)}while(e&&(!n||n(e,a))&&e!==Object.prototype);return a},kindOf:I,kindOfTest:D,endsWith:(e,a,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=a.length;let i=e.indexOf(a,n);return -1!==i&&i===n},toArray:e=>{if(!e)return null;if(H(e))return e;let a=e.length;if(!K(a))return null;let n=Array(a);for(;a-- >0;)n[a]=e[a];return n},forEachEntry:(e,a)=>{let n;let i=(e&&e[B]).call(e);for(;(n=i.next())&&!n.done;){let i=n.value;a.call(e,i[0],i[1])}},matchAll:(e,a)=>{let n;let i=[];for(;null!==(n=e.exec(a));)i.push(n);return i},isHTMLForm:ed,hasOwnProperty:em,hasOwnProp:em,reduceDescriptors:ex,freezeMethods:e=>{ex(e,(a,n)=>{if(V(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;if(V(e[n])){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},toObjectSet:(e,a)=>{let n={};return(e=>{e.forEach(e=>{n[e]=!0})})(H(e)?e:String(e).split(a)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,a,n){return a.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e=+e)?e:a,findKey:ec,global:ep,isContextDefined:el,isSpecCompliantForm:function(e){return!!(e&&V(e.append)&&"FormData"===e[L]&&e[B])},toJSONObject:e=>{let a=Array(10),n=(e,i)=>{if(Y(e)){if(a.indexOf(e)>=0)return;if(G(e))return e;if(!("toJSON"in e)){a[i]=e;let t=H(e)?[]:{};return er(e,(e,a)=>{let o=n(e,i+1);W(o)||(t[a]=o)}),a[i]=void 0,t}}return e};return n(e,0)},isAsyncFn:ev,isThenable:e=>e&&(Y(e)||V(e))&&V(e.then)&&V(e.catch),setImmediate:eh,asap:eb,isIterable:e=>null!=e&&V(e[B])};function ey(e,a,n,i,t){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",a&&(this.code=a),n&&(this.config=n),i&&(this.request=i),t&&(this.response=t,this.status=t.status?t.status:null)}eg.inherits(ey,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:eg.toJSONObject(this.config),code:this.code,status:this.status}}});let ew=ey.prototype,ek={};function ej(e){return eg.isPlainObject(e)||eg.isArray(e)}function e_(e){return eg.endsWith(e,"[]")?e.slice(0,-2):e}function eE(e,a,n){return e?e.concat(a).map(function(e,a){return e=e_(e),!n&&a?"["+e+"]":e}).join(n?".":""):a}["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ek[e]={value:e}}),Object.defineProperties(ey,ek),Object.defineProperty(ew,"isAxiosError",{value:!0}),ey.from=(e,a,n,i,t,o)=>{let s=Object.create(ew);return eg.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),ey.call(s,e.message,a,n,i,t),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};let eO=eg.toFlatObject(eg,{},null,function(e){return/^is[A-Z]/.test(e)});function eR(e,a,n){if(!eg.isObject(e))throw TypeError("target must be an object");a=a||new(E.default||FormData);let i=(n=eg.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,a){return!eg.isUndefined(a[e])})).metaTokens,t=n.visitor||p,o=n.dots,s=n.indexes,r=(n.Blob||"undefined"!=typeof Blob&&Blob)&&eg.isSpecCompliantForm(a);if(!eg.isFunction(t))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(eg.isDate(e))return e.toISOString();if(eg.isBoolean(e))return e.toString();if(!r&&eg.isBlob(e))throw new ey("Blob is not supported. Use a Buffer instead.");return eg.isArrayBuffer(e)||eg.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,n,t){let r=e;if(e&&!t&&"object"==typeof e){if(eg.endsWith(n,"{}"))n=i?n:n.slice(0,-2),e=JSON.stringify(e);else{var p;if(eg.isArray(e)&&(p=e,eg.isArray(p)&&!p.some(ej))||(eg.isFileList(e)||eg.endsWith(n,"[]"))&&(r=eg.toArray(e)))return n=e_(n),r.forEach(function(e,i){eg.isUndefined(e)||null===e||a.append(!0===s?eE([n],i,o):null===s?n:n+"[]",c(e))}),!1}}return!!ej(e)||(a.append(eE(t,n,o),c(e)),!1)}let l=[],u=Object.assign(eO,{defaultVisitor:p,convertValue:c,isVisitable:ej});if(!eg.isObject(e))throw TypeError("data must be an object");return function e(n,i){if(!eg.isUndefined(n)){if(-1!==l.indexOf(n))throw Error("Circular reference detected in "+i.join("."));l.push(n),eg.forEach(n,function(n,o){!0===(!(eg.isUndefined(n)||null===n)&&t.call(a,n,eg.isString(o)?o.trim():o,i,u))&&e(n,i?i.concat(o):[o])}),l.pop()}}(e),a}function eS(e){let a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return a[e]})}function eA(e,a){this._pairs=[],e&&eR(e,this,a)}let eC=eA.prototype;function eT(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eP(e,a,n){let i;if(!a)return e;let t=n&&n.encode||eT;eg.isFunction(n)&&(n={serialize:n});let o=n&&n.serialize;if(i=o?o(a,n):eg.isURLSearchParams(a)?a.toString():new eA(a,n).toString(t)){let a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}eC.append=function(e,a){this._pairs.push([e,a])},eC.toString=function(e){let a=e?function(a){return e.call(this,a,eS)}:eS;return this._pairs.map(function(e){return a(e[0])+"="+a(e[1])},"").join("&")};class ez{constructor(){this.handlers=[]}use(e,a,n){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){eg.forEach(this.handlers,function(a){null!==a&&e(a)})}}let eF={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},eN=R.default.URLSearchParams,eU="abcdefghijklmnopqrstuvwxyz",eq="0123456789",eB={DIGIT:eq,ALPHA:eU,ALPHA_DIGIT:eU+eU.toUpperCase()+eq},eL={isNode:!0,classes:{URLSearchParams:eN,FormData:E.default,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:eB,generateString:(e=16,a=eB.ALPHA_DIGIT)=>{let n="",{length:i}=a,t=new Uint32Array(e);O.default.randomFillSync(t);for(let o=0;o<e;o++)n+=a[t[o]%i];return n},protocols:["http","https","file","data"]},eI="undefined"!=typeof window&&"undefined"!=typeof document,eD="object"==typeof navigator&&navigator||void 0,eM=eI&&(!eD||0>["ReactNative","NativeScript","NS"].indexOf(eD.product)),eH="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,eW=eI&&window.location.href||"http://localhost",eG={...Object.freeze({__proto__:null,hasBrowserEnv:eI,hasStandardBrowserWebWorkerEnv:eH,hasStandardBrowserEnv:eM,navigator:eD,origin:eW}),...eL};function e$(e){if(eg.isFormData(e)&&eg.isFunction(e.entries)){let a={};return eg.forEachEntry(e,(e,n)=>{!function e(a,n,i,t){let o=a[t++];if("__proto__"===o)return!0;let s=Number.isFinite(+o),r=t>=a.length;return(o=!o&&eg.isArray(i)?i.length:o,r)?eg.hasOwnProp(i,o)?i[o]=[i[o],n]:i[o]=n:(i[o]&&eg.isObject(i[o])||(i[o]=[]),e(a,n,i[o],t)&&eg.isArray(i[o])&&(i[o]=function(e){let a,n;let i={},t=Object.keys(e),o=t.length;for(a=0;a<o;a++)i[n=t[a]]=e[n];return i}(i[o]))),!s}(eg.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),n,a,0)}),a}return null}let eJ={transitional:eF,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let n;let i=a.getContentType()||"",t=i.indexOf("application/json")>-1,o=eg.isObject(e);if(o&&eg.isHTMLForm(e)&&(e=new FormData(e)),eg.isFormData(e))return t?JSON.stringify(e$(e)):e;if(eg.isArrayBuffer(e)||eg.isBuffer(e)||eg.isStream(e)||eg.isFile(e)||eg.isBlob(e)||eg.isReadableStream(e))return e;if(eg.isArrayBufferView(e))return e.buffer;if(eg.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(i.indexOf("application/x-www-form-urlencoded")>-1){var s,r;return(s=e,r=this.formSerializer,eR(s,new eG.classes.URLSearchParams,{visitor:function(e,a,n,i){return eG.isNode&&eg.isBuffer(e)?(this.append(a,e.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)},...r})).toString()}if((n=eg.isFileList(e))||i.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return eR(n?{"files[]":e}:e,a&&new a,this.formSerializer)}}return o||t?(a.setContentType("application/json",!1),function(e,a,n){if(eg.isString(e))try{return(0,JSON.parse)(e),eg.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let a=this.transitional||eJ.transitional,n=a&&a.forcedJSONParsing,i="json"===this.responseType;if(eg.isResponse(e)||eg.isReadableStream(e))return e;if(e&&eg.isString(e)&&(n&&!this.responseType||i)){let n=a&&a.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!n&&i){if("SyntaxError"===e.name)throw ey.from(e,ey.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eG.classes.FormData,Blob:eG.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};eg.forEach(["delete","get","head","post","put","patch"],e=>{eJ.headers[e]={}});let eV=eg.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),eK=e=>{let a,n,i;let t={};return e&&e.split("\n").forEach(function(e){i=e.indexOf(":"),a=e.substring(0,i).trim().toLowerCase(),n=e.substring(i+1).trim(),!a||t[a]&&eV[a]||("set-cookie"===a?t[a]?t[a].push(n):t[a]=[n]:t[a]=t[a]?t[a]+", "+n:n)}),t},eY=Symbol("internals");function eX(e){return e&&String(e).trim().toLowerCase()}function eQ(e){return!1===e||null==e?e:eg.isArray(e)?e.map(eQ):String(e)}let eZ=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function e0(e,a,n,i,t){if(eg.isFunction(i))return i.call(this,a,n);if(t&&(a=n),eg.isString(a)){if(eg.isString(i))return -1!==a.indexOf(i);if(eg.isRegExp(i))return i.test(a)}}class e1{constructor(e){e&&this.set(e)}set(e,a,n){let i=this;function t(e,a,n){let t=eX(a);if(!t)throw Error("header name must be a non-empty string");let o=eg.findKey(i,t);o&&void 0!==i[o]&&!0!==n&&(void 0!==n||!1===i[o])||(i[o||a]=eQ(e))}let o=(e,a)=>eg.forEach(e,(e,n)=>t(e,n,a));if(eg.isPlainObject(e)||e instanceof this.constructor)o(e,a);else if(eg.isString(e)&&(e=e.trim())&&!eZ(e))o(eK(e),a);else if(eg.isObject(e)&&eg.isIterable(e)){let n={},i,t;for(let a of e){if(!eg.isArray(a))throw TypeError("Object iterator must return a key-value pair");n[t=a[0]]=(i=n[t])?eg.isArray(i)?[...i,a[1]]:[i,a[1]]:a[1]}o(n,a)}else null!=e&&t(a,e,n);return this}get(e,a){if(e=eX(e)){let n=eg.findKey(this,e);if(n){let e=this[n];if(!a)return e;if(!0===a)return function(e){let a;let n=Object.create(null),i=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;a=i.exec(e);)n[a[1]]=a[2];return n}(e);if(eg.isFunction(a))return a.call(this,e,n);if(eg.isRegExp(a))return a.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=eX(e)){let n=eg.findKey(this,e);return!!(n&&void 0!==this[n]&&(!a||e0(this,this[n],n,a)))}return!1}delete(e,a){let n=this,i=!1;function t(e){if(e=eX(e)){let t=eg.findKey(n,e);t&&(!a||e0(n,n[t],t,a))&&(delete n[t],i=!0)}}return eg.isArray(e)?e.forEach(t):t(e),i}clear(e){let a=Object.keys(this),n=a.length,i=!1;for(;n--;){let t=a[n];(!e||e0(this,this[t],t,e,!0))&&(delete this[t],i=!0)}return i}normalize(e){let a=this,n={};return eg.forEach(this,(i,t)=>{let o=eg.findKey(n,t);if(o){a[o]=eQ(i),delete a[t];return}let s=e?t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,n)=>a.toUpperCase()+n):String(t).trim();s!==t&&delete a[t],a[s]=eQ(i),n[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return eg.forEach(this,(n,i)=>{null!=n&&!1!==n&&(a[i]=e&&eg.isArray(n)?n.join(", "):n)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let n=new this(e);return a.forEach(e=>n.set(e)),n}static accessor(e){let a=(this[eY]=this[eY]={accessors:{}}).accessors,n=this.prototype;function i(e){let i=eX(e);a[i]||(function(e,a){let n=eg.toCamelCase(" "+a);["get","set","has"].forEach(i=>{Object.defineProperty(e,i+n,{value:function(e,n,t){return this[i].call(this,a,e,n,t)},configurable:!0})})}(n,e),a[i]=!0)}return eg.isArray(e)?e.forEach(i):i(e),this}}function e2(e,a){let n=this||eJ,i=a||n,t=e1.from(i.headers),o=i.data;return eg.forEach(e,function(e){o=e.call(n,o,t.normalize(),a?a.status:void 0)}),t.normalize(),o}function e3(e){return!!(e&&e.__CANCEL__)}function e6(e,a,n){ey.call(this,null==e?"canceled":e,ey.ERR_CANCELED,a,n),this.name="CanceledError"}function e4(e,a,n){let i=n.config.validateStatus;!n.status||!i||i(n.status)?e(n):a(new ey("Request failed with status code "+n.status,[ey.ERR_BAD_REQUEST,ey.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function e9(e,a,n){let i=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a);return e&&(i||!1==n)?a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e:a}e1.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),eg.reduceDescriptors(e1.prototype,({value:e},a)=>{let n=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[n]=e}}}),eg.freezeMethods(e1),eg.inherits(e6,ey,{__CANCEL__:!0});let e8="1.11.0";function e7(e){let a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}let e5=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/,ae=Symbol("internals");class aa extends F.default.Transform{constructor(e){super({readableHighWaterMark:(e=eg.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,a)=>!eg.isUndefined(a[e]))).chunkSize});let a=this[ae]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)})}_read(e){let a=this[ae];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,n){let i=this[ae],t=i.maxRate,o=this.readableHighWaterMark,s=i.timeWindow,r=t/(1e3/s),c=!1!==i.minChunkSize?Math.max(i.minChunkSize,.01*r):0,p=(e,a)=>{let n=Buffer.byteLength(e);i.bytesSeen+=n,i.bytes+=n,i.isCaptured&&this.emit("progress",i.bytesSeen),this.push(e)?process.nextTick(a):i.onReadCallback=()=>{i.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{let n;let l=Buffer.byteLength(e),u=null,d=o,m=0;if(t){let e=Date.now();(!i.ts||(m=e-i.ts)>=s)&&(i.ts=e,n=r-i.bytes,i.bytes=n<0?-n:0,m=0),n=r-i.bytes}if(t){if(n<=0)return setTimeout(()=>{a(null,e)},s-m);n<d&&(d=n)}d&&l>d&&l-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),p(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,function e(a,i){if(a)return n(a);i?l(i,e):n(null)})}}let{asyncIterator:an}=Symbol,ai=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[an]?yield*e[an]():yield e},at=eG.ALPHABET.ALPHA_DIGIT+"-_",ao="function"==typeof TextEncoder?new TextEncoder:new T.default.TextEncoder,as=ao.encode("\r\n");class ar{constructor(e,a){let{escapeName:n}=this.constructor,i=eg.isString(a),t=`Content-Disposition: form-data; name="${n(e)}"${!i&&a.name?`; filename="${n(a.name)}"`:""}\r
`;i?a=ao.encode(String(a).replace(/\r?\n|\r\n?/g,"\r\n")):t+=`Content-Type: ${a.type||"application/octet-stream"}\r
`,this.headers=ao.encode(t+"\r\n"),this.contentLength=i?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async *encode(){yield this.headers;let{value:e}=this;eg.isTypedArray(e)?yield e:yield*ai(e),yield as}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let ac=(e,a,n)=>{let{tag:i="form-data-boundary",size:t=25,boundary:o=i+"-"+eG.generateString(t,at)}=n||{};if(!eg.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let s=ao.encode("--"+o+"\r\n"),r=ao.encode("--"+o+"--\r\n"),c=r.byteLength,p=Array.from(e.entries()).map(([e,a])=>{let n=new ar(e,a);return c+=n.size,n});c+=s.byteLength*p.length;let l={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(c=eg.toFiniteNumber(c))&&(l["Content-Length"]=c),a&&a(l),k.Readable.from(async function*(){for(let e of p)yield s,yield*e.encode();yield r}())};class ap extends F.default.Transform{__transform(e,a,n){this.push(e),n()}_transform(e,a,n){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,n)}}let al=(e,a)=>eg.isAsyncFn(e)?function(...n){let i=n.pop();e.apply(this,n).then(e=>{try{a?i(null,...a(e)):i(null,e)}catch(e){i(e)}},i)}:e,au=(e,a,n=3)=>{let i=0,t=function(e,a){let n;let i=Array(e=e||10),t=Array(e),o=0,s=0;return a=void 0!==a?a:1e3,function(r){let c=Date.now(),p=t[s];n||(n=c),i[o]=r,t[o]=c;let l=s,u=0;for(;l!==o;)u+=i[l++],l%=e;if((o=(o+1)%e)===s&&(s=(s+1)%e),c-n<a)return;let d=p&&c-p;return d?Math.round(1e3*u/d):void 0}}(50,250);return function(e,a){let n,i,t=0,o=1e3/a,s=(a,o=Date.now())=>{t=o,n=null,i&&(clearTimeout(i),i=null),e(...a)};return[(...e)=>{let a=Date.now(),r=a-t;r>=o?s(e,a):(n=e,i||(i=setTimeout(()=>{i=null,s(n)},o-r)))},()=>n&&s(n)]}(n=>{let o=n.loaded,s=n.lengthComputable?n.total:void 0,r=o-i,c=t(r);i=o,e({loaded:o,total:s,progress:s?o/s:void 0,bytes:r,rate:c||void 0,estimated:c&&s&&o<=s?(s-o)/c:void 0,event:n,lengthComputable:null!=s,[a?"download":"upload"]:!0})},n)},ad=(e,a)=>{let n=null!=e;return[i=>a[0]({lengthComputable:n,total:e,loaded:i}),a[1]]},am=e=>(...a)=>eg.asap(()=>e(...a)),af={flush:z.default.constants.Z_SYNC_FLUSH,finishFlush:z.default.constants.Z_SYNC_FLUSH},ax={flush:z.default.constants.BROTLI_OPERATION_FLUSH,finishFlush:z.default.constants.BROTLI_OPERATION_FLUSH},av=eg.isFunction(z.default.createBrotliDecompress),{http:ah,https:ab}=P.default,ag=/https:?/,ay=eG.protocols.map(e=>e+":"),aw=(e,[a,n])=>(e.on("end",n).on("error",n),a);function ak(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}let aj="undefined"!=typeof process&&"process"===eg.kindOf(process),a_=e=>new Promise((a,n)=>{let i,t;let o=(e,a)=>{!t&&(t=!0,i&&i(e,a))},s=e=>{o(e,!0),n(e)};e(e=>{o(e),a(e)},s,e=>i=e).catch(s)}),aE=({address:e,family:a})=>{if(!eg.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(0>e.indexOf(".")?6:4)}},aO=(e,a)=>aE(eg.isObject(e)?e:{address:e,family:a}),aR=aj&&function(e){return a_(async function(a,n,i){let t,o,s,r,c,p,l,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:x}=e,v=e.method.toUpperCase(),h=!1;if(d){let e=al(d,e=>eg.isArray(e)?e:[e]);d=(a,n,i)=>{e(a,n,(e,a,t)=>{if(e)return i(e);let o=eg.isArray(a)?a.map(e=>aO(e)):[aO(a,t)];n.all?i(e,o):i(e,o[0].address,o[0].family)})}}let b=new j.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(a){b.emit("abort",!a||a.type?new e6(null,e,c):a)}i((e,a)=>{r=!0,a&&(h=!0,g())}),b.once("abort",n),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(e9(e.baseURL,e.url,e.allowAbsoluteUrls),eG.hasBrowserEnv?eG.origin:void 0),k=w.protocol||ay[0];if("data:"===k){let i;if("GET"!==v)return e4(a,n,{status:405,statusText:"method not allowed",headers:{},config:e});try{i=function(e,a,n){let i=n&&n.Blob||eG.classes.Blob,t=e7(e);if(void 0===a&&i&&(a=!0),"data"===t){e=t.length?e.slice(t.length+1):e;let n=e5.exec(e);if(!n)throw new ey("Invalid URL",ey.ERR_INVALID_URL);let o=n[1],s=n[2],r=n[3],c=Buffer.from(decodeURIComponent(r),s?"base64":"utf8");if(a){if(!i)throw new ey("Blob is not supported",ey.ERR_NOT_SUPPORT);return new i([c],{type:o})}return c}throw new ey("Unsupported protocol "+t,ey.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(a){throw ey.from(a,ey.ERR_BAD_REQUEST,e)}return"text"===f?(i=i.toString(x),x&&"utf8"!==x||(i=eg.stripBOM(i))):"stream"===f&&(i=F.default.Readable.from(i)),e4(a,n,{data:i,status:200,statusText:"OK",headers:new e1,config:e})}if(-1===ay.indexOf(k))return n(new ey("Unsupported protocol "+k,ey.ERR_BAD_REQUEST,e));let _=e1.from(e.headers).normalize();_.set("User-Agent","axios/"+e8,!1);let{onUploadProgress:E,onDownloadProgress:O}=e,R=e.maxRate;if(eg.isSpecCompliantForm(u)){let e=_.getContentType(/boundary=([-_\w\d]{10,70})/i);u=ac(u,e=>{_.set(e)},{tag:`axios-${e8}-boundary`,boundary:e&&e[1]||void 0})}else if(eg.isFormData(u)&&eg.isFunction(u.getHeaders)){if(_.set(u.getHeaders()),!_.hasContentLength())try{let e=await T.default.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&_.setContentLength(e)}catch(e){}}else if(eg.isBlob(u)||eg.isFile(u))u.size&&_.setContentType(u.type||"application/octet-stream"),_.setContentLength(u.size||0),u=F.default.Readable.from(ai(u));else if(u&&!eg.isStream(u)){if(Buffer.isBuffer(u));else if(eg.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!eg.isString(u))return n(new ey("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",ey.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(_.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return n(new ey("Request body larger than maxBodyLength limit",ey.ERR_BAD_REQUEST,e))}let P=eg.toFiniteNumber(_.getContentLength());eg.isArray(R)?(t=R[0],o=R[1]):t=o=R,u&&(E||t)&&(eg.isStream(u)||(u=F.default.Readable.from(u,{objectMode:!1})),u=F.default.pipeline([u,new aa({maxRate:eg.toFiniteNumber(t)})],eg.noop),E&&u.on("progress",aw(u,ad(P,au(am(E),!1,3))))),e.auth&&(s=(e.auth.username||"")+":"+(e.auth.password||"")),!s&&w.username&&(s=w.username+":"+w.password),s&&_.delete("authorization");try{p=eP(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(i){let a=Error(i.message);return a.config=e,a.url=e.url,a.exists=!0,n(a)}_.set("Accept-Encoding","gzip, compress, deflate"+(av?", br":""),!1);let N={path:p,method:v,headers:_.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:s,protocol:k,family:m,beforeRedirect:ak,beforeRedirects:{}};eg.isUndefined(d)||(N.lookup=d),e.socketPath?N.socketPath=e.socketPath:(N.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,N.port=w.port,function e(a,n,i){let t=n;if(!t&&!1!==t){let e=S.default.getProxyForUrl(i);e&&(t=new URL(e))}if(t){if(t.username&&(t.auth=(t.username||"")+":"+(t.password||"")),t.auth){(t.auth.username||t.auth.password)&&(t.auth=(t.auth.username||"")+":"+(t.auth.password||""));let e=Buffer.from(t.auth,"utf8").toString("base64");a.headers["Proxy-Authorization"]="Basic "+e}a.headers.host=a.hostname+(a.port?":"+a.port:"");let e=t.hostname||t.host;a.hostname=e,a.host=e,a.port=t.port,a.path=i,t.protocol&&(a.protocol=t.protocol.includes(":")?t.protocol:`${t.protocol}:`)}a.beforeRedirects.proxy=function(a){e(a,n,a.href)}}(N,e.proxy,k+"//"+w.hostname+(w.port?":"+w.port:"")+N.path));let U=ag.test(N.protocol);if(N.agent=U?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=U?C.default:A.default:(e.maxRedirects&&(N.maxRedirects=e.maxRedirects),e.beforeRedirect&&(N.beforeRedirects.config=e.beforeRedirect),l=U?ab:ah),e.maxBodyLength>-1?N.maxBodyLength=e.maxBodyLength:N.maxBodyLength=1/0,e.insecureHTTPParser&&(N.insecureHTTPParser=e.insecureHTTPParser),c=l.request(N,function(i){if(c.destroyed)return;let t=[i],s=+i.headers["content-length"];if(O||o){let e=new aa({maxRate:eg.toFiniteNumber(o)});O&&e.on("progress",aw(e,ad(s,au(am(O),!0,3)))),t.push(e)}let r=i,p=i.req||c;if(!1!==e.decompress&&i.headers["content-encoding"])switch(("HEAD"===v||204===i.statusCode)&&delete i.headers["content-encoding"],(i.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":t.push(z.default.createUnzip(af)),delete i.headers["content-encoding"];break;case"deflate":t.push(new ap),t.push(z.default.createUnzip(af)),delete i.headers["content-encoding"];break;case"br":av&&(t.push(z.default.createBrotliDecompress(ax)),delete i.headers["content-encoding"])}r=t.length>1?F.default.pipeline(t,eg.noop):t[0];let l=F.default.finished(r,()=>{l(),g()}),u={status:i.statusCode,statusText:i.statusMessage,headers:new e1(i.headers),config:e,request:p};if("stream"===f)u.data=r,e4(a,n,u);else{let i=[],t=0;r.on("data",function(a){i.push(a),t+=a.length,e.maxContentLength>-1&&t>e.maxContentLength&&(h=!0,r.destroy(),n(new ey("maxContentLength size of "+e.maxContentLength+" exceeded",ey.ERR_BAD_RESPONSE,e,p)))}),r.on("aborted",function(){if(h)return;let a=new ey("stream has been aborted",ey.ERR_BAD_RESPONSE,e,p);r.destroy(a),n(a)}),r.on("error",function(a){c.destroyed||n(ey.from(a,null,e,p))}),r.on("end",function(){try{let e=1===i.length?i[0]:Buffer.concat(i);"arraybuffer"===f||(e=e.toString(x),x&&"utf8"!==x||(e=eg.stripBOM(e))),u.data=e}catch(a){return n(ey.from(a,null,e,u.request,u))}e4(a,n,u)})}b.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),b.once("abort",e=>{n(e),c.destroy(e)}),c.on("error",function(a){n(ey.from(a,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let a=parseInt(e.timeout,10);if(Number.isNaN(a)){n(new ey("error trying to parse `config.timeout` to int",ey.ERR_BAD_OPTION_VALUE,e,c));return}c.setTimeout(a,function(){if(r)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",i=e.transitional||eF;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),n(new ey(a,i.clarifyTimeoutError?ey.ETIMEDOUT:ey.ECONNABORTED,e,c)),y()})}if(eg.isStream(u)){let a=!1,n=!1;u.on("end",()=>{a=!0}),u.once("error",e=>{n=!0,c.destroy(e)}),u.on("close",()=>{a||n||y(new e6("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},aS=eG.hasStandardBrowserEnv?(c=new URL(eG.origin),p=eG.navigator&&/(msie|trident)/i.test(eG.navigator.userAgent),e=>(e=new URL(e,eG.origin),c.protocol===e.protocol&&c.host===e.host&&(p||c.port===e.port))):()=>!0,aA=eG.hasStandardBrowserEnv?{write(e,a,n,i,t,o){let s=[e+"="+encodeURIComponent(a)];eg.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),eg.isString(i)&&s.push("path="+i),eg.isString(t)&&s.push("domain="+t),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){let a=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},aC=e=>e instanceof e1?{...e}:e;function aT(e,a){a=a||{};let n={};function i(e,a,n,i){return eg.isPlainObject(e)&&eg.isPlainObject(a)?eg.merge.call({caseless:i},e,a):eg.isPlainObject(a)?eg.merge({},a):eg.isArray(a)?a.slice():a}function t(e,a,n,t){return eg.isUndefined(a)?eg.isUndefined(e)?void 0:i(void 0,e,n,t):i(e,a,n,t)}function o(e,a){if(!eg.isUndefined(a))return i(void 0,a)}function s(e,a){return eg.isUndefined(a)?eg.isUndefined(e)?void 0:i(void 0,e):i(void 0,a)}function r(n,t,o){return o in a?i(n,t):o in e?i(void 0,n):void 0}let c={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:r,headers:(e,a,n)=>t(aC(e),aC(a),n,!0)};return eg.forEach(Object.keys({...e,...a}),function(i){let o=c[i]||t,s=o(e[i],a[i],i);eg.isUndefined(s)&&o!==r||(n[i]=s)}),n}let aP=e=>{let a;let n=aT({},e),{data:i,withXSRFToken:t,xsrfHeaderName:o,xsrfCookieName:s,headers:r,auth:c}=n;if(n.headers=r=e1.from(r),n.url=eP(e9(n.baseURL,n.url,n.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),eg.isFormData(i)){if(eG.hasStandardBrowserEnv||eG.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(a=r.getContentType())){let[e,...n]=a?a.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...n].join("; "))}}if(eG.hasStandardBrowserEnv&&(t&&eg.isFunction(t)&&(t=t(n)),t||!1!==t&&aS(n.url))){let e=o&&s&&aA.read(s);e&&r.set(o,e)}return n},az="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(a,n){let i,t,o,s,r;let c=aP(e),p=c.data,l=e1.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function f(){s&&s(),r&&r(),c.cancelToken&&c.cancelToken.unsubscribe(i),c.signal&&c.signal.removeEventListener("abort",i)}let x=new XMLHttpRequest;function v(){if(!x)return;let i=e1.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders());e4(function(e){a(e),f()},function(e){n(e),f()},{data:u&&"text"!==u&&"json"!==u?x.response:x.responseText,status:x.status,statusText:x.statusText,headers:i,config:e,request:x}),x=null}x.open(c.method.toUpperCase(),c.url,!0),x.timeout=c.timeout,"onloadend"in x?x.onloadend=v:x.onreadystatechange=function(){x&&4===x.readyState&&(0!==x.status||x.responseURL&&0===x.responseURL.indexOf("file:"))&&setTimeout(v)},x.onabort=function(){x&&(n(new ey("Request aborted",ey.ECONNABORTED,e,x)),x=null)},x.onerror=function(){n(new ey("Network Error",ey.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let a=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",i=c.transitional||eF;c.timeoutErrorMessage&&(a=c.timeoutErrorMessage),n(new ey(a,i.clarifyTimeoutError?ey.ETIMEDOUT:ey.ECONNABORTED,e,x)),x=null},void 0===p&&l.setContentType(null),"setRequestHeader"in x&&eg.forEach(l.toJSON(),function(e,a){x.setRequestHeader(a,e)}),eg.isUndefined(c.withCredentials)||(x.withCredentials=!!c.withCredentials),u&&"json"!==u&&(x.responseType=c.responseType),m&&([o,r]=au(m,!0),x.addEventListener("progress",o)),d&&x.upload&&([t,s]=au(d),x.upload.addEventListener("progress",t),x.upload.addEventListener("loadend",s)),(c.cancelToken||c.signal)&&(i=a=>{x&&(n(!a||a.type?new e6(null,e,x):a),x.abort(),x=null)},c.cancelToken&&c.cancelToken.subscribe(i),c.signal&&(c.signal.aborted?i():c.signal.addEventListener("abort",i)));let h=e7(c.url);if(h&&-1===eG.protocols.indexOf(h)){n(new ey("Unsupported protocol "+h+":",ey.ERR_BAD_REQUEST,e));return}x.send(p||null)})},aF=(e,a)=>{let{length:n}=e=e?e.filter(Boolean):[];if(a||n){let n,i=new AbortController,t=function(e){if(!n){n=!0,s();let a=e instanceof Error?e:this.reason;i.abort(a instanceof ey?a:new e6(a instanceof Error?a.message:a))}},o=a&&setTimeout(()=>{o=null,t(new ey(`timeout ${a} of ms exceeded`,ey.ETIMEDOUT))},a),s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(t):e.removeEventListener("abort",t)}),e=null)};e.forEach(e=>e.addEventListener("abort",t));let{signal:r}=i;return r.unsubscribe=()=>eg.asap(s),r}},aN=function*(e,a){let n,i=e.byteLength;if(!a||i<a){yield e;return}let t=0;for(;t<i;)n=t+a,yield e.slice(t,n),t=n},aU=async function*(e,a){for await(let n of aq(e))yield*aN(n,a)},aq=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let a=e.getReader();try{for(;;){let{done:e,value:n}=await a.read();if(e)break;yield n}}finally{await a.cancel()}},aB=(e,a,n,i)=>{let t;let o=aU(e,a),s=0,r=e=>{!t&&(t=!0,i&&i(e))};return new ReadableStream({async pull(e){try{let{done:a,value:i}=await o.next();if(a){r(),e.close();return}let t=i.byteLength;if(n){let e=s+=t;n(e)}e.enqueue(new Uint8Array(i))}catch(e){throw r(e),e}},cancel:e=>(r(e),o.return())},{highWaterMark:2})},aL="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,aI=aL&&"function"==typeof ReadableStream,aD=aL&&("function"==typeof TextEncoder?(l=new TextEncoder,e=>l.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),aM=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},aH=aI&&aM(()=>{let e=!1,a=new Request(eG.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a}),aW=aI&&aM(()=>eg.isReadableStream(new Response("").body)),aG={stream:aW&&(e=>e.body)};aL&&(o=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{aG[e]||(aG[e]=eg.isFunction(o[e])?a=>a[e]():(a,n)=>{throw new ey(`Response type '${e}' is not supported`,ey.ERR_NOT_SUPPORT,n)})}));let a$=async e=>{if(null==e)return 0;if(eg.isBlob(e))return e.size;if(eg.isSpecCompliantForm(e)){let a=new Request(eG.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return eg.isArrayBufferView(e)||eg.isArrayBuffer(e)?e.byteLength:(eg.isURLSearchParams(e)&&(e+=""),eg.isString(e))?(await aD(e)).byteLength:void 0},aJ=async(e,a)=>{let n=eg.toFiniteNumber(e.getContentLength());return null==n?a$(a):n},aV={http:aR,xhr:az,fetch:aL&&(async e=>{let a,n,{url:i,method:t,data:o,signal:s,cancelToken:r,timeout:c,onDownloadProgress:p,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:f}=aP(e);u=u?(u+"").toLowerCase():"text";let x=aF([s,r&&r.toAbortSignal()],c),v=x&&x.unsubscribe&&(()=>{x.unsubscribe()});try{if(l&&aH&&"get"!==t&&"head"!==t&&0!==(n=await aJ(d,o))){let e,a=new Request(i,{method:"POST",body:o,duplex:"half"});if(eg.isFormData(o)&&(e=a.headers.get("content-type"))&&d.setContentType(e),a.body){let[e,i]=ad(n,au(am(l)));o=aB(a.body,65536,e,i)}}eg.isString(m)||(m=m?"include":"omit");let s="credentials"in Request.prototype;a=new Request(i,{...f,signal:x,method:t.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:s?m:void 0});let r=await fetch(a,f),c=aW&&("stream"===u||"response"===u);if(aW&&(p||c&&v)){let e={};["status","statusText","headers"].forEach(a=>{e[a]=r[a]});let a=eg.toFiniteNumber(r.headers.get("content-length")),[n,i]=p&&ad(a,au(am(p),!0))||[];r=new Response(aB(r.body,65536,n,()=>{i&&i(),v&&v()}),e)}u=u||"text";let h=await aG[eg.findKey(aG,u)||"text"](r,e);return!c&&v&&v(),await new Promise((n,i)=>{e4(n,i,{data:h,headers:e1.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:a})})}catch(n){if(v&&v(),n&&"TypeError"===n.name&&/Load failed|fetch/i.test(n.message))throw Object.assign(new ey("Network Error",ey.ERR_NETWORK,e,a),{cause:n.cause||n});throw ey.from(n,n&&n.code,e,a)}})};eg.forEach(aV,(e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}});let aK=e=>`- ${e}`,aY=e=>eg.isFunction(e)||null===e||!1===e,aX={getAdapter:e=>{let a,n;let{length:i}=e=eg.isArray(e)?e:[e],t={};for(let o=0;o<i;o++){let i;if(n=a=e[o],!aY(a)&&void 0===(n=aV[(i=String(a)).toLowerCase()]))throw new ey(`Unknown adapter '${i}'`);if(n)break;t[i||"#"+o]=n}if(!n){let e=Object.entries(t).map(([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build"));throw new ey("There is no suitable adapter to dispatch the request "+(i?e.length>1?"since :\n"+e.map(aK).join("\n"):" "+aK(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return n}};function aQ(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new e6(null,e)}function aZ(e){return aQ(e),e.headers=e1.from(e.headers),e.data=e2.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),aX.getAdapter(e.adapter||eJ.adapter)(e).then(function(a){return aQ(e),a.data=e2.call(e,e.transformResponse,a),a.headers=e1.from(a.headers),a},function(a){return!e3(a)&&(aQ(e),a&&a.response&&(a.response.data=e2.call(e,e.transformResponse,a.response),a.response.headers=e1.from(a.response.headers))),Promise.reject(a)})}let a0={};["object","boolean","number","function","string","symbol"].forEach((e,a)=>{a0[e]=function(n){return typeof n===e||"a"+(a<1?"n ":" ")+e}});let a1={};a0.transitional=function(e,a,n){function i(e,a){return"[Axios v"+e8+"] Transitional option '"+e+"'"+a+(n?". "+n:"")}return(n,t,o)=>{if(!1===e)throw new ey(i(t," has been removed"+(a?" in "+a:"")),ey.ERR_DEPRECATED);return a&&!a1[t]&&(a1[t]=!0,console.warn(i(t," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(n,t,o)}},a0.spelling=function(e){return(a,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};let a2={assertOptions:function(e,a,n){if("object"!=typeof e)throw new ey("options must be an object",ey.ERR_BAD_OPTION_VALUE);let i=Object.keys(e),t=i.length;for(;t-- >0;){let o=i[t],s=a[o];if(s){let a=e[o],n=void 0===a||s(a,o,e);if(!0!==n)throw new ey("option "+o+" must be "+n,ey.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new ey("Unknown option "+o,ey.ERR_BAD_OPTION)}},validators:a0},a3=a2.validators;class a6{constructor(e){this.defaults=e||{},this.interceptors={request:new ez,response:new ez}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=Error();let n=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}catch(e){}}throw e}}_request(e,a){let n,i;"string"==typeof e?(a=a||{}).url=e:a=e||{};let{transitional:t,paramsSerializer:o,headers:s}=a=aT(this.defaults,a);void 0!==t&&a2.assertOptions(t,{silentJSONParsing:a3.transitional(a3.boolean),forcedJSONParsing:a3.transitional(a3.boolean),clarifyTimeoutError:a3.transitional(a3.boolean)},!1),null!=o&&(eg.isFunction(o)?a.paramsSerializer={serialize:o}:a2.assertOptions(o,{encode:a3.function,serialize:a3.function},!0)),void 0!==a.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?a.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:a.allowAbsoluteUrls=!0),a2.assertOptions(a,{baseUrl:a3.spelling("baseURL"),withXsrfToken:a3.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let r=s&&eg.merge(s.common,s[a.method]);s&&eg.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),a.headers=e1.concat(r,s);let c=[],p=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(a))&&(p=p&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!p){let e=[aZ.bind(this),void 0];for(e.unshift(...c),e.push(...l),i=e.length,n=Promise.resolve(a);u<i;)n=n.then(e[u++],e[u++]);return n}i=c.length;let d=a;for(u=0;u<i;){let e=c[u++],a=c[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{n=aZ.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,i=l.length;u<i;)n=n.then(l[u++],l[u++]);return n}getUri(e){return eP(e9((e=aT(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}eg.forEach(["delete","get","head","options"],function(e){a6.prototype[e]=function(a,n){return this.request(aT(n||{},{method:e,url:a,data:(n||{}).data}))}}),eg.forEach(["post","put","patch"],function(e){function a(a){return function(n,i,t){return this.request(aT(t||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:n,data:i}))}}a6.prototype[e]=a(),a6.prototype[e+"Form"]=a(!0)});class a4{constructor(e){let a;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){a=e});let n=this;this.promise.then(e=>{if(!n._listeners)return;let a=n._listeners.length;for(;a-- >0;)n._listeners[a](e);n._listeners=null}),this.promise.then=e=>{let a;let i=new Promise(e=>{n.subscribe(e),a=e}).then(e);return i.cancel=function(){n.unsubscribe(a)},i},e(function(e,i,t){n.reason||(n.reason=new e6(e,i,t),a(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new a4(function(a){e=a}),cancel:e}}}let a9={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(a9).forEach(([e,a])=>{a9[a]=e});let a8=function e(a){let n=new a6(a),i=N(a6.prototype.request,n);return eg.extend(i,a6.prototype,n,{allOwnKeys:!0}),eg.extend(i,n,null,{allOwnKeys:!0}),i.create=function(n){return e(aT(a,n))},i}(eJ);a8.Axios=a6,a8.CanceledError=e6,a8.CancelToken=a4,a8.isCancel=e3,a8.VERSION=e8,a8.toFormData=eR,a8.AxiosError=ey,a8.Cancel=a8.CanceledError,a8.all=function(e){return Promise.all(e)},a8.spread=function(e){return function(a){return e.apply(null,a)}},a8.isAxiosError=function(e){return eg.isObject(e)&&!0===e.isAxiosError},a8.mergeConfig=aT,a8.AxiosHeaders=e1,a8.formToJSON=e=>e$(eg.isHTMLForm(e)?new FormData(e):e),a8.getAdapter=aX.getAdapter,a8.HttpStatusCode=a9,a8.default=a8,e.exports=a8},2753:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')},74667:e=>{"use strict";e.exports=JSON.parse('{"name":"razorpay","version":"2.9.6","description":"Official Node SDK for Razorpay API","main":"dist/razorpay","typings":"dist/razorpay","scripts":{"prepublish":"npm test","clean":"rm -rf dist && mkdir dist","cp-types":"mkdir dist/types && cp lib/types/* dist/types && cp lib/utils/*d.ts dist/utils","cp-ts":"cp lib/razorpay.d.ts dist/ && cp lib/oAuthTokenClient.d.ts dist/ && npm run cp-types","build:commonjs":"babel lib -d dist","build":"npm run clean && npm run build:commonjs && npm run cp-ts","debug":"npm run build && node-debug examples/index.js","test":"npm run build && mocha --recursive --require babel-register test/ && nyc --reporter=text mocha","coverage":"nyc report --reporter=text-lcov > coverage.lcov"},"repository":{"type":"git","url":"https://github.com/razorpay/razorpay-node.git"},"keywords":["razorpay","payments","node","nodejs","razorpay-node"],"files":["dist"],"mocha":{"recursive":true,"full-trace":true},"license":"MIT","devDependencies":{"@types/node":"^20.12.12","babel-cli":"^6.26.0","babel-preset-env":"^1.7.0","babel-preset-stage-0":"^6.24.0","babel-register":"^6.26.0","chai":"^4.3.4","deep-equal":"^2.0.5","mocha":"^9.0.0","nock":"^13.1.1","nyc":"^15.1.0","typescript":"^4.9.4"},"dependencies":{"axios":"^1.6.8"}}')}};var a=require("../../../../webpack-runtime.js");a.C(e);var n=e=>a(a.s=e),i=a.X(0,[8948,5972],()=>n(63734));module.exports=i})();