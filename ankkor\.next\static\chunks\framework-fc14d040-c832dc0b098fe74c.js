"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8989],{37253:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Router:function(){return a.default},createRouter:function(){return _},default:function(){return p},makePublicRouterInstance:function(){return b},useRouter:function(){return y},withRouter:function(){return i.default}});let n=r(38754),o=n._(r(67294)),a=n._(r(29668)),u=r(37171),l=n._(r(80676)),i=n._(r(538)),s={router:null,readyCallbacks:[],ready(e){if(this.router)return e();this.readyCallbacks.push(e)}},d=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],f=["push","replace","reload","back","prefetch","beforePopState"];function c(){if(!s.router)throw Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n');return s.router}Object.defineProperty(s,"events",{get:()=>a.default.events}),d.forEach(e=>{Object.defineProperty(s,e,{get:()=>c()[e]})}),f.forEach(e=>{s[e]=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return c()[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{s.ready(()=>{a.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let o="on"+e.charAt(0).toUpperCase()+e.substring(1);if(s[o])try{s[o](...r)}catch(e){console.error("Error when running the Router event: "+o),console.error((0,l.default)(e)?e.message+"\n"+e.stack:e+"")}})})});let p=s;function y(){let e=o.default.useContext(u.RouterContext);if(!e)throw Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted");return e}function _(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return s.router=new a.default(...t),s.readyCallbacks.forEach(e=>e()),s.readyCallbacks=[],s.router}function b(e){let t={};for(let r of d){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=a.default.events,f.forEach(r=>{t[r]=function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return e[r](...n)}}),t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95026:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return v},handleClientScriptLoad:function(){return b},initScriptLoader:function(){return h}});let n=r(38754),o=r(61757),a=r(85893),u=n._(r(73935)),l=o._(r(67294)),i=r(20077),s=r(52071),d=r(66078),f=new Map,c=new Set,p=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],y=e=>{if(u.default.preinit){e.forEach(e=>{u.default.preinit(e,{as:"style"})});return}{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},_=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:o=null,dangerouslySetInnerHTML:a,children:u="",strategy:l="afterInteractive",onError:i,stylesheets:d}=e,_=r||t;if(_&&c.has(_))return;if(f.has(t)){c.add(_),f.get(t).then(n,i);return}let b=()=>{o&&o(),c.add(_)},h=document.createElement("script"),g=new Promise((e,t)=>{h.addEventListener("load",function(t){e(),n&&n.call(this,t),b()}),h.addEventListener("error",function(e){t(e)})}).catch(function(e){i&&i(e)});for(let[r,n]of(a?(h.innerHTML=a.__html||"",b()):u?(h.textContent="string"==typeof u?u:Array.isArray(u)?u.join(""):"",b()):t&&(h.src=t,f.set(t,g)),Object.entries(e))){if(void 0===n||p.includes(r))continue;let e=s.DOMAttributeNames[r]||r.toLowerCase();h.setAttribute(e,n)}"worker"===l&&h.setAttribute("type","text/partytown"),h.setAttribute("data-nscript",l),d&&y(d),document.body.appendChild(h)};function b(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>_(e))}):_(e)}function h(e){e.forEach(b),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");c.add(t)})}function g(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:o=null,strategy:s="afterInteractive",onError:f,stylesheets:p,...y}=e,{updateScripts:b,scripts:h,getIsSsr:g,appDir:v,nonce:m}=(0,l.useContext)(i.HeadManagerContext),O=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||r;O.current||(o&&e&&c.has(e)&&o(),O.current=!0)},[o,t,r]);let j=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{!j.current&&("afterInteractive"===s?_(e):"lazyOnload"===s&&("complete"===document.readyState?(0,d.requestIdleCallback)(()=>_(e)):window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>_(e))})),j.current=!0)},[e,s]),("beforeInteractive"===s||"worker"===s)&&(b?(h[s]=(h[s]||[]).concat([{id:t,src:r,onLoad:n,onReady:o,onError:f,...y}]),b(h)):g&&g()?c.add(t||r):g&&!g()&&_(e)),v){if(p&&p.forEach(e=>{u.default.preinit(e,{as:"style"})}),"beforeInteractive"===s)return r?(u.default.preload(r,y.integrity?{as:"script",integrity:y.integrity,nonce:m,crossOrigin:y.crossOrigin}:{as:"script",nonce:m,crossOrigin:y.crossOrigin}),(0,a.jsx)("script",{nonce:m,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...y,id:t}])+")"}})):(y.dangerouslySetInnerHTML&&(y.children=y.dangerouslySetInnerHTML.__html,delete y.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:m,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...y,id:t}])+")"}}));"afterInteractive"===s&&r&&u.default.preload(r,y.integrity?{as:"script",integrity:y.integrity,nonce:m,crossOrigin:y.crossOrigin}:{as:"script",nonce:m,crossOrigin:y.crossOrigin})}return null}Object.defineProperty(g,"__nextScript",{value:!0});let v=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95179:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(45303);function o(e){if("ended"!==e.state.state)throw Error("Expected span to be ended");(0,n.sendMessage)(JSON.stringify({event:"span-end",startTime:e.startTime,endTime:e.state.endTime,spanName:e.name,attributes:e.attributes}))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57930:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(38754)._(r(58967));class o{end(e){if("ended"===this.state.state)throw Error("Span has already ended");this.state={state:"ended",endTime:null!=e?e:Date.now()},this.onSpanEnd(this)}constructor(e,t,r){var n,o;this.name=e,this.attributes=null!=(n=t.attributes)?n:{},this.startTime=null!=(o=t.startTime)?o:Date.now(),this.onSpanEnd=r,this.state={state:"inprogress"}}}class a{startSpan(e,t){return new o(e,t,this.handleSpanEnd)}onSpanEnd(e){return this._emitter.on("spanend",e),()=>{this._emitter.off("spanend",e)}}constructor(){this._emitter=(0,n.default)(),this.handleSpanEnd=e=>{this._emitter.emit("spanend",e)}}}let u=new a;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92518:function(e,t){let r;function n(e){var t;return(null==(t=function(){if(void 0===r){var e;r=(null==(e=window.trustedTypes)?void 0:e.createPolicy("nextjs",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e}))||null}return r}())?void 0:t.createScriptURL(e))||e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99151:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),r(84878),self.__next_set_public_path__=e=>{r.p=e},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},538:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),r(38754);let n=r(85893);r(67294);let o=r(37253);function a(e){function t(t){return(0,n.jsx)(e,{router:(0,o.useRouter)(),...t})}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16081:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return i}});let n=r(2265),o=r(63515),a="function"==typeof IntersectionObserver,u=new Map,l=[];function i(e){let{rootRef:t,rootMargin:r,disabled:i}=e,s=i||!a,[d,f]=(0,n.useState)(!1),c=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{c.current=e},[]);return(0,n.useEffect)(()=>{if(a){if(s||d)return;let e=c.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:o,elements:a}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=l.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=u.get(n)))return t;let o=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:o},l.push(r),u.set(r,t),t}(r);return a.set(e,t),o.observe(e),function(){if(a.delete(e),o.unobserve(e),0===a.size){o.disconnect(),u.delete(n);let e=l.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&l.splice(e,1)}}}(e,e=>e&&f(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!d){let e=(0,o.requestIdleCallback)(()=>f(!0));return()=>(0,o.cancelIdleCallback)(e)}},[s,r,t,d,c.current]),[p,d,(0,n.useCallback)(()=>{f(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);