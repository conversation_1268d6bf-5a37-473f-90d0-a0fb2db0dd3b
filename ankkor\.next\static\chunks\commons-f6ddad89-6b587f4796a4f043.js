"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4615],{95260:function(e,t,i){i.d(t,{Qc:function(){return l}});var s=i(85338),n=i(93084),r=i(14081),a=i(50499),h=i(94507),o=i(99590),p=i(67139);function l(e,t){let i=new c(e,t),s=i.parseDocument();return Object.defineProperty(s,"tokenCount",{enumerable:!1,value:i.tokenCount}),s}class c{constructor(e,t={}){let i=(0,o.T)(e)?e:new o.H(e);this._lexer=new h.h(i),this._options=t,this._tokenCounter=0}get tokenCount(){return this._tokenCounter}parseName(){let e=this.expectToken(p.T.NAME);return this.node(e,{kind:a.h.NAME,value:e.value})}parseDocument(){return this.node(this._lexer.token,{kind:a.h.DOCUMENT,definitions:this.many(p.T.SOF,this.parseDefinition,p.T.EOF)})}parseDefinition(){if(this.peek(p.T.BRACE_L))return this.parseOperationDefinition();let e=this.peekDescription(),t=e?this._lexer.lookahead():this._lexer.token;if(t.kind===p.T.NAME){switch(t.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}if(e)throw(0,s.h)(this._lexer.source,this._lexer.token.start,"Unexpected description, descriptions are supported only on type definitions.");switch(t.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"extend":return this.parseTypeSystemExtension()}}throw this.unexpected(t)}parseOperationDefinition(){let e;let t=this._lexer.token;if(this.peek(p.T.BRACE_L))return this.node(t,{kind:a.h.OPERATION_DEFINITION,operation:n.ku.QUERY,name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet()});let i=this.parseOperationType();return this.peek(p.T.NAME)&&(e=this.parseName()),this.node(t,{kind:a.h.OPERATION_DEFINITION,operation:i,name:e,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseOperationType(){let e=this.expectToken(p.T.NAME);switch(e.value){case"query":return n.ku.QUERY;case"mutation":return n.ku.MUTATION;case"subscription":return n.ku.SUBSCRIPTION}throw this.unexpected(e)}parseVariableDefinitions(){return this.optionalMany(p.T.PAREN_L,this.parseVariableDefinition,p.T.PAREN_R)}parseVariableDefinition(){return this.node(this._lexer.token,{kind:a.h.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(p.T.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(p.T.EQUALS)?this.parseConstValueLiteral():void 0,directives:this.parseConstDirectives()})}parseVariable(){let e=this._lexer.token;return this.expectToken(p.T.DOLLAR),this.node(e,{kind:a.h.VARIABLE,name:this.parseName()})}parseSelectionSet(){return this.node(this._lexer.token,{kind:a.h.SELECTION_SET,selections:this.many(p.T.BRACE_L,this.parseSelection,p.T.BRACE_R)})}parseSelection(){return this.peek(p.T.SPREAD)?this.parseFragment():this.parseField()}parseField(){let e,t;let i=this._lexer.token,s=this.parseName();return this.expectOptionalToken(p.T.COLON)?(e=s,t=this.parseName()):t=s,this.node(i,{kind:a.h.FIELD,alias:e,name:t,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(p.T.BRACE_L)?this.parseSelectionSet():void 0})}parseArguments(e){let t=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(p.T.PAREN_L,t,p.T.PAREN_R)}parseArgument(e=!1){let t=this._lexer.token,i=this.parseName();return this.expectToken(p.T.COLON),this.node(t,{kind:a.h.ARGUMENT,name:i,value:this.parseValueLiteral(e)})}parseConstArgument(){return this.parseArgument(!0)}parseFragment(){let e=this._lexer.token;this.expectToken(p.T.SPREAD);let t=this.expectOptionalKeyword("on");return!t&&this.peek(p.T.NAME)?this.node(e,{kind:a.h.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1)}):this.node(e,{kind:a.h.INLINE_FRAGMENT,typeCondition:t?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentDefinition(){let e=this._lexer.token;return(this.expectKeyword("fragment"),!0===this._options.allowLegacyFragmentVariables)?this.node(e,{kind:a.h.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()}):this.node(e,{kind:a.h.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentName(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()}parseValueLiteral(e){let t=this._lexer.token;switch(t.kind){case p.T.BRACKET_L:return this.parseList(e);case p.T.BRACE_L:return this.parseObject(e);case p.T.INT:return this.advanceLexer(),this.node(t,{kind:a.h.INT,value:t.value});case p.T.FLOAT:return this.advanceLexer(),this.node(t,{kind:a.h.FLOAT,value:t.value});case p.T.STRING:case p.T.BLOCK_STRING:return this.parseStringLiteral();case p.T.NAME:switch(this.advanceLexer(),t.value){case"true":return this.node(t,{kind:a.h.BOOLEAN,value:!0});case"false":return this.node(t,{kind:a.h.BOOLEAN,value:!1});case"null":return this.node(t,{kind:a.h.NULL});default:return this.node(t,{kind:a.h.ENUM,value:t.value})}case p.T.DOLLAR:if(e){if(this.expectToken(p.T.DOLLAR),this._lexer.token.kind===p.T.NAME){let e=this._lexer.token.value;throw(0,s.h)(this._lexer.source,t.start,`Unexpected variable "$${e}" in constant value.`)}throw this.unexpected(t)}return this.parseVariable();default:throw this.unexpected()}}parseConstValueLiteral(){return this.parseValueLiteral(!0)}parseStringLiteral(){let e=this._lexer.token;return this.advanceLexer(),this.node(e,{kind:a.h.STRING,value:e.value,block:e.kind===p.T.BLOCK_STRING})}parseList(e){return this.node(this._lexer.token,{kind:a.h.LIST,values:this.any(p.T.BRACKET_L,()=>this.parseValueLiteral(e),p.T.BRACKET_R)})}parseObject(e){return this.node(this._lexer.token,{kind:a.h.OBJECT,fields:this.any(p.T.BRACE_L,()=>this.parseObjectField(e),p.T.BRACE_R)})}parseObjectField(e){let t=this._lexer.token,i=this.parseName();return this.expectToken(p.T.COLON),this.node(t,{kind:a.h.OBJECT_FIELD,name:i,value:this.parseValueLiteral(e)})}parseDirectives(e){let t=[];for(;this.peek(p.T.AT);)t.push(this.parseDirective(e));return t}parseConstDirectives(){return this.parseDirectives(!0)}parseDirective(e){let t=this._lexer.token;return this.expectToken(p.T.AT),this.node(t,{kind:a.h.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e)})}parseTypeReference(){let e;let t=this._lexer.token;if(this.expectOptionalToken(p.T.BRACKET_L)){let i=this.parseTypeReference();this.expectToken(p.T.BRACKET_R),e=this.node(t,{kind:a.h.LIST_TYPE,type:i})}else e=this.parseNamedType();return this.expectOptionalToken(p.T.BANG)?this.node(t,{kind:a.h.NON_NULL_TYPE,type:e}):e}parseNamedType(){return this.node(this._lexer.token,{kind:a.h.NAMED_TYPE,name:this.parseName()})}peekDescription(){return this.peek(p.T.STRING)||this.peek(p.T.BLOCK_STRING)}parseDescription(){if(this.peekDescription())return this.parseStringLiteral()}parseSchemaDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("schema");let i=this.parseConstDirectives(),s=this.many(p.T.BRACE_L,this.parseOperationTypeDefinition,p.T.BRACE_R);return this.node(e,{kind:a.h.SCHEMA_DEFINITION,description:t,directives:i,operationTypes:s})}parseOperationTypeDefinition(){let e=this._lexer.token,t=this.parseOperationType();this.expectToken(p.T.COLON);let i=this.parseNamedType();return this.node(e,{kind:a.h.OPERATION_TYPE_DEFINITION,operation:t,type:i})}parseScalarTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("scalar");let i=this.parseName(),s=this.parseConstDirectives();return this.node(e,{kind:a.h.SCALAR_TYPE_DEFINITION,description:t,name:i,directives:s})}parseObjectTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("type");let i=this.parseName(),s=this.parseImplementsInterfaces(),n=this.parseConstDirectives(),r=this.parseFieldsDefinition();return this.node(e,{kind:a.h.OBJECT_TYPE_DEFINITION,description:t,name:i,interfaces:s,directives:n,fields:r})}parseImplementsInterfaces(){return this.expectOptionalKeyword("implements")?this.delimitedMany(p.T.AMP,this.parseNamedType):[]}parseFieldsDefinition(){return this.optionalMany(p.T.BRACE_L,this.parseFieldDefinition,p.T.BRACE_R)}parseFieldDefinition(){let e=this._lexer.token,t=this.parseDescription(),i=this.parseName(),s=this.parseArgumentDefs();this.expectToken(p.T.COLON);let n=this.parseTypeReference(),r=this.parseConstDirectives();return this.node(e,{kind:a.h.FIELD_DEFINITION,description:t,name:i,arguments:s,type:n,directives:r})}parseArgumentDefs(){return this.optionalMany(p.T.PAREN_L,this.parseInputValueDef,p.T.PAREN_R)}parseInputValueDef(){let e;let t=this._lexer.token,i=this.parseDescription(),s=this.parseName();this.expectToken(p.T.COLON);let n=this.parseTypeReference();this.expectOptionalToken(p.T.EQUALS)&&(e=this.parseConstValueLiteral());let r=this.parseConstDirectives();return this.node(t,{kind:a.h.INPUT_VALUE_DEFINITION,description:i,name:s,type:n,defaultValue:e,directives:r})}parseInterfaceTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("interface");let i=this.parseName(),s=this.parseImplementsInterfaces(),n=this.parseConstDirectives(),r=this.parseFieldsDefinition();return this.node(e,{kind:a.h.INTERFACE_TYPE_DEFINITION,description:t,name:i,interfaces:s,directives:n,fields:r})}parseUnionTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("union");let i=this.parseName(),s=this.parseConstDirectives(),n=this.parseUnionMemberTypes();return this.node(e,{kind:a.h.UNION_TYPE_DEFINITION,description:t,name:i,directives:s,types:n})}parseUnionMemberTypes(){return this.expectOptionalToken(p.T.EQUALS)?this.delimitedMany(p.T.PIPE,this.parseNamedType):[]}parseEnumTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("enum");let i=this.parseName(),s=this.parseConstDirectives(),n=this.parseEnumValuesDefinition();return this.node(e,{kind:a.h.ENUM_TYPE_DEFINITION,description:t,name:i,directives:s,values:n})}parseEnumValuesDefinition(){return this.optionalMany(p.T.BRACE_L,this.parseEnumValueDefinition,p.T.BRACE_R)}parseEnumValueDefinition(){let e=this._lexer.token,t=this.parseDescription(),i=this.parseEnumValueName(),s=this.parseConstDirectives();return this.node(e,{kind:a.h.ENUM_VALUE_DEFINITION,description:t,name:i,directives:s})}parseEnumValueName(){if("true"===this._lexer.token.value||"false"===this._lexer.token.value||"null"===this._lexer.token.value)throw(0,s.h)(this._lexer.source,this._lexer.token.start,`${u(this._lexer.token)} is reserved and cannot be used for an enum value.`);return this.parseName()}parseInputObjectTypeDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("input");let i=this.parseName(),s=this.parseConstDirectives(),n=this.parseInputFieldsDefinition();return this.node(e,{kind:a.h.INPUT_OBJECT_TYPE_DEFINITION,description:t,name:i,directives:s,fields:n})}parseInputFieldsDefinition(){return this.optionalMany(p.T.BRACE_L,this.parseInputValueDef,p.T.BRACE_R)}parseTypeSystemExtension(){let e=this._lexer.lookahead();if(e.kind===p.T.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)}parseSchemaExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");let t=this.parseConstDirectives(),i=this.optionalMany(p.T.BRACE_L,this.parseOperationTypeDefinition,p.T.BRACE_R);if(0===t.length&&0===i.length)throw this.unexpected();return this.node(e,{kind:a.h.SCHEMA_EXTENSION,directives:t,operationTypes:i})}parseScalarTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");let t=this.parseName(),i=this.parseConstDirectives();if(0===i.length)throw this.unexpected();return this.node(e,{kind:a.h.SCALAR_TYPE_EXTENSION,name:t,directives:i})}parseObjectTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");let t=this.parseName(),i=this.parseImplementsInterfaces(),s=this.parseConstDirectives(),n=this.parseFieldsDefinition();if(0===i.length&&0===s.length&&0===n.length)throw this.unexpected();return this.node(e,{kind:a.h.OBJECT_TYPE_EXTENSION,name:t,interfaces:i,directives:s,fields:n})}parseInterfaceTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");let t=this.parseName(),i=this.parseImplementsInterfaces(),s=this.parseConstDirectives(),n=this.parseFieldsDefinition();if(0===i.length&&0===s.length&&0===n.length)throw this.unexpected();return this.node(e,{kind:a.h.INTERFACE_TYPE_EXTENSION,name:t,interfaces:i,directives:s,fields:n})}parseUnionTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");let t=this.parseName(),i=this.parseConstDirectives(),s=this.parseUnionMemberTypes();if(0===i.length&&0===s.length)throw this.unexpected();return this.node(e,{kind:a.h.UNION_TYPE_EXTENSION,name:t,directives:i,types:s})}parseEnumTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");let t=this.parseName(),i=this.parseConstDirectives(),s=this.parseEnumValuesDefinition();if(0===i.length&&0===s.length)throw this.unexpected();return this.node(e,{kind:a.h.ENUM_TYPE_EXTENSION,name:t,directives:i,values:s})}parseInputObjectTypeExtension(){let e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");let t=this.parseName(),i=this.parseConstDirectives(),s=this.parseInputFieldsDefinition();if(0===i.length&&0===s.length)throw this.unexpected();return this.node(e,{kind:a.h.INPUT_OBJECT_TYPE_EXTENSION,name:t,directives:i,fields:s})}parseDirectiveDefinition(){let e=this._lexer.token,t=this.parseDescription();this.expectKeyword("directive"),this.expectToken(p.T.AT);let i=this.parseName(),s=this.parseArgumentDefs(),n=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");let r=this.parseDirectiveLocations();return this.node(e,{kind:a.h.DIRECTIVE_DEFINITION,description:t,name:i,arguments:s,repeatable:n,locations:r})}parseDirectiveLocations(){return this.delimitedMany(p.T.PIPE,this.parseDirectiveLocation)}parseDirectiveLocation(){let e=this._lexer.token,t=this.parseName();if(Object.prototype.hasOwnProperty.call(r.B,t.value))return t;throw this.unexpected(e)}node(e,t){return!0!==this._options.noLocation&&(t.loc=new n.Ye(e,this._lexer.lastToken,this._lexer.source)),t}peek(e){return this._lexer.token.kind===e}expectToken(e){let t=this._lexer.token;if(t.kind===e)return this.advanceLexer(),t;throw(0,s.h)(this._lexer.source,t.start,`Expected ${d(e)}, found ${u(t)}.`)}expectOptionalToken(e){return this._lexer.token.kind===e&&(this.advanceLexer(),!0)}expectKeyword(e){let t=this._lexer.token;if(t.kind===p.T.NAME&&t.value===e)this.advanceLexer();else throw(0,s.h)(this._lexer.source,t.start,`Expected "${e}", found ${u(t)}.`)}expectOptionalKeyword(e){let t=this._lexer.token;return t.kind===p.T.NAME&&t.value===e&&(this.advanceLexer(),!0)}unexpected(e){let t=null!=e?e:this._lexer.token;return(0,s.h)(this._lexer.source,t.start,`Unexpected ${u(t)}.`)}any(e,t,i){this.expectToken(e);let s=[];for(;!this.expectOptionalToken(i);)s.push(t.call(this));return s}optionalMany(e,t,i){if(this.expectOptionalToken(e)){let e=[];do e.push(t.call(this));while(!this.expectOptionalToken(i));return e}return[]}many(e,t,i){this.expectToken(e);let s=[];do s.push(t.call(this));while(!this.expectOptionalToken(i));return s}delimitedMany(e,t){this.expectOptionalToken(e);let i=[];do i.push(t.call(this));while(this.expectOptionalToken(e));return i}advanceLexer(){let{maxTokens:e}=this._options,t=this._lexer.advance();if(t.kind!==p.T.EOF&&(++this._tokenCounter,void 0!==e&&this._tokenCounter>e))throw(0,s.h)(this._lexer.source,t.start,`Document contains more that ${e} tokens. Parsing aborted.`)}}function u(e){let t=e.value;return d(e.kind)+(null!=t?` "${t}"`:"")}function d(e){return(0,h.u)(e)?`"${e}"`:e}}}]);