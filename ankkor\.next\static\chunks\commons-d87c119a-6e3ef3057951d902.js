"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7044],{12381:function(e,t,o){o.d(t,{z:function(){return l}});var a=o(57437);o(2265);var r=o(37053),s=o(90535),i=o(93448);let n=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:o,size:s,asChild:l=!1,...d}=e,c=l?r.g7:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(n({variant:o,size:s,className:t})),...d})}},40279:function(e,t,o){o.d(t,{I:function(){return i}});var a=o(57437),r=o(2265),s=o(93448);let i=r.forwardRef((e,t)=>{let{className:o,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",o),ref:t,...i})});i.displayName="Input"},29658:function(e,t,o){var a=o(57437),r=o(29),s=o.n(r);o(2265),t.Z=e=>{let{size:t="md",color:o="#2c2c27",className:r=""}=e,i={sm:{container:"w-6 h-6",dot:"w-1 h-1"},md:{container:"w-10 h-10",dot:"w-1.5 h-1.5"},lg:{container:"w-16 h-16",dot:"w-2 h-2"}};return(0,a.jsxs)("div",{className:"jsx-cba83ab4e8da42d9 "+"flex items-center justify-center ".concat(r),children:[(0,a.jsxs)("div",{className:"jsx-cba83ab4e8da42d9 "+"relative ".concat(i[t].container),children:[(0,a.jsx)("div",{style:{backgroundColor:o,animation:"loaderDot1 1.5s infinite"},className:"jsx-cba83ab4e8da42d9 "+"absolute top-0 left-1/2 -translate-x-1/2 ".concat(i[t].dot," rounded-full")}),(0,a.jsx)("div",{style:{backgroundColor:o,animation:"loaderDot2 1.5s infinite"},className:"jsx-cba83ab4e8da42d9 "+"absolute top-1/2 right-0 -translate-y-1/2 ".concat(i[t].dot," rounded-full")}),(0,a.jsx)("div",{style:{backgroundColor:o,animation:"loaderDot3 1.5s infinite"},className:"jsx-cba83ab4e8da42d9 "+"absolute bottom-0 left-1/2 -translate-x-1/2 ".concat(i[t].dot," rounded-full")}),(0,a.jsx)("div",{style:{backgroundColor:o,animation:"loaderDot4 1.5s infinite"},className:"jsx-cba83ab4e8da42d9 "+"absolute top-1/2 left-0 -translate-y-1/2 ".concat(i[t].dot," rounded-full")}),(0,a.jsx)("div",{style:{border:"2px solid ".concat(o),borderTopColor:"transparent",animation:"loaderRotate 1s linear infinite"},className:"jsx-cba83ab4e8da42d9 absolute inset-0 rounded-full"})]}),(0,a.jsx)(s(),{id:"cba83ab4e8da42d9",children:"@-webkit-keyframes loaderRotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes loaderRotate{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes loaderRotate{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes loaderRotate{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-moz-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-o-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-webkit-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-moz-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-o-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-webkit-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-moz-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-o-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-webkit-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@-moz-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@-o-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}"})]})}},71917:function(e,t,o){o.d(t,{ToastProvider:function(){return u},p:function(){return m}});var a=o(57437),r=o(2265),s=o(43886),i=o(48131),n=o(65302),l=o(22252),d=o(33245),c=o(32489);let f=(0,r.createContext)(void 0);function u(e){let{children:t}=e,[o,s]=(0,r.useState)([]);return(0,a.jsxs)(f.Provider,{value:{toasts:o,addToast:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3,a=Math.random().toString(36).substring(2,9);s(r=>[...r,{id:a,message:e,type:t,duration:o}])},removeToast:e=>{s(t=>t.filter(t=>t.id!==e))}},children:[t,(0,a.jsx)(h,{})]})}function m(){let e=(0,r.useContext)(f);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function g(e){let{toast:t,onRemove:o}=e;return(0,r.useEffect)(()=>{if(t.duration){let e=setTimeout(()=>{o()},t.duration);return()=>clearTimeout(e)}},[t.duration,o]),(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:"flex items-center p-4 rounded-lg border shadow-lg ".concat((()=>{switch(t.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()," max-w-md"),children:[(0,a.jsx)(()=>{switch(t.type){case"success":return(0,a.jsx)(n.Z,{className:"h-5 w-5"});case"error":return(0,a.jsx)(l.Z,{className:"h-5 w-5"});default:return(0,a.jsx)(d.Z,{className:"h-5 w-5"})}},{}),(0,a.jsx)("span",{className:"ml-3 text-sm font-medium flex-1",children:t.message}),(0,a.jsx)("button",{onClick:o,className:"ml-4 text-gray-400 hover:text-gray-600",children:(0,a.jsx)(c.Z,{className:"h-4 w-4"})})]})}function h(){let{toasts:e,removeToast:t}=m();return(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:(0,a.jsx)(i.M,{children:e.map(e=>(0,a.jsx)(g,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},32898:function(e,t,o){o.d(t,{j:function(){return d}});var a=o(2265),r=o(3371),s=o(92371);let i=async()=>{try{let e=await fetch("/api/user/wishlist",{method:"GET",credentials:"include"});if(e.ok)return(await e.json()).wishlist||[]}catch(e){console.error("Error fetching user wishlist:",e)}return[]},n=async e=>{try{await fetch("/api/user/wishlist",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({wishlist:e})})}catch(e){console.error("Error saving user wishlist:",e)}},l=(e,t)=>{let o=[...e];return t.forEach(e=>{o.some(t=>t.id===e.id)||o.push(e)}),o};function d(){let{isAuthenticated:e,customer:t}=(0,r.O)(),o=(0,s.x)(),d=(0,s.Y)(),[c,f]=(0,a.useState)(!1),u=(0,a.useRef)(e),m=(0,a.useRef)((null==t?void 0:t.id)||null);return(0,a.useEffect)(()=>{if(c)return;let a=async()=>{f(!0),console.log("Auth state changed: User logged out - resetting cart and wishlist");try{if(await o.clearCart(),d.items.length>0)try{sessionStorage.setItem("ankkor_temp_wishlist",JSON.stringify(d.items)),console.log("Saved wishlist items to session storage for recovery")}catch(e){console.error("Failed to save wishlist to session storage:",e)}sessionStorage.removeItem("cartInitialized"),await o.initCart(),sessionStorage.setItem("cartInitialized","true")}catch(e){console.error("Error handling logout:",e)}finally{f(!1)}},r=async()=>{f(!0),console.log("Auth state changed: User logged in - syncing cart and wishlist");try{await s()}catch(e){console.error("Error handling login:",e)}finally{f(!1)}},s=async()=>{try{let e=d.items,t=await i();if(t&&t.length>0){let o=l(e,t);d.clearWishlist(),o.forEach(e=>d.addToWishlist(e)),console.log("Wishlist synced from user profile")}else e.length>0&&(await n(e),console.log("Local wishlist saved to user profile"))}catch(e){console.error("Error syncing wishlist on login:",e)}},g=async()=>{try{let e=d.items;e.length>0&&(await n(e),console.log("Wishlist saved before logout"))}catch(e){console.error("Error saving wishlist on logout:",e)}};u.current!==e?e?r():(async()=>{await g(),a()})():e&&(null==t?void 0:t.id)!==m.current&&r(),u.current=e,m.current=(null==t?void 0:t.id)||null},[e,null==t?void 0:t.id,o,d,c]),null}},3697:function(e,t,o){var a=o(2265),r=o(11658);t.Z=function(e,t){let{setLoading:o,setVariant:s}=(0,r.r)();(0,a.useEffect)(()=>{o(e),t&&s(t)},[e,t,o,s])}}}]);