"use strict";(()=>{var e={};e.id=9929,e.ids=[9929],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},72254:e=>{e.exports=require("node:buffer")},6005:e=>{e.exports=require("node:crypto")},47261:e=>{e.exports=require("node:util")},10283:(e,r,s)=>{s.r(r),s.d(r,{originalPathname:()=>w,patchFetch:()=>v,requestAsyncStorage:()=>y,routeModule:()=>h,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{GET:()=>p,PATCH:()=>l,POST:()=>d});var o=s(49303),n=s(88716),a=s(60670),i=s(87070);async function c(e,r){try{let s=await fetch("/api/qstash-test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({data:e,delay:r})});if(!s.ok)throw Error(`Failed to send QStash test message: ${s.status}`);let t=await s.json();return{success:!0,messageId:t.id||"unknown"}}catch(e){return console.error("Error sending QStash test message:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}var u=s(70926);async function g(){try{let e=await fetch("/api/woo-sync",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"sync-inventory",timestamp:new Date().toISOString()})});if(!e.ok)throw Error(`Failed to schedule inventory sync: ${e.status}`);return await e.json()}catch(e){throw console.error("Error scheduling inventory sync:",e),e}}async function p(e){try{let{searchParams:r}=new URL(e.url),s=r.get("delay"),t=s?parseInt(s,10):void 0,o={source:"trigger-test-api",userAgent:e.headers.get("user-agent"),referrer:e.headers.get("referer")},n=await c(o,t);return i.NextResponse.json({status:n.success?"success":"error",...n,message:n.success?`Test message sent successfully with ID ${n.messageId}`:`Failed to send test message: ${n.error}`})}catch(e){return console.error("Error triggering QStash test:",e),i.NextResponse.json({status:"error",message:"Error triggering QStash test",error:e.message},{status:500})}}async function d(e){try{let{delaySeconds:r,...s}=await e.json(),t=await c({...s,source:"trigger-test-api-post"},r);return i.NextResponse.json({status:t.success?"success":"error",...t,message:t.success?`Test message sent successfully with ID ${t.messageId}`:`Failed to send test message: ${t.error}`})}catch(e){return console.error("Error triggering QStash test via POST:",e),i.NextResponse.json({status:"error",message:"Error triggering QStash test",error:e.message},{status:500})}}async function l(){try{let e=await g();return i.NextResponse.json({success:!0,message:"Inventory sync scheduled successfully",details:e})}catch(e){return console.error("Error scheduling inventory sync:",e),i.NextResponse.json({success:!1,message:"Failed to schedule inventory sync",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}u.Zk,new u.KU({token:process.env.QSTASH_TOKEN||""});let h=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/trigger-test/route",pathname:"/api/trigger-test",filename:"route",bundlePath:"app/api/trigger-test/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\trigger-test\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:y,staticGenerationAsyncStorage:m,serverHooks:f}=h,w="/api/trigger-test/route";function v(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,5972,4766,926],()=>s(10283));module.exports=t})();