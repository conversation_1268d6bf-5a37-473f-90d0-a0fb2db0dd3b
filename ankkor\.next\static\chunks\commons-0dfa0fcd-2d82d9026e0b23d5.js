"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6459],{62280:function(t,e,r){r.d(e,{J:function(){return A}});var n=r(48771),a=r(13223),o=r(4946),i=r(37249);let l=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function s(t,e,r=1){(0,a.k)(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[n,u]=function(t){let e=l.exec(t);if(!e)return[,];let[,r,n]=e;return[r,n]}(t);if(!n)return;let f=window.getComputedStyle(e).getPropertyValue(n);if(f){let t=f.trim();return(0,o.P)(t)?parseFloat(t):t}return(0,i.tm)(u)?s(u,e,r+1):u}var u=r(44944),f=r(8834),c=r(38580),p=r(44563),d=r(74305),m=r(27492);let h=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),g=t=>h.has(t),x=t=>Object.keys(t).some(g),b=t=>t===d.Rx||t===m.px,y=(t,e)=>parseFloat(t.split(", ")[e]),v=(t,e)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let a=n.match(/^matrix3d\((.+)\)$/);if(a)return y(a[1],e);{let e=n.match(/^matrix\((.+)\)$/);return e?y(e[1],t):0}},w=new Set(["x","y","z"]),$=f._.filter(t=>!w.has(t)),k={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:v(4,13),y:v(5,14)};k.translateX=k.x,k.translateY=k.y;let R=(t,e,r)=>{let n=e.measureViewportBox(),a=getComputedStyle(e.current),{display:o}=a,i={};"none"===o&&e.setStaticValue("display",t.display||"block"),r.forEach(t=>{i[t]=k[t](n,a)}),e.render();let l=e.measureViewportBox();return r.forEach(r=>{let n=e.getValue(r);n&&n.jump(i[r]),t[r]=k[r](l,a)}),t},C=(t,e,r={},n={})=>{e={...e},n={...n};let o=Object.keys(e).filter(g),i=[],l=!1,s=[];if(o.forEach(o=>{let f;let p=t.getValue(o);if(!t.hasValue(o))return;let d=r[o],h=(0,c.C)(d),g=e[o];if((0,u.C)(g)){let t=g.length,e=null===g[0]?1:0;d=g[e],h=(0,c.C)(d);for(let r=e;r<t&&null!==g[r];r++)f?(0,a.k)((0,c.C)(g[r])===f,"All keyframes must be of the same type"):(f=(0,c.C)(g[r]),(0,a.k)(f===h||b(h)&&b(f),"Keyframes must be of the same dimension as the current value"))}else f=(0,c.C)(g);if(h!==f){if(b(h)&&b(f)){let t=p.get();"string"==typeof t&&p.set(parseFloat(t)),"string"==typeof g?e[o]=parseFloat(g):Array.isArray(g)&&f===m.px&&(e[o]=g.map(parseFloat))}else(null==h?void 0:h.transform)&&(null==f?void 0:f.transform)&&(0===d||0===g)?0===d?p.set(f.transform(d)):e[o]=h.transform(g):(l||(i=function(t){let e=[];return $.forEach(r=>{let n=t.getValue(r);void 0!==n&&(e.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),l=!0),s.push(o),n[o]=void 0!==n[o]?n[o]:e[o],p.jump(g))}}),!s.length)return{target:e,transitionEnd:n};{let r=s.indexOf("height")>=0?window.pageYOffset:null,a=R(e,t,s);return i.length&&i.forEach(([e,r])=>{t.getValue(e).set(r)}),t.render(),p.j&&null!==r&&window.scrollTo({top:r}),{target:a,transitionEnd:n}}},F=(t,e,r,n)=>{var a,o;let l=function(t,{...e},r){let n=t.current;if(!(n instanceof Element))return{target:e,transitionEnd:r};for(let a in r&&(r={...r}),t.values.forEach(t=>{let e=t.get();if(!(0,i.tm)(e))return;let r=s(e,n);r&&t.set(r)}),e){let t=e[a];if(!(0,i.tm)(t))continue;let o=s(t,n);o&&(e[a]=o,r||(r={}),void 0===r[a]&&(r[a]=t))}return{target:e,transitionEnd:r}}(t,e,n);return e=l.target,n=l.transitionEnd,a=e,o=n,x(a)?C(t,a,r,o):{target:a,transitionEnd:o}};var W=r(60282);class A extends W.l{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...r},{transformValues:a},o){let i=(0,n.P$)(r,t||{},this);if(a&&(e&&(e=a(e)),r&&(r=a(r)),i&&(i=a(i))),o){(0,n.GJ)(this,r,i);let t=F(this,r,i,e);e=t.transitionEnd,r=t.target}return{transition:t,transitionEnd:e,...r}}}},43886:function(t,e,r){r.d(e,{E:function(){return Z}});var n,a=r(51765),o=r(91337);function i(t){if("string"!=typeof t||t.includes("-"));else if(o.B.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}var l=r(2265),s=r(68780),u=r(35998);let f=t=>!(0,u.Z)(t);try{(n=require("@emotion/is-prop-valid").default)&&(f=t=>t.startsWith("on")?!(0,u.Z)(t):n(t))}catch(t){}var c=r(86540),p=r(23999),d=r(16408),m=r(74935),h=r(67971),g=r(44437);let x={useVisualState:(0,m.t)({scrapeMotionValuesFromProps:h.U,createRenderState:g.a})};var b=r(47021),y=r(37997),v=r(22279),w=r(4469),$=r(37249),k=r(8834),R=r(1125),C=r(22005),F=r(50813),W=r(62280);class A extends W.J{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(k.G.has(e)){let t=(0,C.A)(e);return t&&t.default||0}{let r=window.getComputedStyle(t),n=((0,$.f9)(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,F.J)(t,e)}build(t,e,r,n){(0,w.r)(t,e,r,n.transformTemplate)}scrapeMotionValuesFromProps(t,e){return(0,h.U)(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,p.i)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,r,n){(0,R.N)(t,e,r,n)}}var S=r(23482);let V=(t,e)=>i(t)?new S.e(e,{enableHardwareAcceleration:!1}):new A(e,{enableHardwareAcceleration:!0});var P=r(82760);let T={...y.s,...b.E,...v.o,...P.b},Z=function(t){function e(e,r={}){return(0,a.F)(t(e,r))}if("undefined"==typeof Proxy)return e;let r=new Map;return new Proxy(e,{get:(t,n)=>(r.has(n)||r.set(n,e(n)),r.get(n))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},r,n){return{...i(t)?d.V:x,preloadedFeatures:r,useRender:function(t=!1){return(e,r,n,{latestValues:a},o)=>{let d=(i(e)?c.R:s.I)(r,a,o,e),m={...function(t,e,r){let n={};for(let a in t)("values"!==a||"object"!=typeof t.values)&&(f(a)||!0===r&&(0,u.Z)(a)||!e&&!(0,u.Z)(a)||t.draggable&&a.startsWith("onDrag"))&&(n[a]=t[a]);return n}(r,"string"==typeof e,t),...d,ref:n},{children:h}=r,g=(0,l.useMemo)(()=>(0,p.i)(h)?h.get():h,[h]);return(0,l.createElement)(e,{...m,children:g})}}(e),createVisualElement:n,Component:t}})(t,e,T,V))},17444:function(t,e,r){r.d(e,{D:function(){return n}});let n=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()},37249:function(t,e,r){r.d(e,{Xp:function(){return i},f9:function(){return a},tm:function(){return o}});let n=t=>e=>"string"==typeof e&&e.startsWith(t),a=n("--"),o=n("var(--"),i=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g},94239:function(t,e,r){r.d(e,{v:function(){return n}});function n(t){return t instanceof SVGElement&&"svg"!==t.tagName}},25861:function(t,e,r){r.d(e,{T:function(){return i}});var n=r(15636),a=r(22779),o=r(22005);function i(t,e){let r=(0,o.A)(t);return r!==a.h&&(r=n.P),r.getAnimatableNone?r.getAnimatableNone(e):void 0}},22005:function(t,e,r){r.d(e,{A:function(){return i}});var n=r(33964),a=r(22779);let o={...r(38325).j,color:n.$,backgroundColor:n.$,outlineColor:n.$,fill:n.$,stroke:n.$,borderColor:n.$,borderTopColor:n.$,borderRightColor:n.$,borderBottomColor:n.$,borderLeftColor:n.$,filter:a.h,WebkitFilter:a.h},i=t=>o[t]},38580:function(t,e,r){r.d(e,{$:function(){return i},C:function(){return l}});var n=r(74305),a=r(27492),o=r(55113);let i=[n.Rx,a.px,a.aQ,a.RW,a.vw,a.vh,{test:t=>"auto"===t,parse:t=>t}],l=t=>i.find((0,o.l)(t))},37622:function(t,e,r){r.d(e,{c:function(){return s}});var n=r(33964),a=r(15636),o=r(38580),i=r(55113);let l=[...o.$,n.$,a.P],s=t=>l.find((0,i.l)(t))},38325:function(t,e,r){r.d(e,{j:function(){return i}});var n=r(74305),a=r(27492);let o={...n.Rx,transform:Math.round},i={borderWidth:a.px,borderTopWidth:a.px,borderRightWidth:a.px,borderBottomWidth:a.px,borderLeftWidth:a.px,borderRadius:a.px,radius:a.px,borderTopLeftRadius:a.px,borderTopRightRadius:a.px,borderBottomRightRadius:a.px,borderBottomLeftRadius:a.px,width:a.px,maxWidth:a.px,height:a.px,maxHeight:a.px,size:a.px,top:a.px,right:a.px,bottom:a.px,left:a.px,padding:a.px,paddingTop:a.px,paddingRight:a.px,paddingBottom:a.px,paddingLeft:a.px,margin:a.px,marginTop:a.px,marginRight:a.px,marginBottom:a.px,marginLeft:a.px,rotate:a.RW,rotateX:a.RW,rotateY:a.RW,rotateZ:a.RW,scale:n.bA,scaleX:n.bA,scaleY:n.bA,scaleZ:n.bA,skew:a.RW,skewX:a.RW,skewY:a.RW,distance:a.px,translateX:a.px,translateY:a.px,translateZ:a.px,x:a.px,y:a.px,z:a.px,perspective:a.px,transformPerspective:a.px,opacity:n.Fq,originX:a.$C,originY:a.$C,originZ:a.px,zIndex:o,fillOpacity:n.Fq,strokeOpacity:n.Fq,numOctaves:o}},55113:function(t,e,r){r.d(e,{l:function(){return n}});let n=t=>e=>e.test(t)},68780:function(t,e,r){r.d(e,{I:function(){return u},p:function(){return s}});var n=r(2265),a=r(77556),o=r(23999),i=r(4469),l=r(44437);function s(t,e,r){for(let n in e)(0,o.i)(e[n])||(0,a.j)(n,r)||(t[n]=e[n])}function u(t,e,r){let a={},o=function(t,e,r){let a=t.style||{},o={};return s(o,a,t),Object.assign(o,function({transformTemplate:t},e,r){return(0,n.useMemo)(()=>{let n=(0,l.a)();return(0,i.r)(n,e,{enableHardwareAcceleration:!r},t),Object.assign({},n.vars,n.style)},[e])}(t,e,r)),t.transformValues?t.transformValues(o):o}(t,e,r);return t.drag&&!1!==t.dragListener&&(a.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(a.tabIndex=0),a.style=o,a}},4469:function(t,e,r){r.d(e,{r:function(){return u}});var n=r(8834);let a={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},o=n._.length;var i=r(37249);let l=(t,e)=>e&&"number"==typeof t?e.transform(t):t;var s=r(38325);function u(t,e,r,u){let{style:f,vars:c,transform:p,transformOrigin:d}=t,m=!1,h=!1,g=!0;for(let t in e){let r=e[t];if((0,i.f9)(t)){c[t]=r;continue}let a=s.j[t],o=l(r,a);if(n.G.has(t)){if(m=!0,p[t]=o,!g)continue;r!==(a.default||0)&&(g=!1)}else t.startsWith("origin")?(h=!0,d[t]=o):f[t]=o}if(!e.transform&&(m||u?f.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:r=!0},i,l){let s="";for(let e=0;e<o;e++){let r=n._[e];if(void 0!==t[r]){let e=a[r]||r;s+=`${e}(${t[r]}) `}}return e&&!t.z&&(s+="translateZ(0)"),s=s.trim(),l?s=l(t,i?"":s):r&&i&&(s="none"),s}(t.transform,r,g,u):f.transform&&(f.transform="none")),h){let{originX:t="50%",originY:e="50%",originZ:r=0}=d;f.transformOrigin=`${t} ${e} ${r}`}}},44437:function(t,e,r){r.d(e,{a:function(){return n}});let n=()=>({style:{},transform:{},transformOrigin:{},vars:{}})},1125:function(t,e,r){r.d(e,{N:function(){return n}});function n(t,{style:e,vars:r},n,a){for(let o in Object.assign(t.style,e,a&&a.getProjectionStyles(n)),r)t.style.setProperty(o,r[o])}},67971:function(t,e,r){r.d(e,{U:function(){return o}});var n=r(77556),a=r(23999);function o(t,e){let{style:r}=t,o={};for(let i in r)((0,a.i)(r[i])||e.style&&(0,a.i)(e.style[i])||(0,n.j)(i,t))&&(o[i]=r[i]);return o}},8834:function(t,e,r){r.d(e,{G:function(){return a},_:function(){return n}});let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],a=new Set(n)},37003:function(t,e,r){r.d(e,{R:function(){return n}});let n=new WeakMap}}]);