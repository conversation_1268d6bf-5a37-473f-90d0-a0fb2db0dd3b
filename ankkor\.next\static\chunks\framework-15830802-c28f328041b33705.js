"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7231],{99601:function(e,t,l){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let r=l(44848),n=l(33456),a=l(81935),o=l(63237),u=l(95967),c=l(44510),i=l(27420),f=l(12846),d=l(77831),s=l(28077),h=l(50232);function p(e,t){let{origin:l}=t,p={},g=e.canonicalUrl,y=e.tree;p.preserveCustomHistoryState=!1;let R=(0,f.createEmptyCacheNode)(),_=(0,s.hasInterceptionRouteInCurrentTree)(e.tree);return R.lazyData=(0,r.fetchServerResponse)(new URL(g,l),[y[0],y[1],y[2],"refetch"],_?e.nextUrl:null,e.buildId),R.lazyData.then(async l=>{let[r,f]=l;if("string"==typeof r)return(0,u.handleExternalUrl)(e,p,r,e.pushRef.pendingPush);for(let l of(R.lazyData=null,r)){if(3!==l.length)return console.log("REFRESH FAILED"),e;let[r]=l,c=(0,a.applyRouterStatePatchToTree)([""],y,r,e.canonicalUrl);if(null===c)return(0,d.handleSegmentMismatch)(e,t,r);if((0,o.isNavigatingToNewRootLayout)(y,c))return(0,u.handleExternalUrl)(e,p,g,e.pushRef.pendingPush);let s=f?(0,n.createHrefFromUrl)(f):void 0;f&&(p.canonicalUrl=s);let[v,P]=l.slice(-2);if(null!==v){let e=v[2];R.rsc=e,R.prefetchRsc=null,(0,i.fillLazyItemsTillLeafWithHead)(R,void 0,r,v,P),p.prefetchCache=new Map}await (0,h.refreshInactiveParallelSegments)({state:e,updatedTree:c,updatedCache:R,includeNextUrl:_,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=R,p.patchedTree=c,p.canonicalUrl=g,y=c}return(0,c.handleMutable)(e,p)},()=>e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77784:function(e,t,l){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let r=l(33456),n=l(5410);function a(e,t){var l;let{url:a,tree:o}=t,u=(0,r.createHrefFromUrl)(a),c=o||e.tree,i=e.cache;return{buildId:e.buildId,canonicalUrl:u,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:i,prefetchCache:e.prefetchCache,tree:c,nextUrl:null!=(l=(0,n.extractPathFromFlightRouterState)(c))?l:a.pathname}}l(56118),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13722:function(e,t,l){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return v}});let r=l(83079),n=l(6866),a=l(1634),o=l(33456),u=l(95967),c=l(81935),i=l(63237),f=l(44510),d=l(27420),s=l(12846),h=l(28077),p=l(77831),g=l(50232),{createFromFetch:y,encodeReply:R}=l(6671);async function _(e,t,l){let o,{actionId:u,actionArgs:c}=l,i=await R(c),f=await fetch("",{method:"POST",headers:{Accept:n.RSC_CONTENT_TYPE_HEADER,[n.ACTION]:u,[n.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[n.NEXT_URL]:t}:{}},body:i}),d=f.headers.get("x-action-redirect");try{let e=JSON.parse(f.headers.get("x-action-revalidated")||"[[],0,0]");o={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){o={paths:[],tag:!1,cookie:!1}}let s=d?new URL((0,a.addBasePath)(d),new URL(e.canonicalUrl,window.location.href)):void 0;if(f.headers.get("content-type")===n.RSC_CONTENT_TYPE_HEADER){let e=await y(Promise.resolve(f),{callServer:r.callServer});if(d){let[,t]=null!=e?e:[];return{actionFlightData:t,redirectLocation:s,revalidatedParts:o}}let[t,[,l]]=null!=e?e:[];return{actionResult:t,actionFlightData:l,redirectLocation:s,revalidatedParts:o}}return{redirectLocation:s,revalidatedParts:o}}function v(e,t){let{resolve:l,reject:r}=t,n={},a=e.canonicalUrl,y=e.tree;n.preserveCustomHistoryState=!1;let R=e.nextUrl&&(0,h.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return n.inFlightServerAction=_(e,R,t),n.inFlightServerAction.then(async r=>{let{actionResult:h,actionFlightData:_,redirectLocation:v}=r;if(v&&(e.pushRef.pendingPush=!0,n.pendingPush=!0),!_)return(l(h),v)?(0,u.handleExternalUrl)(e,n,v.href,e.pushRef.pendingPush):e;if("string"==typeof _)return(0,u.handleExternalUrl)(e,n,_,e.pushRef.pendingPush);if(n.inFlightServerAction=null,v){let e=(0,o.createHrefFromUrl)(v,!1);n.canonicalUrl=e}for(let l of _){if(3!==l.length)return console.log("SERVER ACTION APPLY FAILED"),e;let[r]=l,f=(0,c.applyRouterStatePatchToTree)([""],y,r,v?(0,o.createHrefFromUrl)(v):e.canonicalUrl);if(null===f)return(0,p.handleSegmentMismatch)(e,t,r);if((0,i.isNavigatingToNewRootLayout)(y,f))return(0,u.handleExternalUrl)(e,n,a,e.pushRef.pendingPush);let[h,_]=l.slice(-2),P=null!==h?h[2]:null;if(null!==P){let t=(0,s.createEmptyCacheNode)();t.rsc=P,t.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(t,void 0,r,h,_),await (0,g.refreshInactiveParallelSegments)({state:e,updatedTree:f,updatedCache:t,includeNextUrl:!!R,canonicalUrl:n.canonicalUrl||e.canonicalUrl}),n.cache=t,n.prefetchCache=new Map}n.patchedTree=f,y=f}return l(h),(0,f.handleMutable)(e,n)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68448:function(e,t,l){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return d}});let r=l(33456),n=l(81935),a=l(63237),o=l(95967),u=l(22356),c=l(44510),i=l(12846),f=l(77831);function d(e,t){let{serverResponse:l}=t,[d,s]=l,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof d)return(0,o.handleExternalUrl)(e,h,d,e.pushRef.pendingPush);let p=e.tree,g=e.cache;for(let l of d){let c=l.slice(0,-4),[d]=l.slice(-3,-2),y=(0,n.applyRouterStatePatchToTree)(["",...c],p,d,e.canonicalUrl);if(null===y)return(0,f.handleSegmentMismatch)(e,t,d);if((0,a.isNavigatingToNewRootLayout)(p,y))return(0,o.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let R=s?(0,r.createHrefFromUrl)(s):void 0;R&&(h.canonicalUrl=R);let _=(0,i.createEmptyCacheNode)();(0,u.applyFlightData)(g,_,l),h.patchedTree=y,h.cache=_,g=_,p=y}return(0,c.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50232:function(e,t,l){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var l in t)Object.defineProperty(e,l,{enumerable:!0,get:t[l]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,l){let[r,n,,o]=t;for(let u in r.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=l,t[3]="refresh"),n)e(n[u],l)}},refreshInactiveParallelSegments:function(){return o}});let r=l(22356),n=l(44848),a=l(84541);async function o(e){let t=new Set;await u({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function u(e){let{state:t,updatedTree:l,updatedCache:a,includeNextUrl:o,fetchedSegments:c,rootTree:i=l,canonicalUrl:f}=e,[,d,s,h]=l,p=[];if(s&&s!==f&&"refresh"===h&&!c.has(s)){c.add(s);let e=(0,n.fetchServerResponse)(new URL(s,location.origin),[i[0],i[1],i[2],"refetch"],o?t.nextUrl:null,t.buildId).then(e=>{let t=e[0];if("string"!=typeof t)for(let e of t)(0,r.applyFlightData)(a,a,e)});p.push(e)}for(let e in d){let l=u({state:t,updatedTree:d[e],updatedCache:a,includeNextUrl:o,fetchedSegments:c,rootTree:i,canonicalUrl:f});p.push(l)}await Promise.all(p)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);