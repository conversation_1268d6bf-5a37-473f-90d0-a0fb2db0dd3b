"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3379],{94630:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},92369:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},69792:function(e,t,n){n.d(t,{VY:function(){return et},aV:function(){return X},fC:function(){return Q},xz:function(){return ee}});var r=n(2265),o=n.t(r,2);function a(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var u=n(57437);function i(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let a=r.createContext(o),i=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,c=n?.[e]?.[i]||a,s=r.useMemo(()=>l,Object.values(l));return(0,u.jsx)(c.Provider,{value:s,children:o})};return l.displayName=t+"Provider",[l,function(n,u){let l=u?.[e]?.[i]||a,c=r.useContext(l);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}var l=n(98575),c=n(37053),s=globalThis?.document?r.useLayoutEffect:()=>{},f=o["useId".toString()]||(()=>void 0),d=0;function m(e){let[t,n]=r.useState(f());return s(()=>{e||n(e=>e??String(d++))},[e]),e||(t?`radix-${t}`:"")}var v=n(66840);function p(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}function y({prop:e,defaultProp:t,onChange:n=()=>{}}){let[o,a]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[o]=n,a=r.useRef(o),u=p(t);return r.useEffect(()=>{a.current!==o&&(u(o),a.current=o)},[o,a,u]),n}({defaultProp:t,onChange:n}),u=void 0!==e,i=u?e:o,l=p(n);return[i,r.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l(n)}else a(t)},[u,e,a,l])]}var w=r.createContext(void 0);function b(e){let t=r.useContext(w);return e||t||"ltr"}var g="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},N="RovingFocusGroup",[R,x,M]=function(e){let t=e+"CollectionProvider",[n,o]=i(t),[a,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,u.jsx)(a,{scope:t,itemMap:i,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),a=(0,l.e)(t,o.collectionRef);return(0,u.jsx)(c.g7,{ref:a,children:r})});m.displayName=d;let v=e+"CollectionItemSlot",p="data-radix-collection-item",y=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,i=r.useRef(null),f=(0,l.e)(t,i),d=s(v,n);return r.useEffect(()=>(d.itemMap.set(i,{ref:i,...a}),()=>void d.itemMap.delete(i))),(0,u.jsx)(c.g7,{[p]:"",ref:f,children:o})});return y.displayName=v,[{Provider:f,Slot:m,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(p,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}(N),[C,I]=i(N,[M]),[T,E]=C(N),A=r.forwardRef((e,t)=>(0,u.jsx)(R.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(R.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(S,{...e,ref:t})})}));A.displayName=N;var S=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:o,loop:i=!1,dir:c,currentTabStopId:s,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:d,onEntryFocus:m,preventScrollOnEntryFocus:w=!1,...N}=e,R=r.useRef(null),M=(0,l.e)(t,R),C=b(c),[I=null,E]=y({prop:s,defaultProp:f,onChange:d}),[A,S]=r.useState(!1),j=p(m),D=x(n),F=r.useRef(!1),[k,_]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(g,j),()=>e.removeEventListener(g,j)},[j]),(0,u.jsx)(T,{scope:n,orientation:o,dir:C,loop:i,currentTabStopId:I,onItemFocus:r.useCallback(e=>E(e),[E]),onItemShiftTab:r.useCallback(()=>S(!0),[]),onFocusableItemAdd:r.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>_(e=>e-1),[]),children:(0,u.jsx)(v.WV.div,{tabIndex:A||0===k?-1:0,"data-orientation":o,...N,ref:M,style:{outline:"none",...e.style},onMouseDown:a(e.onMouseDown,()=>{F.current=!0}),onFocus:a(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(g,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);O([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),w)}}F.current=!1}),onBlur:a(e.onBlur,()=>S(!1))})})}),j="RovingFocusGroupItem",D=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:o=!0,active:i=!1,tabStopId:l,...c}=e,s=m(),f=l||s,d=E(j,n),p=d.currentTabStopId===f,y=x(n),{onFocusableItemAdd:w,onFocusableItemRemove:b}=d;return r.useEffect(()=>{if(o)return w(),()=>b()},[o,w,b]),(0,u.jsx)(R.ItemSlot,{scope:n,id:f,focusable:o,active:i,children:(0,u.jsx)(v.WV.span,{tabIndex:p?0:-1,"data-orientation":d.orientation,...c,ref:t,onMouseDown:a(e.onMouseDown,e=>{o?d.onItemFocus(f):e.preventDefault()}),onFocus:a(e.onFocus,()=>d.onItemFocus(f)),onKeyDown:a(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){d.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return F[o]}(e,d.orientation,d.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=d.loop?(n=o,r=a+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(a+1)}setTimeout(()=>O(o))}})})})});D.displayName=j;var F={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var k=e=>{var t,n;let o,a;let{present:u,children:i}=e,c=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef({}),i=r.useRef(e),l=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=_(u.current);l.current="mounted"===c?e:"none"},[c]),s(()=>{let t=u.current,n=i.current;if(n!==e){let r=l.current,o=_(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),i.current=e}},[e,f]),s(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=_(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!i.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(l.current=_(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),a(e)},[])}}(u),f="function"==typeof i?i({present:c.isPresent}):r.Children.only(i),d=(0,l.e)(c.ref,(o=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning?f.ref:(o=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in o&&o.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof i||c.isPresent?r.cloneElement(f,{ref:d}):null};function _(e){return(null==e?void 0:e.animationName)||"none"}k.displayName="Presence";var P="Tabs",[L,U]=i(P,[I]),V=I(),[K,W]=L(P),$=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:c="automatic",...s}=e,f=b(l),[d,p]=y({prop:r,onChange:o,defaultProp:a});return(0,u.jsx)(K,{scope:n,baseId:m(),value:d,onValueChange:p,orientation:i,dir:f,activationMode:c,children:(0,u.jsx)(v.WV.div,{dir:f,"data-orientation":i,...s,ref:t})})});$.displayName=P;var G="TabsList",Z=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=W(G,n),i=V(n);return(0,u.jsx)(A,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:(0,u.jsx)(v.WV.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});Z.displayName=G;var q="TabsTrigger",z=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...i}=e,l=W(q,n),c=V(n),s=Y(l.baseId,r),f=J(l.baseId,r),d=r===l.value;return(0,u.jsx)(D,{asChild:!0,...c,focusable:!o,active:d,children:(0,u.jsx)(v.WV.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":f,"data-state":d?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:s,...i,ref:t,onMouseDown:a(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:a(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:a(e.onFocus,()=>{let e="manual"!==l.activationMode;d||o||!e||l.onValueChange(r)})})})});z.displayName=q;var B="TabsContent",H=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...l}=e,c=W(B,n),s=Y(c.baseId,o),f=J(c.baseId,o),d=o===c.value,m=r.useRef(d);return r.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(k,{present:a||d,children:n=>{let{present:r}=n;return(0,u.jsx)(v.WV.div,{"data-state":d?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":s,hidden:!r,id:f,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&i})}})});function Y(e,t){return"".concat(e,"-trigger-").concat(t)}function J(e,t){return"".concat(e,"-content-").concat(t)}H.displayName=B;var Q=$,X=Z,ee=z,et=H}}]);