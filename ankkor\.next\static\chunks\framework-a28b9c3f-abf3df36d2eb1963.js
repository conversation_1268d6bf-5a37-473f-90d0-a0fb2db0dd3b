"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6076],{45104:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{APP_BUILD_MANIFEST:function(){return h},APP_CLIENT_INTERNALS:function(){return q},APP_PATHS_MANIFEST:function(){return E},APP_PATH_ROUTES_MANIFEST:function(){return m},AUTOMATIC_FONT_OPTIMIZATION_MANIFEST:function(){return L},BARREL_OPTIMIZATION_PREFIX:function(){return W},BLOCKED_PAGES:function(){return D},BUILD_ID_FILE:function(){return w},BUILD_MANIFEST:function(){return S},CLIENT_PUBLIC_FILES_PATH:function(){return U},CLIENT_REFERENCE_MANIFEST:function(){return H},CLIENT_STATIC_FILES_PATH:function(){return x},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return Z},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return $},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return X},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return Q},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return ee},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return K},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return J},COMPILER_INDEXES:function(){return o},COMPILER_NAMES:function(){return i},CONFIG_FILES:function(){return j},DEFAULT_RUNTIME_WEBPACK:function(){return et},DEFAULT_SANS_SERIF_FONT:function(){return es},DEFAULT_SERIF_FONT:function(){return ea},DEV_CLIENT_PAGES_MANIFEST:function(){return y},DEV_MIDDLEWARE_MANIFEST:function(){return C},EDGE_RUNTIME_WEBPACK:function(){return en},EDGE_UNSUPPORTED_NODE_APIS:function(){return e_},EXPORT_DETAIL:function(){return A},EXPORT_MARKER:function(){return N},FUNCTIONS_CONFIG_MANIFEST:function(){return T},GOOGLE_FONT_PROVIDER:function(){return eo},IMAGES_MANIFEST:function(){return b},INTERCEPTION_ROUTE_REWRITE_MANIFEST:function(){return Y},MIDDLEWARE_BUILD_MANIFEST:function(){return G},MIDDLEWARE_MANIFEST:function(){return M},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return V},MODERN_BROWSERSLIST_TARGET:function(){return r.default},NEXT_BUILTIN_DOCUMENT:function(){return B},NEXT_FONT_MANIFEST:function(){return g},OPTIMIZED_FONT_PROVIDERS:function(){return eu},PAGES_MANIFEST:function(){return p},PHASE_DEVELOPMENT_SERVER:function(){return l},PHASE_EXPORT:function(){return s},PHASE_INFO:function(){return _},PHASE_PRODUCTION_BUILD:function(){return f},PHASE_PRODUCTION_SERVER:function(){return c},PHASE_TEST:function(){return d},PRERENDER_MANIFEST:function(){return R},REACT_LOADABLE_MANIFEST:function(){return v},ROUTES_MANIFEST:function(){return O},RSC_MODULE_TYPES:function(){return ed},SERVER_DIRECTORY:function(){return F},SERVER_FILES_MANIFEST:function(){return P},SERVER_PROPS_ID:function(){return ei},SERVER_REFERENCE_MANIFEST:function(){return z},STATIC_PROPS_ID:function(){return er},STATIC_STATUS_PAGES:function(){return ef},STRING_LITERAL_DROP_BUNDLE:function(){return k},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return I},SYSTEM_ENTRYPOINTS:function(){return ep},TRACE_OUTPUT_VERSION:function(){return ec},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return el},UNDERSCORE_NOT_FOUND_ROUTE:function(){return u},UNDERSCORE_NOT_FOUND_ROUTE_ENTRY:function(){return a}});let r=n(38754)._(n(60979)),i={client:"client",server:"server",edgeServer:"edge-server"},o={[i.client]:0,[i.server]:1,[i.edgeServer]:2},u="/_not-found",a=""+u+"/page",s="phase-export",f="phase-production-build",c="phase-production-server",l="phase-development-server",d="phase-test",_="phase-info",p="pages-manifest.json",E="app-paths-manifest.json",m="app-path-routes-manifest.json",S="build-manifest.json",h="app-build-manifest.json",T="functions-config-manifest.json",I="subresource-integrity-manifest",g="next-font-manifest",N="export-marker.json",A="export-detail.json",R="prerender-manifest.json",O="routes-manifest.json",b="images-manifest.json",P="required-server-files.json",y="_devPagesManifest.json",M="middleware-manifest.json",C="_devMiddlewareManifest.json",v="react-loadable-manifest.json",L="font-manifest.json",F="server",j=["next.config.js","next.config.mjs"],w="BUILD_ID",D=["/_document","/_app","/_error"],U="public",x="static",k="__NEXT_DROP_CLIENT_FILE__",B="__NEXT_BUILTIN_DOCUMENT__",W="__barrel_optimize__",H="client-reference-manifest",z="server-reference-manifest",G="middleware-build-manifest",V="middleware-react-loadable-manifest",Y="interception-route-rewrite-manifest",$="main",X=""+$+"-app",q="app-pages-internals",K="react-refresh",Z="amp",J="webpack",Q="polyfills",ee=Symbol(Q),et="webpack-runtime",en="edge-runtime-webpack",er="__N_SSG",ei="__N_SSP",eo="https://fonts.googleapis.com/",eu=[{url:eo,preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],ea={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},es={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},ef=["/500"],ec=1,el=6e3,ed={client:"client",server:"server"},e_=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],ep=new Set([$,K,Z,X]);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34592:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function i(e){return n.test(e)?e.replace(r,"\\$&"):e}},20077:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HeadManagerContext",{enumerable:!0,get:function(){return r}});let r=n(38754)._(n(67294)).default.createContext({})},50494:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return E},defaultHead:function(){return l}});let r=n(38754),i=n(61757),o=n(85893),u=i._(n(67294)),a=r._(n(3657)),s=n(75010),f=n(20077),c=n(98579);function l(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===u.default.Fragment?e.concat(u.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(79784);let _=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:n}=t;return e.reduce(d,[]).reverse().concat(l(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,r={};return i=>{let o=!0,u=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){u=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=_.length;e<t;e++){let t=_[e];if(i.props.hasOwnProperty(t)){if("charSet"===t)n.has(t)?o=!1:n.add(t);else{let e=i.props[t],n=r[t]||new Set;("name"!==t||!u)&&n.has(e)?o=!1:(n.add(e),r[t]=n)}}}}return o}}()).reverse().map((e,t)=>{let r=e.key||t;if(!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,u.default.cloneElement(e,t)}return u.default.cloneElement(e,{key:r})})}let E=function(e){let{children:t}=e,n=(0,u.useContext)(s.AmpStateContext),r=(0,u.useContext)(f.HeadManagerContext);return(0,o.jsx)(a.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,c.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90042:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function i(e){return n.test(e)?e.replace(r,"\\$&"):e}},55346:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),n(31765);let r=n(96496),i=n(90128);function o(e){return void 0!==e.default}function u(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var n,a;let s,f,c,{src:l,sizes:d,unoptimized:_=!1,priority:p=!1,loading:E,className:m,quality:S,width:h,height:T,fill:I=!1,style:g,overrideSrc:N,onLoad:A,onLoadingComplete:R,placeholder:O="empty",blurDataURL:b,fetchPriority:P,decoding:y="async",layout:M,objectFit:C,objectPosition:v,lazyBoundary:L,lazyRoot:F,...j}=e,{imgConf:w,showAltText:D,blurComplete:U,defaultLoader:x}=t,k=w||i.imageConfigDefault;if("allSizes"in k)s=k;else{let e=[...k.deviceSizes,...k.imageSizes].sort((e,t)=>e-t),t=k.deviceSizes.sort((e,t)=>e-t),r=null==(n=k.qualities)?void 0:n.sort((e,t)=>e-t);s={...k,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===x)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let B=j.loader||x;delete j.loader,delete j.srcSet;let W="__next_img_default"in B;if(W){if("custom"===s.loader)throw Error('Image with src "'+l+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=B;B=t=>{let{config:n,...r}=t;return e(r)}}if(M){"fill"===M&&(I=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(g={...g,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!d&&(d=t)}let H="",z=u(h),G=u(T);if("object"==typeof(a=l)&&(o(a)||void 0!==a.src)){let e=o(l)?l.default:l;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(f=e.blurWidth,c=e.blurHeight,b=b||e.blurDataURL,H=e.src,!I){if(z||G){if(z&&!G){let t=z/e.width;G=Math.round(e.height*t)}else if(!z&&G){let t=G/e.height;z=Math.round(e.width*t)}}else z=e.width,G=e.height}}let V=!p&&("lazy"===E||void 0===E);(!(l="string"==typeof l?l:H)||l.startsWith("data:")||l.startsWith("blob:"))&&(_=!0,V=!1),s.unoptimized&&(_=!0),W&&l.endsWith(".svg")&&!s.dangerouslyAllowSVG&&(_=!0),p&&(P="high");let Y=u(S),$=Object.assign(I?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:C,objectPosition:v}:{},D?{}:{color:"transparent"},g),X=U||"empty"===O?null:"blur"===O?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:z,heightInt:G,blurWidth:f,blurHeight:c,blurDataURL:b||"",objectFit:$.objectFit})+'")':'url("'+O+'")',q=X?{backgroundSize:$.objectFit||"cover",backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},K=function(e){let{config:t,src:n,unoptimized:r,width:i,quality:o,sizes:u,loader:a}=e;if(r)return{src:n,srcSet:void 0,sizes:void 0};let{widths:s,kind:f}=function(e,t,n){let{deviceSizes:r,allSizes:i}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(n);r)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,u),c=s.length-1;return{sizes:u||"w"!==f?u:"100vw",srcSet:s.map((e,r)=>a({config:t,src:n,quality:o,width:e})+" "+("w"===f?e:r+1)+f).join(", "),src:a({config:t,src:n,quality:o,width:s[c]})}}({config:s,src:l,unoptimized:_,width:z,quality:Y,sizes:d,loader:B});return{props:{...j,loading:V?"lazy":E,fetchPriority:P,width:z,height:G,decoding:y,className:m,style:{...$,...q},sizes:K.sizes,srcSet:K.srcSet,src:N||K.src},meta:{unoptimized:_,priority:p,placeholder:O,fill:I}}}},37207:function(e,t){function n(e){let t=5381;for(let n=0;n<e.length;n++)t=(t<<5)+t+e.charCodeAt(n)&4294967295;return t>>>0}function r(e){return n(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{djb2Hash:function(){return n},hexHash:function(){return r}})},48701:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HeadManagerContext",{enumerable:!0,get:function(){return r}});let r=n(47043)._(n(2265)).default.createContext({})},38293:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return E},defaultHead:function(){return l}});let r=n(47043),i=n(53099),o=n(57437),u=i._(n(2265)),a=r._(n(17421)),s=n(91436),f=n(48701),c=n(23964);function l(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===u.default.Fragment?e.concat(u.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(31765);let _=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:n}=t;return e.reduce(d,[]).reverse().concat(l(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,r={};return i=>{let o=!0,u=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){u=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=_.length;e<t;e++){let t=_[e];if(i.props.hasOwnProperty(t)){if("charSet"===t)n.has(t)?o=!1:n.add(t);else{let e=i.props[t],n=r[t]||new Set;("name"!==t||!u)&&n.has(e)?o=!1:(n.add(e),r[t]=n)}}}}return o}}()).reverse().map((e,t)=>{let r=e.key||t;if(!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,u.default.cloneElement(e,t)}return u.default.cloneElement(e,{key:r})})}let E=function(e){let{children:t}=e,n=(0,u.useContext)(s.AmpStateContext),r=(0,u.useContext)(f.HeadManagerContext);return(0,o.jsx)(a.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,c.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);