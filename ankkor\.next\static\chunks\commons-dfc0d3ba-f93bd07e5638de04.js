"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9965],{4293:function(e,t,n){n.d(t,{I:function(){return o}});var r=n(84962),i=n(87093);function o({keyframes:e,velocity:t=0,power:n=.8,timeConstant:o=325,bounceDamping:a=10,bounceStiffness:u=500,modifyTarget:l,min:s,max:f,restDelta:c=.5,restSpeed:d}){let p,m;let h=e[0],y={done:!1,value:h},v=e=>void 0!==s&&e<s||void 0!==f&&e>f,g=e=>void 0===s?f:void 0===f?s:Math.abs(s-e)<Math.abs(f-e)?s:f,M=n*t,x=h+M,A=void 0===l?x:l(x);A!==x&&(M=A-h);let C=e=>-M*Math.exp(-e/o),w=e=>A+C(e),P=e=>{let t=C(e),n=w(e);y.done=Math.abs(t)<=c,y.value=y.done?A:n},E=e=>{v(y.value)&&(p=e,m=(0,r.S)({keyframes:[y.value,g(y.value)],velocity:(0,i.P)(w,e,y.value),damping:a,stiffness:u,restDelta:c,restSpeed:d}))};return E(0),{calculatedDuration:null,next:e=>{let t=!1;return(m||void 0!==p||(t=!0,P(e),E(e)),void 0!==p&&e>p)?m.next(e-p):(t||P(e),y)}}}},18794:function(e,t,n){n.d(t,{F:function(){return s}});var r=n(50314),i=n(81477),o=n(24775),a=n(22888),u=n(8913),l=n(3609);function s({duration:e=300,keyframes:t,times:n,ease:s="easeInOut"}){let f=(0,i.N)(s)?s.map(o.R):(0,o.R)(s),c={done:!1,value:t[0]},d=(0,l.q)(n&&n.length===t.length?n:(0,u.Y)(t),e),p=(0,a.s)(d,t,{ease:Array.isArray(f)?f:t.map(()=>f||r.mZ).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(c.value=p(t),c.done=t>=e,c)}}},84962:function(e,t,n){n.d(t,{S:function(){return c}});var r=n(56717),i=n(87093),o=n(13223),a=n(59111);function u(e,t){return e*Math.sqrt(1-t*t)}let l=["duration","bounce"],s=["stiffness","damping","mass"];function f(e,t){return t.some(t=>void 0!==e[t])}function c({keyframes:e,restDelta:t,restSpeed:n,...c}){let d;let p=e[0],m=e[e.length-1],h={done:!1,value:p},{stiffness:y,damping:v,mass:g,duration:M,velocity:x,isResolvedFromDuration:A}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!f(e,s)&&f(e,l)){let n=function({duration:e=800,bounce:t=.25,velocity:n=0,mass:i=1}){let l,s;(0,o.K)(e<=(0,r.w)(10),"Spring duration must be 10 seconds or less");let f=1-t;f=(0,a.u)(.05,1,f),e=(0,a.u)(.01,10,(0,r.X)(e)),f<1?(l=t=>{let r=t*f,i=r*e;return .001-(r-n)/u(t,f)*Math.exp(-i)},s=t=>{let r=t*f*e,i=Math.pow(f,2)*Math.pow(t,2)*e,o=u(Math.pow(t,2),f);return(r*n+n-i)*Math.exp(-r)*(-l(t)+.001>0?-1:1)/o}):(l=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),s=t=>e*e*(n-t)*Math.exp(-t*e));let c=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(l,s,5/e);if(e=(0,r.w)(e),isNaN(c))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(c,2)*i;return{stiffness:t,damping:2*f*Math.sqrt(i*t),duration:e}}}(e);(t={...t,...n,mass:1}).isResolvedFromDuration=!0}return t}({...c,velocity:-(0,r.X)(c.velocity||0)}),C=x||0,w=v/(2*Math.sqrt(y*g)),P=m-p,E=(0,r.X)(Math.sqrt(y/g)),b=5>Math.abs(P);if(n||(n=b?.01:2),t||(t=b?.005:.5),w<1){let e=u(E,w);d=t=>m-Math.exp(-w*E*t)*((C+w*E*P)/e*Math.sin(e*t)+P*Math.cos(e*t))}else if(1===w)d=e=>m-Math.exp(-E*e)*(P+(C+E*P)*e);else{let e=E*Math.sqrt(w*w-1);d=t=>{let n=Math.exp(-w*E*t),r=Math.min(e*t,300);return m-n*((C+w*E*P)*Math.sinh(r)+e*P*Math.cosh(r))/e}}return{calculatedDuration:A&&M||null,next:e=>{let r=d(e);if(A)h.done=e>=M;else{let o=C;0!==e&&(o=w<1?(0,i.P)(d,e,r):0);let a=Math.abs(o)<=n,u=Math.abs(m-r)<=t;h.done=a&&u}return h.value=h.done?m:r,h}}}},30431:function(e,t,n){n.d(t,{i:function(){return r}});function r(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}},87093:function(e,t,n){n.d(t,{P:function(){return i}});var r=n(14438);function i(e,t,n){let i=Math.max(t-5,0);return(0,r.R)(n-e(i),t-i)}},54111:function(e,t,n){n.d(t,{v:function(){return A}});var r=n(13223),i=n(56717),o=n(64522),a=n(44046),u=n(54593),l=n(8834);let s={type:"spring",stiffness:500,damping:25,restSpeed:10},f=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),c={type:"keyframes",duration:.8},d={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},p=(e,{keyframes:t})=>t.length>2?c:l.G.has(e)?e.startsWith("scale")?f(t[1]):s:d;var m=n(15636);let h=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(m.P.test(t)||"0"===t)&&!t.startsWith("url("));var y=n(25861),v=n(13697),g=n(79573),M=n(84325),x=n(24118);let A=(e,t,n,l={})=>s=>{let f=(0,g.e)(l,e)||{},c=f.delay||l.delay||0,{elapsed:d=0}=l;d-=(0,i.w)(c);let m=function(e,t,n,r){let i,o;let a=h(t,n);i=Array.isArray(n)?[...n]:[null,n];let u=void 0!==r.from?r.from:e.get(),l=[];for(let e=0;e<i.length;e++){var s;null===i[e]&&(i[e]=0===e?u:i[e-1]),("number"==typeof(s=i[e])?0===s:null!==s?"none"===s||"0"===s||(0,v.W)(s):void 0)&&l.push(e),"string"==typeof i[e]&&"none"!==i[e]&&"0"!==i[e]&&(o=i[e])}if(a&&l.length&&o)for(let e=0;e<l.length;e++)i[l[e]]=(0,y.T)(t,o);return i}(t,e,n,f),A=m[0],C=m[m.length-1],w=h(e,A),P=h(e,C);(0,r.K)(w===P,`You are trying to animate ${e} from "${A}" to "${C}". ${A} is not an animatable value - to enable this animation set ${A} to a value animatable to ${C} via the \`style\` property.`);let E={keyframes:m,velocity:t.getVelocity(),ease:"easeOut",...f,delay:-d,onUpdate:e=>{t.set(e),f.onUpdate&&f.onUpdate(e)},onComplete:()=>{s(),f.onComplete&&f.onComplete()}};if((0,g.r)(f)||(E={...E,...p(e,E)}),E.duration&&(E.duration=(0,i.w)(E.duration)),E.repeatDelay&&(E.repeatDelay=(0,i.w)(E.repeatDelay)),!w||!P||o.c.current||!1===f.type||x.c.skipAnimations)return(0,u.f)(o.c.current?{...E,delay:0}:E);if(!l.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let n=(0,a.P)(t,e,E);if(n)return n}return(0,M.y)(E)}},43289:function(e,t,n){n.d(t,{D:function(){return a}});var r=n(54111),i=n(3078),o=n(23999);function a(e,t,n){let a=(0,o.i)(e)?e:(0,i.BX)(e);return a.start((0,r.v)("",a,t,n)),a.animation}},45647:function(e,t,n){n.d(t,{d:function(){return m}});var r=n(67043),i=n(8834),o=n(61750),a=n(54111),u=n(39593),l=n(48771),s=n(79573),f=n(58345);function c(e,t,{delay:n=0,transitionOverride:r,type:c}={}){let{transition:d=e.getDefaultTransition(),transitionEnd:p,...m}=e.makeTargetAnimatable(t),h=e.getValue("willChange");r&&(d=r);let y=[],v=c&&e.animationState&&e.animationState.getState()[c];for(let t in m){let r=e.getValue(t),l=m[t];if(!r||void 0===l||v&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(v,t))continue;let c={delay:n,elapsed:0,...(0,s.e)(d||{},t)};if(window.HandoffAppearAnimations){let n=e.getProps()[o.M];if(n){let e=window.HandoffAppearAnimations(n,t,r,f.Wi);null!==e&&(c.elapsed=e,c.isHandoff=!0)}}let p=!c.isHandoff&&!function(e,t){let n=e.get();if(!Array.isArray(t))return n!==t;for(let e=0;e<t.length;e++)if(t[e]!==n)return!0}(r,l);if("spring"===c.type&&(r.getVelocity()||c.velocity)&&(p=!1),r.animation&&(p=!1),p)continue;r.start((0,a.v)(t,r,l,e.shouldReduceMotion&&i.G.has(t)?{type:!1}:c));let g=r.animation;(0,u.L)(h)&&(h.add(t),g.then(()=>h.remove(t))),y.push(g)}return p&&Promise.all(y).then(()=>{p&&(0,l.CD)(e,p)}),y}function d(e,t,n={}){let i=(0,r.x)(e,t,n.custom),{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);let a=i?()=>Promise.all(c(e,i,n)):()=>Promise.resolve(),u=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:i=0,staggerChildren:a,staggerDirection:u}=o;return function(e,t,n=0,r=0,i=1,o){let a=[],u=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>u-e*r;return Array.from(e.variantChildren).sort(p).forEach((e,r)=>{e.notify("AnimationStart",t),a.push(d(e,t,{...o,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,i+r,a,u,n)}:()=>Promise.resolve(),{when:l}=o;if(!l)return Promise.all([a(),u(n.delay)]);{let[e,t]="beforeChildren"===l?[a,u]:[u,a];return e().then(()=>t())}}function p(e,t){return e.sortNodePosition(t)}function m(e,t,n={}){let i;if(e.notify("AnimationStart",t),Array.isArray(t))i=Promise.all(t.map(t=>d(e,t,n)));else if("string"==typeof t)i=d(e,t,n);else{let o="function"==typeof t?(0,r.x)(e,t,n.custom):t;i=Promise.all(c(e,o,n))}return i.then(()=>e.notify("AnimationComplete",t))}},61750:function(e,t,n){n.d(t,{M:function(){return r}});let r="data-"+(0,n(17444).D)("framerAppearId")},20569:function(e,t,n){n.d(t,{H:function(){return r}});function r(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}},44944:function(e,t,n){n.d(t,{C:function(){return r}});let r=e=>Array.isArray(e)},79573:function(e,t,n){function r({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:u,from:l,elapsed:s,...f}){return!!Object.keys(f).length}function i(e,t){return e[t]||e.default||e}n.d(t,{e:function(){return i},r:function(){return r}})},48131:function(e,t,n){n.d(t,{M:function(){return v}});var r=n(2265),i=n(31251),o=n(65871),a=n(64252),u=n(53576);class l extends r.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function s({children:e,isPresent:t}){let n=(0,r.useId)(),i=(0,r.useRef)(null),o=(0,r.useRef)({width:0,height:0,top:0,left:0});return(0,r.useInsertionEffect)(()=>{let{width:e,height:r,top:a,left:u}=o.current;if(t||!i.current||!e||!r)return;i.current.dataset.motionPopId=n;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            top: ${a}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[t]),r.createElement(l,{isPresent:t,childRef:i,sizeRef:o},r.cloneElement(e,{ref:i}))}let f=({children:e,initial:t,isPresent:n,onExitComplete:i,custom:o,presenceAffectsLayout:l,mode:f})=>{let d=(0,u.h)(c),p=(0,r.useId)(),m=(0,r.useMemo)(()=>({id:p,initial:t,isPresent:n,custom:o,onExitComplete:e=>{for(let t of(d.set(e,!0),d.values()))if(!t)return;i&&i()},register:e=>(d.set(e,!1),()=>d.delete(e))}),l?void 0:[n]);return(0,r.useMemo)(()=>{d.forEach((e,t)=>d.set(t,!1))},[n]),r.useEffect(()=>{n||d.size||!i||i()},[n]),"popLayout"===f&&(e=r.createElement(s,{isPresent:n},e)),r.createElement(a.O.Provider,{value:m},e)};function c(){return new Map}var d=n(58881),p=n(11534),m=n(42762),h=n(13223);let y=e=>e.key||"",v=({children:e,custom:t,initial:n=!0,onExitComplete:a,exitBeforeEnter:u,presenceAffectsLayout:l=!0,mode:s="sync"})=>{(0,h.k)(!u,"Replace exitBeforeEnter with mode='wait'");let c=(0,r.useContext)(d.p).forceRender||(0,i.N)()[0],v=(0,o.t)(),g=function(e){let t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}(e),M=g,x=(0,r.useRef)(new Map).current,A=(0,r.useRef)(M),C=(0,r.useRef)(new Map).current,w=(0,r.useRef)(!0);if((0,p.L)(()=>{w.current=!1,function(e,t){e.forEach(e=>{let n=y(e);t.set(n,e)})}(g,C),A.current=M}),(0,m.z)(()=>{w.current=!0,C.clear(),x.clear()}),w.current)return r.createElement(r.Fragment,null,M.map(e=>r.createElement(f,{key:y(e),isPresent:!0,initial:!!n&&void 0,presenceAffectsLayout:l,mode:s},e)));M=[...M];let P=A.current.map(y),E=g.map(y),b=P.length;for(let e=0;e<b;e++){let t=P[e];-1!==E.indexOf(t)||x.has(t)||x.set(t,void 0)}return"wait"===s&&x.size&&(M=[]),x.forEach((e,n)=>{if(-1!==E.indexOf(n))return;let i=C.get(n);if(!i)return;let o=P.indexOf(n),u=e;u||(u=r.createElement(f,{key:y(i),isPresent:!1,onExitComplete:()=>{x.delete(n);let e=Array.from(C.keys()).filter(e=>!E.includes(e));if(e.forEach(e=>C.delete(e)),A.current=g.filter(t=>{let r=y(t);return r===n||e.includes(r)}),!x.size){if(!1===v.current)return;c(),a&&a()}},custom:t,presenceAffectsLayout:l,mode:s},i),x.set(n,u)),M.splice(o,0,u)}),M=M.map(e=>{let t=e.key;return x.has(t)?e:r.createElement(f,{key:y(e),isPresent:!0,presenceAffectsLayout:l,mode:s},e)}),r.createElement(r.Fragment,null,x.size?M:M.map(e=>(0,r.cloneElement)(e)))}},49637:function(e,t,n){n.d(t,{oO:function(){return o}});var r=n(2265),i=n(64252);function o(){let e=(0,r.useContext)(i.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:n,register:o}=e,a=(0,r.useId)();return(0,r.useEffect)(()=>o(a),[]),!t&&n?[!1,()=>n&&n(a)]:[!0]}},58881:function(e,t,n){n.d(t,{p:function(){return r}});let r=(0,n(2265).createContext)({})},47337:function(e,t,n){n.d(t,{u:function(){return r}});let r=(0,n(2265).createContext)({strict:!1})},45750:function(e,t,n){n.d(t,{_:function(){return r}});let r=(0,n(2265).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},82349:function(e,t,n){n.d(t,{H:function(){return u}});var r=n(2265),i=n(56961),o=n(74115),a=n(17743);function u(e){let{initial:t,animate:n}=function(e,t){if((0,a.G)(e)){let{initial:t,animate:n}=e;return{initial:!1===t||(0,o.$)(t)?t:void 0,animate:(0,o.$)(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,r.useContext)(i.v));return(0,r.useMemo)(()=>({initial:t,animate:n}),[l(t),l(n)])}function l(e){return Array.isArray(e)?e.join(" "):e}},56961:function(e,t,n){n.d(t,{v:function(){return r}});let r=(0,n(2265).createContext)({})},64252:function(e,t,n){n.d(t,{O:function(){return r}});let r=(0,n(2265).createContext)(null)},29913:function(e,t,n){n.d(t,{g:function(){return r}});let r=(0,n(2265).createContext)({})}}]);