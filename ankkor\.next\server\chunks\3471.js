"use strict";exports.id=3471,exports.ids=[3471],exports.modules={53471:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{Z:()=>j});var i=a(10326),r=a(17577),l=a(90434),c=a(92148),n=a(67427),o=a(75290),d=a(34565),x=a(86806),u=a(96040),p=a(68897),m=a(77321),f=a(44960),h=a(68471),g=a(40381),v=e([u,p]);[u,p]=v.then?(await v)():v;let N=e=>{if("number"==typeof e)return e.toString();if(!e)return"0";let t=e.toString().replace(/[^\d.-]/g,""),a=parseFloat(t);return isNaN(a)?"0":a.toString()},j=({id:e,name:t,price:a,image:s,slug:v,material:j,isNew:w=!1,stockStatus:b="IN_STOCK",compareAtPrice:y=null,regularPrice:S=null,salePrice:$=null,onSale:k=!1,currencySymbol:A=h.J6,currencyCode:O=h.EJ,shortDescription:I,type:C})=>{let[T,E]=(0,r.useState)(!1),F=(0,x.rY)(),{openCart:Z}=(0,m.j)(),{addToWishlist:_,isInWishlist:H,removeFromWishlist:M}=(0,u.Y)(),{isAuthenticated:R}=(0,p.O)(),D=H(e),K=async i=>{if(i.preventDefault(),i.stopPropagation(),!e||""===e){console.error("Cannot add to cart: Missing product ID for product",t),g.Am.error("Cannot add to cart: Invalid product");return}if(!T){E(!0),console.log(`Adding product to cart: ${t} (ID: ${e})`);try{await F.addToCart({productId:e,quantity:1,name:t,price:a,image:{url:s,altText:t}}),g.Am.success(`${t} added to cart!`),Z()}catch(e){console.error(`Failed to add ${t} to cart:`,e),g.Am.error("Failed to add item to cart. Please try again.")}finally{E(!1)}}},P=i=>{i.preventDefault(),i.stopPropagation(),D?(M(e),g.Am.success("Removed from wishlist")):(_({id:e,name:t,price:N(a),image:s,handle:v,material:j||"Material not specified",variantId:e}),R?g.Am.success("Added to your wishlist"):g.Am.success("Added to wishlist (saved locally)"))},z=y&&parseFloat(y)>parseFloat(a)?Math.round((parseFloat(y)-parseFloat(a))/parseFloat(y)*100):null,J="IN_STOCK"!==b;return(0,i.jsxs)(c.E.div,{className:"group relative",whileHover:{y:-5},transition:{duration:.3},children:[(0,i.jsxs)(l.default,{href:`/product/${v}`,className:"block",children:[(0,i.jsxs)("div",{className:"relative overflow-hidden mb-4",children:[i.jsx("div",{className:"aspect-[3/4] relative bg-[#f4f3f0] overflow-hidden",children:i.jsx(f.Z,{src:s,alt:t,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",animate:!0,className:"h-full"})}),(0,i.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[i.jsx(c.E.button,{onClick:P,className:`p-2 rounded-none ${D?"bg-[#2c2c27]":"bg-[#f8f8f5]"}`,whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":D?"Remove from wishlist":"Add to wishlist",children:i.jsx(n.Z,{className:`h-5 w-5 ${D?"text-[#f4f3f0] fill-current":"text-[#2c2c27]"}`})}),i.jsx(c.E.button,{onClick:K,className:`p-2 rounded-none ${J||T?"bg-gray-400 cursor-not-allowed":"bg-[#2c2c27]"} text-[#f4f3f0]`,whileHover:J||T?{}:{scale:1.05},whileTap:J||T?{}:{scale:.95},"aria-label":J?"Out of stock":T?"Adding to cart...":"Add to cart",disabled:J||T,children:T?i.jsx(o.Z,{className:"h-5 w-5 animate-spin"}):i.jsx(d.Z,{className:"h-5 w-5"})})]}),w&&i.jsx("div",{className:"absolute top-0 left-0 bg-[#2c2c27] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"New"}),J&&i.jsx("div",{className:"absolute top-0 right-0 bg-red-600 text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"Out of Stock"}),!J&&z&&(0,i.jsxs)("div",{className:"absolute top-0 right-0 bg-[#8a8778] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:[z,"% Off"]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[i.jsx("h3",{className:"font-serif text-lg text-[#2c2c27] mb-1 line-clamp-2",children:t}),j&&i.jsx("p",{className:"text-[#8a8778] text-xs",children:j}),C&&i.jsx("p",{className:"text-[#8a8778] text-xs capitalize",children:C.toLowerCase().replace("_"," ")}),I&&i.jsx("p",{className:"text-[#5c5c52] text-xs line-clamp-2",dangerouslySetInnerHTML:{__html:I.replace(/<[^>]*>/g,"")}}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 product-card-price",children:[i.jsx("p",{className:"text-[#2c2c27] font-medium",children:k&&$?$.toString().includes("₹")||$.toString().includes("$")||$.toString().includes("€")||$.toString().includes("\xa3")?$:`${A}${$}`:a.toString().includes("₹")||a.toString().includes("$")||a.toString().includes("€")||a.toString().includes("\xa3")?a:`${A}${a}`}),k&&S&&i.jsx("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:S.toString().includes("₹")||S.toString().includes("$")||S.toString().includes("€")||S.toString().includes("\xa3")?S:`${A}${S}`}),!k&&y&&parseFloat(y.toString().replace(/[₹$€£]/g,""))>parseFloat(a.toString().replace(/[₹$€£]/g,""))&&i.jsx("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:y.toString().includes("₹")||y.toString().includes("$")||y.toString().includes("€")||y.toString().includes("\xa3")?y:`${A}${y}`})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[i.jsx("div",{className:"flex items-center gap-2",children:"IN_STOCK"===b?i.jsx("span",{className:"text-green-600 text-xs font-medium",children:"✓ In Stock"}):"OUT_OF_STOCK"===b?i.jsx("span",{className:"text-red-600 text-xs font-medium",children:"✗ Out of Stock"}):"ON_BACKORDER"===b?i.jsx("span",{className:"text-orange-600 text-xs font-medium",children:"⏳ Backorder"}):i.jsx("span",{className:"text-gray-600 text-xs font-medium",children:"? Unknown"})}),k&&i.jsx("span",{className:"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium",children:"Sale"})]})]})]})]}),(0,i.jsxs)("div",{className:"mt-4 space-y-2",children:[i.jsx(c.E.button,{onClick:K,className:`w-full py-3 px-4 transition-all duration-200 ${J||T?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-[#2c2c27] text-[#f4f3f0] hover:bg-[#1a1a17]"}`,whileHover:J||T?{}:{scale:1.02},whileTap:J||T?{}:{scale:.98},"aria-label":J?"Out of stock":T?"Adding to cart...":"Add to cart",disabled:J||T,children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[T?i.jsx(o.Z,{className:"h-4 w-4 animate-spin"}):i.jsx(d.Z,{className:"h-4 w-4"}),i.jsx("span",{className:"text-sm font-medium",children:J?"Out of Stock":T?"Adding...":"Add to Cart"})]})}),i.jsx(c.E.button,{onClick:P,className:`w-full py-3 px-4 border transition-all duration-200 ${D?"bg-[#2c2c27] text-[#f4f3f0] border-[#2c2c27]":"bg-transparent text-[#2c2c27] border-[#2c2c27] hover:bg-[#2c2c27] hover:text-[#f4f3f0]"}`,whileHover:{scale:1.02},whileTap:{scale:.98},"aria-label":D?"Remove from wishlist":"Add to wishlist",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[i.jsx(n.Z,{className:`h-4 w-4 ${D?"fill-current":""}`}),i.jsx("span",{className:"text-sm font-medium",children:D?"In Wishlist":"Add to Wishlist"})]})})]})]})};s()}catch(e){s(e)}})},44960:(e,t,a)=>{a.d(t,{Z:()=>c});var s=a(10326),i=a(17577),r=a(46226),l=a(92148);let c=({src:e,alt:t,width:a,height:c,fill:n=!1,sizes:o=n?"(max-width: 768px) 100vw, 50vw":void 0,priority:d=!1,className:x="",animate:u=!0,style:p={}})=>{let[m,f]=(0,i.useState)(!0),[h,g]=(0,i.useState)(!1);return(0,s.jsxs)("div",{className:`relative overflow-hidden ${x}`,style:{minHeight:n?"100%":void 0,height:n?"100%":void 0,...p},onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:[m&&s.jsx(l.E.div,{className:"absolute inset-0 bg-[#f4f3f0]",initial:{opacity:1},animate:{opacity:[.5,.8,.5],backgroundPosition:["0% 0%","100% 100%"]},transition:{opacity:{duration:1.5,repeat:1/0,ease:"easeInOut"},backgroundPosition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},style:{background:"linear-gradient(90deg, #f4f3f0, #e5e2d9, #f4f3f0)",backgroundSize:"200% 100%"}}),s.jsx(l.E.div,{className:"w-full h-full",animate:u&&h?{scale:1.05,filter:"brightness(1.1)"}:{scale:1,filter:"brightness(1)"},transition:{duration:.7,ease:"easeInOut"},children:s.jsx(r.default,{src:e,alt:t,width:a,height:c,fill:n,sizes:o,priority:d,className:`
            ${m?"opacity-0":"opacity-100"} 
            transition-opacity duration-500
            ${n?"object-cover":""}
          `,onLoad:()=>f(!1)})})]})}},68471:(e,t,a)=>{a.d(t,{EJ:()=>i,J6:()=>s});let s="₹",i="INR"}};