"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5363],{87466:function(t,r,e){var i=e(29865),a=e(40257);new i.s({url:a.env.UPSTASH_REDIS_REST_URL||a.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:a.env.UPSTASH_REDIS_REST_TOKEN||a.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""})},67111:function(t,r,e){e.d(r,{xS:function(){return n}});var i=e(59625),a=e(89134),o=e(82372);let n=(0,i.Ue)()((0,a.tJ)((t,r)=>({id:null,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1,error:null,initializeCart:async()=>{t({isLoading:!0,error:null});try{let r=await o.dv();if(r){let e=o.Id(r);t({id:e.id,items:e.lines.map(t=>({id:t.id,productId:t.merchandise.product.id,variationId:t.merchandise.id!==t.merchandise.product.id?t.merchandise.id:void 0,quantity:t.quantity,name:t.merchandise.title,price:t.cost.totalAmount.amount,image:t.merchandise.product.image,attributes:t.merchandise.selectedOptions})),itemCount:e.totalQuantity,subtotal:e.cost.subtotalAmount.amount,total:e.cost.totalAmount.amount,isLoading:!1});return}let e=await o.Bk();if(e){let r=o.Id(e);t({id:r.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(r){console.error("Error initializing cart:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},addToCart:async e=>{t({isLoading:!0,error:null});try{r().id||await r().initializeCart();let i=[{productId:e.productId,quantity:e.quantity,variationId:e.variationId}],a=await o.Xq("",i);if(a){let r=o.Id(a);t({items:r.lines.map(t=>({id:t.id,productId:t.merchandise.product.id,variationId:t.merchandise.id!==t.merchandise.product.id?t.merchandise.id:void 0,quantity:t.quantity,name:t.merchandise.title,price:t.cost.totalAmount.amount,image:t.merchandise.product.image,attributes:t.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to add item to cart")}catch(r){console.error("Error adding item to cart:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:async(e,i)=>{t({isLoading:!0,error:null});try{if(!r().id)throw Error("Cart not initialized");if(i<=0)return r().removeCartItem(e);let a=await o.xu([{key:e,quantity:i}]);if(a){let r=o.Id(a);t({items:r.lines.map(t=>({id:t.id,productId:t.merchandise.product.id,variationId:t.merchandise.id!==t.merchandise.product.id?t.merchandise.id:void 0,quantity:t.quantity,name:t.merchandise.title,price:t.cost.totalAmount.amount,image:t.merchandise.product.image,attributes:t.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to update cart item")}catch(r){console.error("Error updating cart item:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:async e=>{t({isLoading:!0,error:null});try{if(!r().id)throw Error("Cart not initialized");let i=await o.h2("",[e]);if(i){let r=o.Id(i);t({items:r.lines.map(t=>({id:t.id,productId:t.merchandise.product.id,variationId:t.merchandise.id!==t.merchandise.product.id?t.merchandise.id:void 0,quantity:t.quantity,name:t.merchandise.title,price:t.cost.totalAmount.amount,image:t.merchandise.product.image,attributes:t.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to remove item from cart")}catch(r){console.error("Error removing cart item:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},clearCart:async()=>{t({isLoading:!0,error:null});try{let r=await o.Bk();if(r){let e=o.Id(r);t({id:e.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(r){console.error("Error clearing cart:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},setError:r=>t({error:r}),setIsLoading:r=>t({isLoading:r})}),{name:"woo-cart-storage",version:1,partialize:t=>({id:t.id}),onRehydrateStorage:()=>t=>{t&&t.id&&t.initializeCart()}}))}}]);