#!/usr/bin/env node

/**
 * Test script for WooCommerce guest checkout functionality
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Read environment variables from .env.local
let woocommerceUrl = '';
try {
  const envContent = fs.readFileSync(path.join(process.cwd(), '.env.local'), 'utf-8');
  const envLines = envContent.split('\n');
  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_WOOCOMMERCE_URL=')) {
      woocommerceUrl = line.split('=')[1].trim();
      break;
    } else if (line.startsWith('NEXT_PUBLIC_WORDPRESS_URL=')) {
      woocommerceUrl = line.split('=')[1].trim();
    }
  }
} catch (err) {
  console.error('Error reading .env.local file:', err.message);
}

// Ask for the WooCommerce URL if not found in .env.local
if (!woocommerceUrl) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('Enter your WooCommerce site URL (e.g. https://example.com): ', (answer) => {
    woocommerceUrl = answer.trim();
    runTests(woocommerceUrl);
    rl.close();
  });
} else {
  runTests(woocommerceUrl);
}

function runTests(baseUrl) {
  console.log(`\n========================================================`);
  console.log(`🧪 TESTING WOOCOMMERCE GUEST CHECKOUT`);
  console.log(`========================================================\n`);
  console.log(`Using WooCommerce URL: ${baseUrl}\n`);

  // Create a temporary HTML file with test buttons
  const tempHtmlPath = path.join(process.cwd(), 'guest-checkout-test.html');
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WooCommerce Guest Checkout Test</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    h1 { color: #7f54b3; }
    .test-group { margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; border-radius: 4px; }
    h2 { margin-top: 0; color: #2c3e50; }
    button { background-color: #7f54b3; color: white; border: none; padding: 10px 15px; margin: 5px 0; cursor: pointer; border-radius: 4px; }
    button:hover { background-color: #6b4a99; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    .note { background-color: #fffbea; padding: 10px; border-left: 4px solid #f0c674; margin-top: 20px; }
  </style>
</head>
<body>
  <h1>WooCommerce Guest Checkout Test</h1>
  <p>This page helps you test different approaches for guest checkout. Click each button to try a different method.</p>
  
  <div class="test-group">
    <h2>Test 1: Direct Guest Checkout URL</h2>
    <button onclick="window.open('${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1')">
      Test Guest Checkout Parameters
    </button>
    <p>This test uses URL parameters to force guest checkout.</p>
  </div>

  <div class="test-group">
    <h2>Test 2: Custom API Endpoints</h2>
    <button onclick="window.open('${baseUrl}/wp-json/ankkor/v1/guest-checkout')">
      Test /guest-checkout API
    </button>
    <button onclick="testFixCheckout()">
      Test /fix-checkout API
    </button>
    <p>These tests check if the custom API endpoints are working.</p>
  </div>

  <div class="test-group">
    <h2>Test 3: Add Product & Checkout</h2>
    <button onclick="window.open('${baseUrl}/shop')">
      Go to Shop
    </button>
    <p>Go to the shop, add a product to cart, and proceed to checkout.</p>
  </div>

  <div class="note">
    <strong>Important:</strong> For best testing, use an incognito/private browser window to ensure you're not already logged in.
  </div>

  <div class="test-group">
    <h2>Troubleshooting</h2>
    <p>If you're still having issues:</p>
    <ol>
      <li>Make sure both plugins are properly activated</li>
      <li>Clear all caches (WordPress, browser, CDN)</li>
      <li>Check WooCommerce settings at ${baseUrl}/wp-admin/admin.php?page=wc-settings&tab=account</li>
      <li>Check the browser console for any errors</li>
    </ol>
  </div>

  <script>
    async function testFixCheckout() {
      try {
        const response = await fetch('${baseUrl}/wp-json/ankkor/v1/fix-checkout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ force_guest_checkout: true })
        });
        
        const result = await response.json();
        alert('API Response: ' + JSON.stringify(result));
        
        if (result.success) {
          window.open('${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1');
        }
      } catch (error) {
        alert('Error testing fix-checkout API: ' + error.message);
      }
    }
  </script>
</body>
</html>
  `;

  fs.writeFileSync(tempHtmlPath, htmlContent);
  console.log(`Created test page at: ${tempHtmlPath}\n`);
  
  // Open the test page in the default browser
  try {
    console.log('Opening test page in your default browser...');
    
    // Cross-platform command to open the file in default browser
    if (process.platform === 'win32') {
      execSync(`start ${tempHtmlPath}`);
    } else if (process.platform === 'darwin') {
      execSync(`open ${tempHtmlPath}`);
    } else {
      execSync(`xdg-open ${tempHtmlPath}`);
    }
    
    console.log(`\n✅ Test page opened successfully!`);
    console.log(`\nIMPORTANT: For best results, copy the URL from the browser and paste it into an incognito/private window.`);
    console.log(`This ensures you're testing as a non-logged-in user.\n`);
    
  } catch (err) {
    console.error('Error opening test page:', err.message);
    console.log(`\nPlease open this file manually in your browser: ${tempHtmlPath}\n`);
  }
} 