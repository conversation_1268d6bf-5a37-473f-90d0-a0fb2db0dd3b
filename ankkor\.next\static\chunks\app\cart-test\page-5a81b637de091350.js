(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1214],{62612:function(e,t,s){Promise.resolve().then(s.bind(s,40733))},40733:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return i}});var r=s(57437);s(2265);var n=s(87758);function i(){let e=(0,n.rY)();return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Cart Test"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{onClick:()=>{e.addToCart({productId:"123",quantity:1,name:"Test Product",price:"99.99",image:{url:"/placeholder-product.jpg",altText:"Test Product"}})},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Add Test Item to Cart"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold",children:["Cart Items (",e.itemCount,")"]}),0===e.items.length?(0,r.jsx)("p",{children:"No items in cart"}):(0,r.jsx)("ul",{className:"space-y-2",children:e.items.map(t=>(0,r.jsxs)("li",{className:"border p-2 rounded",children:[(0,r.jsxs)("div",{children:[t.name," - ₹",t.price," x ",t.quantity]}),(0,r.jsx)("button",{onClick:()=>e.removeCartItem(t.id),className:"text-red-500 text-sm",children:"Remove"})]},t.id))})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:["Subtotal: ₹",e.subtotal().toFixed(2)]}),(0,r.jsxs)("p",{children:["Total: ₹",e.total().toFixed(2)]})]}),(0,r.jsx)("button",{onClick:e.clearCart,className:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"Clear Cart"})]})]})}}},function(e){e.O(0,[632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,6518,4595,3280,8790,1104,7158,6758,7044,5302,6628,5363,4754,5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,5717,8496,9429,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,1744],function(){return e(e.s=62612)}),_N_E=e.O()}]);