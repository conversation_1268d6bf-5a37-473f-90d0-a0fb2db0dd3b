(()=>{var e={};e.id=4645,e.ids=[4645],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},38684:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>c.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>f,tree:()=>n}),s(69601),s(41783),s(12523);var r=s(23191),a=s(88716),o=s(37922),c=s.n(o),l=s(95231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let n=["",{children:["customer-service",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,69601)),"E:\\ankkorwoo\\ankkor\\src\\app\\customer-service\\contact\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,41783)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\customer-service\\contact\\page.tsx"],m="/customer-service/contact/page",x={require:s,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/customer-service/contact/page",pathname:"/customer-service/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},34627:(e,t,s)=>{Promise.resolve().then(s.bind(s,31037))},48998:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},31037:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(10326);s(17577);var a=s(46226),o=s(90434),c=s(76557);let l=(0,c.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),i=(0,c.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),n=(0,c.Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var d=s(48998),m=s(54659),x=s(87888);let f=(0,c.Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);var u=s(60329),p=s(68211);let h=()=>{let e=process.env.NEXT_PUBLIC_FORMSPREE_FORM_ID||"xblgekrr",[t,s]=(0,u.cI)(e,{data:{form_type:"contact"}});return r.jsx("div",{className:"w-full max-w-2xl mx-auto",children:t.succeeded?(0,r.jsxs)("div",{className:"bg-[#f4f3f0] p-8 rounded-lg text-center",children:[r.jsx(m.Z,{className:"w-16 h-16 text-[#2c2c27] mx-auto mb-4"}),r.jsx("h3",{className:"font-serif text-2xl font-bold text-[#2c2c27] mb-2",children:"Message Sent"}),r.jsx("p",{className:"text-[#5c5c52] mb-6",children:"Thank you for contacting us. We have received your message and will respond shortly."}),r.jsx("button",{onClick:()=>window.location.reload(),className:"bg-[#2c2c27] text-[#f4f3f0] px-6 py-3 text-sm uppercase tracking-wider hover:bg-[#3d3d35] transition-colors",children:"Send Another Message"})]}):(0,r.jsxs)("form",{onSubmit:s,className:"space-y-6",children:[t.errors&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 p-4 rounded-lg flex items-start",children:[r.jsx(x.Z,{className:"w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-red-800 font-medium",children:"Error"}),r.jsx("p",{className:"text-red-700 text-sm",children:"There was a problem sending your message. Please try again later."})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"name",className:"block text-[#5c5c52] mb-1",children:"Name"}),r.jsx("input",{type:"text",id:"name",name:"name",className:"w-full border border-[#e5e2d9] bg-[#f8f8f5] p-3 focus:border-[#8a8778] focus:outline-none focus:ring-1 focus:ring-[#8a8778]",required:!0}),r.jsx(u.p8,{prefix:"Name",field:"name",errors:t.errors,className:"text-red-500 text-sm mt-1"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"email",className:"block text-[#5c5c52] mb-1",children:"Email"}),r.jsx("input",{type:"email",id:"email",name:"email",className:"w-full border border-[#e5e2d9] bg-[#f8f8f5] p-3 focus:border-[#8a8778] focus:outline-none focus:ring-1 focus:ring-[#8a8778]",required:!0}),r.jsx(u.p8,{prefix:"Email",field:"email",errors:t.errors,className:"text-red-500 text-sm mt-1"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"subject",className:"block text-[#5c5c52] mb-1",children:"Subject"}),r.jsx("input",{type:"text",id:"subject",name:"subject",className:"w-full border border-[#e5e2d9] bg-[#f8f8f5] p-3 focus:border-[#8a8778] focus:outline-none focus:ring-1 focus:ring-[#8a8778]",required:!0}),r.jsx(u.p8,{prefix:"Subject",field:"subject",errors:t.errors,className:"text-red-500 text-sm mt-1"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"message",className:"block text-[#5c5c52] mb-1",children:"Message"}),r.jsx("textarea",{id:"message",name:"message",rows:6,className:"w-full border border-[#e5e2d9] bg-[#f8f8f5] p-3 focus:border-[#8a8778] focus:outline-none focus:ring-1 focus:ring-[#8a8778]",required:!0}),r.jsx(u.p8,{prefix:"Message",field:"message",errors:t.errors,className:"text-red-500 text-sm mt-1"})]}),r.jsx("div",{className:"pt-2",children:r.jsx("button",{type:"submit",disabled:t.submitting,className:"bg-[#2c2c27] text-[#f4f3f0] px-8 py-3 flex items-center justify-center text-sm uppercase tracking-wider hover:bg-[#3d3d35] transition-colors disabled:opacity-70 disabled:cursor-not-allowed w-full md:w-auto",children:t.submitting?(0,r.jsxs)(r.Fragment,{children:[r.jsx(p.Z,{size:"sm",color:"#f4f3f0",className:"mr-2"}),"Sending..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(f,{className:"w-4 h-4 mr-2"}),"Send Message"]})})}),r.jsx(u.p8,{errors:t.errors})]})})};function b(){return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,r.jsxs)("div",{className:"mb-12 text-center",children:[r.jsx("h1",{className:"mb-4 font-serif text-4xl font-bold text-[#2c2c27]",children:"Contact Us"}),r.jsx("p",{className:"mx-auto max-w-2xl text-[#5c5c52]",children:"We're here to assist you with any questions or concerns. Please feel free to reach out to us using the form below or through our contact information."})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-12",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h2",{className:"mb-8 border-b border-[#e5e2d9] pb-4 font-serif text-2xl font-bold text-[#2c2c27]",children:"Send Us a Message"}),r.jsx(h,{})]}),(0,r.jsxs)("div",{className:"w-full lg:w-96",children:[r.jsx("h2",{className:"mb-8 border-b border-[#e5e2d9] pb-4 font-serif text-2xl font-bold text-[#2c2c27]",children:"Contact Information"}),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx(l,{className:"mr-4 h-5 w-5 text-[#8a8778]"}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"mb-4 font-serif text-xl font-bold text-[#2c2c27]",children:"Email Us"}),r.jsx("p",{className:"mb-1 text-[#5c5c52]",children:r.jsx("a",{href:"mailto:<EMAIL>",className:"hover:text-[#2c2c27] hover:underline",children:"<EMAIL>"})}),r.jsx("p",{className:"text-[#5c5c52]"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx(i,{className:"mr-4 h-5 w-5 text-[#8a8778]"}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"mb-4 font-serif text-xl font-bold text-[#2c2c27]",children:"Call Us"}),r.jsx("p",{className:"mb-1 text-[#5c5c52]",children:r.jsx("a",{href:"tel:+91 9815319207",className:"hover:text-[#2c2c27] hover:underline",children:"+91 9815319207"})}),r.jsx("p",{className:"text-sm text-[#8a8778]",children:"Monday to Friday, 10am - 6pm IST"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx(n,{className:"mr-4 h-5 w-5 text-[#8a8778]"}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"mb-4 font-serif text-xl font-bold text-[#2c2c27]",children:"Operational Address:"}),r.jsx("p",{className:"mb-1 text-[#5c5c52]",children:"163/A Jagdish Nagar, St. 5 Avtar Market, Dugri Road"}),r.jsx("p",{className:"text-[#5c5c52]",children:"Ludhiana 141013, Punjab, India"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx(d.Z,{className:"mr-4 h-5 w-5 text-[#8a8778]"}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"mb-4 font-serif text-xl font-bold text-[#2c2c27]",children:"Business Hours"}),r.jsx("p",{className:"mb-1 text-[#5c5c52]",children:"Orders: 24*7"})]})]})]}),r.jsx("div",{className:"mt-10 overflow-hidden rounded-lg",children:r.jsx(a.default,{src:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80",alt:"Ankkor Store",width:400,height:300,className:"h-auto w-full image-animate transition-all duration-700"})})]})]}),(0,r.jsxs)("div",{className:"mt-16 border-t border-[#e5e2d9] pt-12 text-center",children:[r.jsx("h3",{className:"mb-4 font-serif text-2xl font-bold text-[#2c2c27]",children:"Have Questions?"}),r.jsx("p",{className:"mb-6 text-[#5c5c52]",children:"Check our frequently asked questions for quick answers to common inquiries."}),r.jsx(o.default,{href:"/customer-service/faq",className:"inline-block border border-[#2c2c27] px-8 py-3 text-sm uppercase tracking-wider text-[#2c2c27] transition-colors hover:bg-[#f4f3f0]",children:"View FAQ"})]})]})}},68211:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(10326),a=s(77626),o=s.n(a);s(17577);let c=({size:e="md",color:t="#2c2c27",className:s=""})=>{let a={sm:{container:"w-6 h-6",dot:"w-1 h-1"},md:{container:"w-10 h-10",dot:"w-1.5 h-1.5"},lg:{container:"w-16 h-16",dot:"w-2 h-2"}};return(0,r.jsxs)("div",{className:`jsx-cba83ab4e8da42d9 flex items-center justify-center ${s}`,children:[(0,r.jsxs)("div",{className:`jsx-cba83ab4e8da42d9 relative ${a[e].container}`,children:[r.jsx("div",{style:{backgroundColor:t,animation:"loaderDot1 1.5s infinite"},className:`jsx-cba83ab4e8da42d9 absolute top-0 left-1/2 -translate-x-1/2 ${a[e].dot} rounded-full`}),r.jsx("div",{style:{backgroundColor:t,animation:"loaderDot2 1.5s infinite"},className:`jsx-cba83ab4e8da42d9 absolute top-1/2 right-0 -translate-y-1/2 ${a[e].dot} rounded-full`}),r.jsx("div",{style:{backgroundColor:t,animation:"loaderDot3 1.5s infinite"},className:`jsx-cba83ab4e8da42d9 absolute bottom-0 left-1/2 -translate-x-1/2 ${a[e].dot} rounded-full`}),r.jsx("div",{style:{backgroundColor:t,animation:"loaderDot4 1.5s infinite"},className:`jsx-cba83ab4e8da42d9 absolute top-1/2 left-0 -translate-y-1/2 ${a[e].dot} rounded-full`}),r.jsx("div",{style:{border:`2px solid ${t}`,borderTopColor:"transparent",animation:"loaderRotate 1s linear infinite"},className:"jsx-cba83ab4e8da42d9 absolute inset-0 rounded-full"})]}),r.jsx(o(),{id:"cba83ab4e8da42d9",children:"@-webkit-keyframes loaderRotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes loaderRotate{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes loaderRotate{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes loaderRotate{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-moz-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-o-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-webkit-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-moz-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-o-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-webkit-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-moz-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-o-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-webkit-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@-moz-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@-o-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}"})]})}},69601:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\customer-service\contact\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,7499,9404,2481,329,4520,1651],()=>s(38684));module.exports=r})();