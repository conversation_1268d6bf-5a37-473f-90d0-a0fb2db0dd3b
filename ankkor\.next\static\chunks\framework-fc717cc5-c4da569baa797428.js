(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1539],{36423:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let a=l(53099),r=l(57437),n=a._(l(2265)),u=l(61956);function o(){let e=(0,n.useContext)(u.TemplateContext);return(0,r.jsx)(r.Fragment,{children:e})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20544:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var l in t)Object.defineProperty(e,l,{enumerable:!0,get:t[l]})}(t,{getExpectedRequestStore:function(){return r},requestAsyncStorage:function(){return a.requestAsyncStorage}});let a=l(25575);function r(e){let t=a.requestAsyncStorage.getStore();if(t)return t;throw Error("`"+e+"` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22356:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return n}});let a=l(27420),r=l(92576);function n(e,t,l,n){let[u,o,f]=l.slice(-3);if(null===o)return!1;if(3===l.length){let l=o[2],r=o[3];t.loading=r,t.rsc=l,t.prefetchRsc=null,(0,a.fillLazyItemsTillLeafWithHead)(t,e,u,o,f,n)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,r.fillCacheWithNewSubTreeData)(t,e,l,n);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81935:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,l,a,o){let f;let[c,d,i,s,p]=l;if(1===t.length){let e=u(l,a,t);return(0,n.addRefreshMarkerToActiveParallelSegments)(e,o),e}let[h,y]=t;if(!(0,r.matchSegment)(h,c))return null;if(2===t.length)f=u(d[y],a,t);else if(null===(f=e(t.slice(2),d[y],a,o)))return null;let _=[t[0],{...d,[y]:f},i,s];return p&&(_[4]=!0),(0,n.addRefreshMarkerToActiveParallelSegments)(_,o),_}}});let a=l(84541),r=l(76015),n=l(50232);function u(e,t,l){let[n,o]=e,[f,c]=t;if(f===a.DEFAULT_SEGMENT_KEY&&n!==a.DEFAULT_SEGMENT_KEY)return e;if((0,r.matchSegment)(n,f)){let t={};for(let e in o)void 0!==c[e]?t[e]=u(o[e],c[e],l):t[e]=o[e];for(let e in c)t[e]||(t[e]=c[e]);let a=[n,t];return e[2]&&(a[2]=e[2]),e[3]&&(a[3]=e[3]),e[4]&&(a[4]=e[4]),a}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65556:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,l,r){let n=r.length<=2,[u,o]=r,f=(0,a.createRouterCacheKey)(o),c=l.parallelRoutes.get(u),d=t.parallelRoutes.get(u);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(u,d));let i=null==c?void 0:c.get(f),s=d.get(f);if(n){s&&s.lazyData&&s!==i||d.set(f,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}if(!s||!i){s||d.set(f,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}return s===i&&(s={lazyData:s.lazyData,rsc:s.rsc,prefetchRsc:s.prefetchRsc,head:s.head,prefetchHead:s.prefetchHead,parallelRoutes:new Map(s.parallelRoutes),lazyDataResolved:s.lazyDataResolved,loading:s.loading},d.set(f,s)),e(s,i,r.slice(2))}}});let a=l(78505);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5410:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var l in t)Object.defineProperty(e,l,{enumerable:!0,get:t[l]})}(t,{computeChangedPath:function(){return d},extractPathFromFlightRouterState:function(){return c}});let a=l(91182),r=l(84541),n=l(76015),u=e=>"/"===e[0]?e.slice(1):e,o=e=>"string"==typeof e?"children"===e?"":e:e[1];function f(e){return e.reduce((e,t)=>""===(t=u(t))||(0,r.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let l=Array.isArray(e[0])?e[0][1]:e[0];if(l===r.DEFAULT_SEGMENT_KEY||a.INTERCEPTION_ROUTE_MARKERS.some(e=>l.startsWith(e)))return;if(l.startsWith(r.PAGE_SEGMENT_KEY))return"";let n=[o(l)],u=null!=(t=e[1])?t:{},d=u.children?c(u.children):void 0;if(void 0!==d)n.push(d);else for(let[e,t]of Object.entries(u)){if("children"===e)continue;let l=c(t);void 0!==l&&n.push(l)}return f(n)}function d(e,t){let l=function e(t,l){let[r,u]=t,[f,d]=l,i=o(r),s=o(f);if(a.INTERCEPTION_ROUTE_MARKERS.some(e=>i.startsWith(e)||s.startsWith(e)))return"";if(!(0,n.matchSegment)(r,f)){var p;return null!=(p=c(l))?p:""}for(let t in u)if(d[t]){let l=e(u[t],d[t]);if(null!==l)return o(f)+"/"+l}return null}(e,t);return null==l||"/"===l?l:f(l.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33456:function(e,t){"use strict";function l(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return l}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82952:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInitialRouterState",{enumerable:!0,get:function(){return c}});let a=l(33456),r=l(27420),n=l(5410),u=l(60305),o=l(24673),f=l(50232);function c(e){var t;let{buildId:l,initialTree:c,initialSeedData:d,urlParts:i,initialParallelRoutes:s,location:p,initialHead:h,couldBeIntercepted:y}=e,_=i.join("/"),g=!p,R={lazyData:null,rsc:d[2],prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:g?new Map:s,lazyDataResolved:!1,loading:d[3]},b=p?(0,a.createHrefFromUrl)(p):_;(0,f.addRefreshMarkerToActiveParallelSegments)(c,b);let v=new Map;(null===s||0===s.size)&&(0,r.fillLazyItemsTillLeafWithHead)(R,void 0,c,d,h);let j={buildId:l,tree:c,cache:R,prefetchCache:v,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:b,nextUrl:null!=(t=(0,n.extractPathFromFlightRouterState)(c)||(null==p?void 0:p.pathname))?t:null};if(p){let e=new URL(""+p.pathname+p.search,p.origin),t=[["",c,null,null]];(0,u.createPrefetchCacheEntryForInitialLoad)({url:e,kind:o.PrefetchKind.AUTO,data:[t,void 0,!1,y],tree:j.tree,prefetchCache:j.prefetchCache,nextUrl:j.nextUrl})}return j}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78505:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return r}});let a=l(84541);function r(e,t){return(void 0===t&&(t=!1),Array.isArray(e))?e[0]+"|"+e[1]+"|"+e[2]:t&&e.startsWith(a.PAGE_SEGMENT_KEY)?a.PAGE_SEGMENT_KEY:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44848:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fetchServerResponse",{enumerable:!0,get:function(){return d}});let a=l(6866),r=l(12846),n=l(83079),u=l(24673),o=l(37207),{createFromFetch:f}=l(6671);function c(e){return[(0,r.urlToUrlWithoutFlightMarker)(e).toString(),void 0,!1,!1]}async function d(e,t,l,d,i){let s={[a.RSC_HEADER]:"1",[a.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(t))};i===u.PrefetchKind.AUTO&&(s[a.NEXT_ROUTER_PREFETCH_HEADER]="1"),l&&(s[a.NEXT_URL]=l);let p=(0,o.hexHash)([s[a.NEXT_ROUTER_PREFETCH_HEADER]||"0",s[a.NEXT_ROUTER_STATE_TREE],s[a.NEXT_URL]].join(","));try{var h;let t=new URL(e);t.searchParams.set(a.NEXT_RSC_UNION_QUERY,p);let l=await fetch(t,{credentials:"same-origin",headers:s}),u=(0,r.urlToUrlWithoutFlightMarker)(l.url),o=l.redirected?u:void 0,i=l.headers.get("content-type")||"",y=!!l.headers.get(a.NEXT_DID_POSTPONE_HEADER),_=!!(null==(h=l.headers.get("vary"))?void 0:h.includes(a.NEXT_URL));if(i!==a.RSC_CONTENT_TYPE_HEADER||!l.ok)return e.hash&&(u.hash=e.hash),c(u.toString());let[g,R]=await f(Promise.resolve(l),{callServer:n.callServer});if(d!==g)return c(l.url);return[R,o,y,_]}catch(t){return console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",t),[e.toString(),void 0,!1,!1]}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92576:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return function e(t,l,u,o){let f=u.length<=5,[c,d]=u,i=(0,n.createRouterCacheKey)(d),s=l.parallelRoutes.get(c);if(!s)return;let p=t.parallelRoutes.get(c);p&&p!==s||(p=new Map(s),t.parallelRoutes.set(c,p));let h=s.get(i),y=p.get(i);if(f){if(!y||!y.lazyData||y===h){let e=u[3];y={lazyData:null,rsc:e[2],prefetchRsc:null,head:null,prefetchHead:null,loading:e[3],parallelRoutes:h?new Map(h.parallelRoutes):new Map,lazyDataResolved:!1},h&&(0,a.invalidateCacheByRouterState)(y,h,u[2]),(0,r.fillLazyItemsTillLeafWithHead)(y,h,u[2],e,u[4],o),p.set(i,y)}return}y&&h&&(y===h&&(y={lazyData:y.lazyData,rsc:y.rsc,prefetchRsc:y.prefetchRsc,head:y.head,prefetchHead:y.prefetchHead,parallelRoutes:new Map(y.parallelRoutes),lazyDataResolved:!1,loading:y.loading},p.set(i,y)),e(y,h,u.slice(2),o))}}});let a=l(94377),r=l(27420),n=l(78505);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},27420:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,l,n,u,o,f){if(0===Object.keys(n[1]).length){t.head=o;return}for(let c in n[1]){let d;let i=n[1][c],s=i[0],p=(0,a.createRouterCacheKey)(s),h=null!==u&&void 0!==u[1][c]?u[1][c]:null;if(l){let a=l.parallelRoutes.get(c);if(a){let l;let n=(null==f?void 0:f.kind)==="auto"&&f.status===r.PrefetchCacheEntryStatus.reusable,u=new Map(a),d=u.get(p);l=null!==h?{lazyData:null,rsc:h[2],prefetchRsc:null,head:null,prefetchHead:null,loading:h[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),lazyDataResolved:!1}:n&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),lazyDataResolved:d.lazyDataResolved,loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),lazyDataResolved:!1,loading:null},u.set(p,l),e(l,d,i,h||null,o,f),t.parallelRoutes.set(c,u);continue}}if(null!==h){let e=h[2],t=h[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};let y=t.parallelRoutes.get(c);y?y.set(p,d):t.parallelRoutes.set(c,new Map([[p,d]])),e(d,void 0,i,h,o,f)}}}});let a=l(78505),r=l(24673);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44510:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return n}});let a=l(5410);function r(e){return void 0!==e}function n(e,t){var l,n,u;let o=null==(n=t.shouldScroll)||n,f=e.nextUrl;if(r(t.patchedTree)){let l=(0,a.computeChangedPath)(e.tree,t.patchedTree);l?f=l:f||(f=e.canonicalUrl)}return{buildId:e.buildId,canonicalUrl:r(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:r(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:r(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:r(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!r(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:!!t.hashFragment&&e.canonicalUrl.split("#",1)[0]===(null==(l=t.canonicalUrl)?void 0:l.split("#",1)[0]),hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(u=null==t?void 0:t.scrollableSegments)?u:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:r(t.patchedTree)?t.patchedTree:e.tree,nextUrl:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77831:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return r}});let a=l(95967);function r(e,t,l){return(0,a.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77058:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,l,r){let n=r.length<=2,[u,o]=r,f=(0,a.createRouterCacheKey)(o),c=l.parallelRoutes.get(u);if(!c)return;let d=t.parallelRoutes.get(u);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(u,d)),n){d.delete(f);return}let i=c.get(f),s=d.get(f);s&&i&&(s===i&&(s={lazyData:s.lazyData,rsc:s.rsc,prefetchRsc:s.prefetchRsc,head:s.head,prefetchHead:s.prefetchHead,parallelRoutes:new Map(s.parallelRoutes),lazyDataResolved:s.lazyDataResolved},d.set(f,s)),e(s,i,r.slice(2)))}}});let a=l(78505);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94377:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return r}});let a=l(78505);function r(e,t,l){for(let r in l[1]){let n=l[1][r][0],u=(0,a.createRouterCacheKey)(n),o=t.parallelRoutes.get(r);if(o){let t=new Map(o);t.delete(u),e.parallelRoutes.set(r,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63237:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,l){let a=t[0],r=l[0];if(Array.isArray(a)&&Array.isArray(r)){if(a[0]!==r[0]||a[2]!==r[2])return!0}else if(a!==r)return!0;if(t[4])return!l[4];if(l[4])return!0;let n=Object.values(t[1])[0],u=Object.values(l[1])[0];return!n||!u||e(n,u)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25575:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"requestAsyncStorage",{enumerable:!0,get:function(){return a}});let a=(0,l(54832).createAsyncLocalStorage)();("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);