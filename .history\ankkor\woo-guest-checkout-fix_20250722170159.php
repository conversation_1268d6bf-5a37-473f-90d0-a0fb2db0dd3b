<?php
/**
 * Plugin Name: WooCommerce Guest Checkout Fix
 * Description: Fixes guest checkout redirection issues for headless WooCommerce
 * Version: 1.0.0
 * Author: Ankkor
 * Text Domain: woo-guest-checkout-fix
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class WooGuestCheckoutFix {
    public function __construct() {
        // Force guest checkout settings
        add_action('init', [$this, 'force_guest_checkout_settings']);

        // Disable checkout login reminders completely
        add_filter('woocommerce_checkout_registration_required', '__return_false', 999);
        add_filter('woocommerce_checkout_registration_enabled', '__return_false', 999);
        add_filter('woocommerce_checkout_login_required', '__return_false', 999);
        add_filter('woocommerce_checkout_login_enabled', '__return_false', 999);
        
        // Prevent redirection to my-account page
        add_filter('woocommerce_prevent_admin_access', [$this, 'disable_admin_access_prevention'], 999, 1);
        
        // Disable admin restrictions on checkout page
        add_action('woocommerce_checkout_init', [$this, 'prevent_admin_redirects'], 5);
        
        // Add REST API endpoints for guest checkout
        add_action('rest_api_init', [$this, 'register_rest_routes']);

        // Add headers to all REST API responses
        add_filter('rest_pre_serve_request', [$this, 'add_cors_headers'], 0, 4);

        // Add hidden inputs to checkout form to ensure guest checkout
        add_action('woocommerce_checkout_before_customer_details', [$this, 'add_guest_checkout_fields']);

        // Add notice if guest checkout is not enabled
        add_action('admin_notices', [$this, 'guest_checkout_notice']);
    }

    /**
     * Force enable guest checkout in WooCommerce settings
     */
    public function force_guest_checkout_settings() {
        update_option('woocommerce_enable_guest_checkout', 'yes');
        update_option('woocommerce_enable_checkout_login_reminder', 'no');
    }

    /**
     * Disable admin access prevention for checkout and API endpoints
     */
    public function disable_admin_access_prevention($prevent_access) {
        if (
            // If this is a REST API request
            (defined('REST_REQUEST') && REST_REQUEST) ||
            // Or if we're on the checkout page
            is_checkout() || 
            // Or if this is a headless request with our custom parameter
            isset($_GET['force_guest_checkout'])
        ) {
            return false;
        }
        
        return $prevent_access;
    }

    /**
     * Remove the action that prevents admin access on checkout pages
     */
    public function prevent_admin_redirects() {
        // Remove the WooCommerce action that prevents admin access
        remove_action('template_redirect', 'wc_prevent_admin_access');
    }

    /**
     * Add CORS headers to REST API responses
     */
    public function add_cors_headers($served, $result, $request, $server) {
        $origin = get_http_origin();
        
        // Allow requests from any origin for now
        // In production, you should restrict this to your frontend domains
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Headers: Authorization, Content-Type, X-WP-Nonce, X-WC-Store-API-Nonce');
        
        // Handle preflight OPTIONS requests
        if ('OPTIONS' === $_SERVER['REQUEST_METHOD']) {
            status_header(200);
            exit();
        }
        
        return $served;
    }

    /**
     * Register custom REST API endpoints
     */
    public function register_rest_routes() {
        // Endpoint to get a guest checkout URL
        register_rest_route('ankkor/v1', '/guest-checkout', [
            'methods' => 'GET,POST',
            'callback' => [$this, 'get_guest_checkout_url'],
            'permission_callback' => '__return_true',
        ]);

        // Endpoint to fix checkout session
        register_rest_route('ankkor/v1', '/fix-checkout', [
            'methods' => 'POST',
            'callback' => [$this, 'fix_checkout_session'],
            'permission_callback' => '__return_true',
        ]);
    }

    /**
     * Get a properly formatted guest checkout URL
     */
    public function get_guest_checkout_url($request) {
        // Build a checkout URL with all necessary parameters
        $checkout_url = wc_get_checkout_url();
        $checkout_url = add_query_arg([
            'guest_checkout' => 'yes',
            'checkout_woocommerce_checkout_login_reminder' => '0',
            'create_account' => '0',
            'skip_login' => '1',
            'force_guest_checkout' => '1'
        ], $checkout_url);

        return new WP_REST_Response([
            'success' => true,
            'checkout_url' => $checkout_url
        ], 200);
    }

    /**
     * Fix the checkout session to enable guest checkout
     */
    public function fix_checkout_session($request) {
        $params = $request->get_params();
        
        // Set session values to force guest checkout
        WC()->session->set('checkout_guest', true);
        WC()->session->set('checkout_create_account', false);
        
        // Force user to guest
        if (isset($params['force_guest_checkout']) && $params['force_guest_checkout']) {
            // Ensure user is treated as guest for checkout
            WC()->session->set('force_guest_checkout', true);
        }
        
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Checkout session updated for guest checkout'
        ], 200);
    }

    /**
     * Add hidden fields to checkout form to ensure guest checkout
     */
    public function add_guest_checkout_fields() {
        // Add hidden inputs to ensure guest checkout
        echo '<input type="hidden" name="checkout_guest" value="1" />';
        echo '<input type="hidden" name="createaccount" value="0" />';
        echo '<input type="hidden" name="guest_checkout" value="yes" />';
    }

    /**
     * Show admin notice if guest checkout is not enabled
     */
    public function guest_checkout_notice() {
        if (get_option('woocommerce_enable_guest_checkout') !== 'yes') {
            ?>
            <div class="notice notice-error">
                <p><strong>WooCommerce Guest Checkout Fix:</strong> Guest checkout is not enabled in WooCommerce settings. Please enable it in <a href="<?php echo admin_url('admin.php?page=wc-settings&tab=account'); ?>">WooCommerce > Settings > Accounts & Privacy</a>.</p>
            </div>
            <?php
        }
    }
}

// Initialize the plugin
new WooGuestCheckoutFix(); 