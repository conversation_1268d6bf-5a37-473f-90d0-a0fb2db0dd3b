(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7928],{96458:function(e,r,s){Promise.resolve().then(s.bind(s,25641))},25641:function(e,r,s){"use strict";s.d(r,{default:function(){return m}});var t=s(57437),c=s(2265),a=s(12381),n=s(82372),i=s(67111),l=s(77690),d=s(15863);let o=e=>{let{title:r,children:s}=e;return(0,t.jsxs)("div",{className:"mb-8 border rounded-md p-4",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:r}),s]})};var m=()=>{let[e,r]=(0,c.useState)([]),[s,m]=(0,c.useState)([]),[u,g]=(0,c.useState)(null),[h,x]=(0,c.useState)(null),[p,j]=(0,c.useState)({}),[N,v]=(0,c.useState)({}),b=(0,i.xS)(),y=(e,r)=>{j(s=>({...s,[e]:r}))},f=(e,r)=>{v(s=>({...s,[e]:r}))},E=async()=>{try{var e;y("products",!0);let s=await (0,n.Xp)();r(s.nodes||[]),f("products","Success! Fetched ".concat((null===(e=s.nodes)||void 0===e?void 0:e.length)||0," products"))}catch(e){console.error("Error fetching products:",e),f("products","Error: ".concat(e.message))}finally{y("products",!1)}},w=async()=>{try{var e;y("categories",!0);let r=await (0,n.CP)();m(r.nodes||[]),f("categories","Success! Fetched ".concat((null===(e=r.nodes)||void 0===e?void 0:e.length)||0," categories"))}catch(e){console.error("Error fetching categories:",e),f("categories","Error: ".concat(e.message))}finally{y("categories",!1)}},C=async()=>{if(!e.length){f("product","Error: No products available to test with");return}try{y("product",!0);let r=e[0].databaseId,s=await (0,n.wv)(r);g(s),f("product","Success! Fetched product: ".concat(s.name))}catch(e){console.error("Error fetching product:",e),f("product","Error: ".concat(e.message))}finally{y("product",!1)}},S=async()=>{if(!e.length){f("cart","Error: No products available to test with");return}try{var r,s;y("cart",!0);let t=e[0];await b.addToCart({productId:t.databaseId.toString(),name:t.name,price:t.price,quantity:1,image:{url:(null===(r=t.image)||void 0===r?void 0:r.sourceUrl)||"",altText:(null===(s=t.image)||void 0===s?void 0:s.altText)||t.name}}),f("cart","Success! Added ".concat(t.name," to cart"))}catch(e){console.error("Error adding to cart:",e),f("cart","Error: ".concat(e.message))}finally{y("cart",!1)}},U=async()=>{try{y("login",!0);let e=await (0,l.x4)("<EMAIL>","password123");e&&(x(e),f("login","Success! Logged in as ".concat(e.email)))}catch(e){console.error("Error logging in:",e),f("login","Error: ".concat(e.message))}finally{y("login",!1)}},k=async()=>{try{y("register",!0);let e="test".concat(Math.floor(1e4*Math.random()),"@example.com");await (0,l.z2)({email:e,firstName:"Test",lastName:"User",password:"password123",username:"testuser".concat(Math.floor(1e4*Math.random()))}),f("register","Success! Registered user: ".concat(e))}catch(e){console.error("Error registering:",e),f("register","Error: ".concat(e.message))}finally{y("register",!1)}},z=async()=>{try{y("currentUser",!0);let e=await (0,l.ts)();e?(x(e),f("currentUser","Success! Current user: ".concat(e.email))):f("currentUser","No user is currently logged in")}catch(e){console.error("Error getting current user:",e),f("currentUser","Error: ".concat(e.message))}finally{y("currentUser",!1)}};return(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Integration Test"}),(0,t.jsx)(o,{title:"Products",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(a.z,{onClick:E,disabled:p.products,children:[p.products&&(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Products"]}),N.products&&(0,t.jsx)("div",{className:"p-3 rounded-md ".concat(N.products.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:N.products}),e.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h3",{className:"font-medium mb-2",children:"First 5 Products:"}),(0,t.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:e.slice(0,5).map(e=>(0,t.jsxs)("li",{children:[e.name," - $",e.price]},e.id))})]})]})}),(0,t.jsx)(o,{title:"Categories",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(a.z,{onClick:w,disabled:p.categories,children:[p.categories&&(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Categories"]}),N.categories&&(0,t.jsx)("div",{className:"p-3 rounded-md ".concat(N.categories.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:N.categories}),s.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h3",{className:"font-medium mb-2",children:"Categories:"}),(0,t.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:s.map(e=>(0,t.jsx)("li",{children:e.name},e.id))})]})]})}),(0,t.jsx)(o,{title:"Single Product",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(a.z,{onClick:C,disabled:p.product||!e.length,children:[p.product&&(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Single Product"]}),N.product&&(0,t.jsx)("div",{className:"p-3 rounded-md ".concat(N.product.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:N.product}),u&&(0,t.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[(0,t.jsx)("h3",{className:"font-medium text-lg",children:u.name}),(0,t.jsxs)("p",{className:"text-gray-500 mt-1",children:["$",u.price]}),u.image&&(0,t.jsx)("div",{className:"mt-2 w-32 h-32 relative",children:(0,t.jsx)("img",{src:u.image.sourceUrl,alt:u.image.altText||u.name,className:"object-cover w-full h-full"})})]})]})}),(0,t.jsx)(o,{title:"Cart",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(a.z,{onClick:S,disabled:p.cart||!e.length,children:[p.cart&&(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Add to Cart"]}),N.cart&&(0,t.jsx)("div",{className:"p-3 rounded-md ".concat(N.cart.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:N.cart}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsxs)("h3",{className:"font-medium mb-2",children:["Cart Items: ",b.items.length]}),b.items.length>0&&(0,t.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:b.items.map(e=>(0,t.jsxs)("li",{children:[e.name," - Qty: ",e.quantity]},e.id))})]})]})}),(0,t.jsx)(o,{title:"Authentication",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsxs)(a.z,{onClick:U,disabled:p.login,children:[p.login&&(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Login"]}),(0,t.jsxs)(a.z,{onClick:k,disabled:p.register,variant:"outline",children:[p.register&&(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Register"]}),(0,t.jsxs)(a.z,{onClick:z,disabled:p.currentUser,variant:"secondary",children:[p.currentUser&&(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Get Current User"]})]}),N.login&&(0,t.jsx)("div",{className:"p-3 rounded-md ".concat(N.login.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:N.login}),N.register&&(0,t.jsx)("div",{className:"p-3 rounded-md ".concat(N.register.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:N.register}),N.currentUser&&(0,t.jsx)("div",{className:"p-3 rounded-md ".concat(N.currentUser.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:N.currentUser}),h&&(0,t.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Current User:"}),(0,t.jsxs)("p",{children:["Email: ",h.email]}),(0,t.jsxs)("p",{children:["Name: ",h.firstName," ",h.lastName]})]})]})})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,5717,8496,9429,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,6518,4595,3280,8790,1104,7158,6758,7044,5302,6628,5363,4754,1744],function(){return e(e.s=96458)}),_N_E=e.O()}]);