"use strict";(()=>{var e={};e.id=7536,e.ids=[7536],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},72254:e=>{e.exports=require("node:buffer")},6005:e=>{e.exports=require("node:crypto")},47261:e=>{e.exports=require("node:util")},29872:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>l,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d});var n=r(49303),s=r(88716),a=r(60670),c=r(42004),i=e([c]);c=(i.then?(await i)():i)[0];let u=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/woo-sync/route",pathname:"/api/woo-sync",filename:"route",bundlePath:"app/api/woo-sync/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\woo-sync\\route.ts",nextConfigOutput:"standalone",userland:c}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:g}=u,y="/api/woo-sync/route";function l(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}o()}catch(e){o(e)}})},42004:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{POST:()=>S});var n=r(87070),s=r(67653),a=r(19910),c=r(92861),i=r(79534),l=e([a]);a=(l.then?(await l)():l)[0];let y={PRODUCTS:3600,CATEGORIES:86400};async function u(e){try{let t=await e.json();if(t.token!==process.env.WOOCOMMERCE_REVALIDATION_SECRET)return n.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let r=t.type||"inventory";switch(console.log(`Starting WooCommerce ${r} sync at ${new Date().toISOString()}`),r){case"inventory":await p();break;case"all":await d();break;case"categories":await g();break;default:return n.NextResponse.json({success:!1,error:"Invalid sync type"},{status:400})}return n.NextResponse.json({success:!0,message:`Successfully completed WooCommerce ${r} sync`,timestamp:new Date().toISOString()})}catch(e){return console.error("Error during WooCommerce sync:",e),n.NextResponse.json({success:!1,error:"Sync operation failed"},{status:500})}}async function p(){console.log("Syncing WooCommerce inventory...");try{let e=await a.Dg(100);if(!e||0===e.length){console.log("No products found to sync inventory");return}console.log(`Found ${e.length} products to sync inventory`);let t=e.map(e=>({productId:e.databaseId.toString(),productSlug:e.slug}));for(let r of(await c.p_(t),e)){let e=`product:${r.slug}`,t=await i.U2(e);if(t){let o={...t,availableForSale:"IN_STOCK"===r.stockStatus||r.variations?.nodes?.some(e=>"IN_STOCK"===e.stockStatus),_lastInventoryUpdate:new Date().toISOString()};await i.t8(e,o,y.PRODUCTS)}}console.log(`Successfully updated inventory for ${e.length} products`)}catch(e){throw console.error("Error syncing WooCommerce inventory:",e),e}}async function d(){console.log("Syncing all WooCommerce products...");try{let e=await a.Dg(100);if(!e||0===e.length){console.log("No products found to sync");return}console.log(`Found ${e.length} products to sync`);let t=e.map(e=>({productId:e.databaseId.toString(),productSlug:e.slug}));for(let r of(await c.p_(t),e)){let e=a.Op(r);if(e){let t=`product:${e.handle}`;await i.t8(t,e,y.PRODUCTS)}}await i.IV("all_products"),await i.IV("featured_products"),console.log(`Successfully synced ${e.length} products`)}catch(e){throw console.error("Error syncing all WooCommerce products:",e),e}}async function g(){console.log("Syncing WooCommerce categories...");try{let e=await a.tG(50);if(!e||0===e.length){console.log("No categories found to sync");return}for(let t of(console.log(`Found ${e.length} categories to sync`),e)){let e=a.Zh(t);if(e){let t=`category:${e.handle}`;await i.t8(t,e,y.CATEGORIES)}}let t=e.map(a.Zh).filter(Boolean);await i.t8("all_categories",t,y.CATEGORIES),console.log(`Successfully synced ${e.length} categories`)}catch(e){throw console.error("Error syncing WooCommerce categories:",e),e}}let S=process.env.QSTASH_CURRENT_SIGNING_KEY?(0,s.uM)(u):u;o()}catch(e){o(e)}})},79534:(e,t,r)=>{r.d(t,{Fs:()=>l,IV:()=>i,Pc:()=>s,U2:()=>a,mJ:()=>n,t8:()=>c});let o=new(r(94868)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),n={SHORT:300,MEDIUM:3600,LONG:86400,WEEK:604800};function s(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function a(e){if(!s())return null;try{return await o.get(e)||null}catch(t){return console.error(`Redis get error for key ${e}:`,t),null}}async function c(e,t,r){if(!s())return!1;try{return r?await o.setex(e,r,t):await o.set(e,t),!0}catch(t){return console.error(`Redis set error for key ${e}:`,t),!1}}async function i(e){if(!s())return!1;try{return await o.del(e),!0}catch(t){return console.error(`Redis delete error for key ${e}:`,t),!1}}async function l(e,t,r=n.MEDIUM){if(!s())return await t();try{let n=await o.get(e);if(null!==n)return console.log(`Cache hit for key: ${e}`),JSON.parse(n);console.log(`Cache miss for key: ${e}, fetching fresh data`);let s=await t();return await o.setex(e,r,JSON.stringify(s)),s}catch(r){return console.error(`Redis cache error for key ${e}:`,r),await t()}}},67653:(e,t,r)=>{r.d(t,{uM:()=>n});var o=r(70926);function n(e,t){let r=t?.currentSigningKey??process.env.QSTASH_CURRENT_SIGNING_KEY;if(!r)throw Error("currentSigningKey is required, either in the config or as env variable QSTASH_CURRENT_SIGNING_KEY");let n=t?.nextSigningKey??process.env.QSTASH_NEXT_SIGNING_KEY;if(!n)throw Error("nextSigningKey is required, either in the config or as env variable QSTASH_NEXT_SIGNING_KEY");let s=new o.nk({currentSigningKey:r,nextSigningKey:n});return async(r,o)=>{let n=r.headers["upstash-signature"];if(!n){o.status(400),o.send("`Upstash-Signature` header is missing"),o.end();return}if("string"!=typeof n)throw TypeError("`Upstash-Signature` header is not a string");let a=[];for await(let e of r)a.push("string"==typeof e?Buffer.from(e):e);let c=Buffer.concat(a).toString("utf8");if(!await s.verify({signature:n,body:c,clockTolerance:t?.clockTolerance})){o.status(400),o.send("Invalid signature"),o.end();return}try{r.body="application/json"===r.headers["content-type"]?JSON.parse(c):c}catch{r.body=c}return e(r,o)}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972,4766,4868,926,9910],()=>r(29872));module.exports=o})();