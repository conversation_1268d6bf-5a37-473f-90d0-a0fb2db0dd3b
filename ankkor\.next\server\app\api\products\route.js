"use strict";(()=>{var e={};e.id=9684,e.ids=[9684],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},92691:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>u,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>l});var o=r(49303),s=r(88716),n=r(60670),i=r(27430),p=e([i]);i=(p.then?(await p)():p)[0];let c=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\products\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:g}=c,h="/api/products/route";function u(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:l})}a()}catch(e){a(e)}})},27430:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{GET:()=>i,dynamic:()=>p});var o=r(87070),s=r(19910),n=e([s]);s=(n.then?(await n)():n)[0];let p="force-dynamic";async function i(e){try{let t;let r=e.nextUrl.searchParams,a=r.get("handle"),n=r.get("query"),i=r.get("collection"),p=r.get("category"),u=r.get("tag"),c=r.get("sort"),d=r.get("limit")?parseInt(r.get("limit")):20,l=r.get("page")?parseInt(r.get("page")):1;if(a)return t=await s.Co(a),o.NextResponse.json({product:t});return t=n?await s.s3(n,{limit:d,page:l}):i||p?await s.V8(i||p,{limit:d,page:l,sort:c}):u?await s.e_(u,{limit:d,page:l,sort:c}):await s.Dg({limit:d,page:l,sort:c}),o.NextResponse.json({products:t})}catch(e){return console.error("Error fetching products:",e),o.NextResponse.json({error:"Failed to fetch products"},{status:500})}}a()}catch(e){a(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,4766,4868,9910],()=>r(92691));module.exports=a})();