
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WooCommerce Guest Checkout Test</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    h1 { color: #7f54b3; }
    .test-group { margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; border-radius: 4px; }
    h2 { margin-top: 0; color: #2c3e50; }
    button { background-color: #7f54b3; color: white; border: none; padding: 10px 15px; margin: 5px 0; cursor: pointer; border-radius: 4px; }
    button:hover { background-color: #6b4a99; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    .note { background-color: #fffbea; padding: 10px; border-left: 4px solid #f0c674; margin-top: 20px; }
    .result { margin-top: 10px; padding: 10px; border-radius: 4px; }
    .success { background-color: #e6ffed; border-left: 4px solid #28a745; }
    .error { background-color: #ffeef0; border-left: 4px solid #dc3545; }
    .warning { background-color: #fff5e6; border-left: 4px solid #f0ad4e; }
    .cors-info { background-color: #e6f7ff; border-left: 4px solid #1890ff; padding: 10px; margin-top: 10px; }
    #results { margin-top: 20px; }
  </style>
</head>
<body>
  <h1>WooCommerce Guest Checkout Test</h1>
  <p>This page helps you test different approaches for guest checkout with proper CORS handling. Click each button to try a different method.</p>
  
  <div class="test-group">
    <h2>Test 1: Direct Guest Checkout URL</h2>
    <button onclick="window.open('https://deepskyblue-penguin-370791.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1')">
      Test Guest Checkout Parameters
    </button>
    <p>This test uses URL parameters to force guest checkout.</p>
  </div>

  <div class="test-group">
    <h2>Test 2: Custom API Endpoints</h2>
    <button onclick="testGuestCheckoutAPI()">
      Test /guest-checkout API
    </button>
    <button onclick="testFixCheckout()">
      Test /fix-checkout API
    </button>
    <div id="api-result" class="result"></div>
    <p>These tests check if the custom API endpoints are working with proper CORS handling.</p>
  </div>

  <div class="test-group">
    <h2>Test 3: GraphQL API with CORS</h2>
    <button onclick="testGraphQL()">
      Test GraphQL API
    </button>
    <div id="graphql-result" class="result"></div>
    <p>This test checks if the GraphQL API is properly configured for CORS.</p>
  </div>

  <div class="test-group">
    <h2>Test 4: Add Product & Checkout</h2>
    <button onclick="testAddToCart()">
      Add Product to Cart
    </button>
    <button onclick="window.open('https://deepskyblue-penguin-370791.hostingersite.com/shop')">
      Go to Shop
    </button>
    <div id="cart-result" class="result"></div>
    <p>Add a product to cart and proceed to checkout.</p>
  </div>

  <div class="note">
    <strong>Important:</strong> For best testing, use an incognito/private browser window to ensure you're not already logged in.
  </div>

  <div class="test-group">
    <h2>CORS Headers Information</h2>
    <div class="cors-info">
      <p>For proper CORS handling, the following headers should be present in API responses:</p>
      <ul>
        <li><code>Access-Control-Allow-Origin</code>: Should match your frontend origin</li>
        <li><code>Access-Control-Allow-Credentials</code>: Should be <code>true</code></li>
        <li><code>Access-Control-Allow-Methods</code>: Should include <code>GET, POST, OPTIONS</code></li>
        <li><code>Access-Control-Allow-Headers</code>: Should include necessary headers</li>
      </ul>
    </div>
  </div>

  <div id="results"></div>

  <div class="test-group">
    <h2>Troubleshooting</h2>
    <p>If you're still having issues:</p>
    <ol>
      <li>Make sure the WooCommerce Guest Checkout Fix plugin is properly activated</li>
      <li>Clear all caches (WordPress, browser, CDN)</li>
      <li>Check WooCommerce settings at https://deepskyblue-penguin-370791.hostingersite.com/wp-admin/admin.php?page=wc-settings&tab=account</li>
      <li>Check the browser console for any errors</li>
      <li>Verify GraphQL settings at https://deepskyblue-penguin-370791.hostingersite.com/wp-admin/admin.php?page=graphql-settings</li>
    </ol>
  </div>

  <script>
    // Helper function to display results
    function displayResult(elementId, success, message, headers = null) {
      const resultElement = document.getElementById(elementId);
      resultElement.className = success ? 'result success' : 'result error';
      
      let content = `<strong>${success ? 'Success' : 'Error'}:</strong> ${message}`;
      
      // Add headers info if available
      if (headers) {
        content += '<div class="cors-info"><strong>Response Headers:</strong><ul>';
        const relevantHeaders = [
          'access-control-allow-origin', 
          'access-control-allow-credentials', 
          'access-control-allow-methods',
          'access-control-allow-headers'
        ];
        
        relevantHeaders.forEach(header => {
          const value = headers.get(header);
          content += `<li><code>${header}</code>: ${value || 'Not set'}</li>`;
        });
        
        content += '</ul></div>';
      }
      
      resultElement.innerHTML = content;
      
      // Also add to the results log
      const resultsLog = document.getElementById('results');
      const timestamp = new Date().toLocaleTimeString();
      resultsLog.innerHTML += `<div class="${success ? 'success' : 'error'}">
        <strong>[${timestamp}] ${elementId}:</strong> ${message}
      </div>`;
    }

    // Test the guest checkout API endpoint
    async function testGuestCheckoutAPI() {
      try {
        const response = await fetch('https://deepskyblue-penguin-370791.hostingersite.com/wp-json/ankkor/v1/guest-checkout', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });
        
        const data = await response.json();
        
        if (data.success && data.checkout_url) {
          displayResult('api-result', true, `Successfully retrieved checkout URL: ${data.checkout_url}`, response.headers);
        } else {
          displayResult('api-result', false, 'API request succeeded but returned invalid data', response.headers);
        }
      } catch (error) {
        displayResult('api-result', false, `Error: ${error.message}`);
      }
    }

    // Test the fix-checkout API endpoint
    async function testFixCheckout() {
      try {
        const response = await fetch('https://deepskyblue-penguin-370791.hostingersite.com/wp-json/ankkor/v1/fix-checkout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ force_guest_checkout: true })
        });
        
        const data = await response.json();
        
        if (data.success) {
          displayResult('api-result', true, 'Successfully fixed checkout session for guest checkout', response.headers);
          
          // Redirect to checkout with guest parameters
          setTimeout(() => {
            window.open('https://deepskyblue-penguin-370791.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1');
          }, 1000);
        } else {
          displayResult('api-result', false, 'API request succeeded but returned invalid data', response.headers);
        }
      } catch (error) {
        displayResult('api-result', false, `Error: ${error.message}`);
      }
    }

    // Test GraphQL API with CORS
    async function testGraphQL() {
      try {
        // Simple GraphQL query
        const query = `
          query {
            generalSettings {
              title
              url
            }
          }
        `;
        
        const response = await fetch('https://deepskyblue-penguin-370791.hostingersite.com/graphql', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ query })
        });
        
        const data = await response.json();
        
        if (data.data && data.data.generalSettings) {
          displayResult(
            'graphql-result', 
            true, 
            `Successfully queried GraphQL API. Site title: "${data.data.generalSettings.title}"`, 
            response.headers
          );
        } else {
          displayResult('graphql-result', false, 'GraphQL request succeeded but returned invalid data', response.headers);
        }
      } catch (error) {
        displayResult('graphql-result', false, `Error: ${error.message}`);
      }
    }

    // Test adding a product to cart
    async function testAddToCart() {
      try {
        // First get a product ID
        const productsResponse = await fetch('https://deepskyblue-penguin-370791.hostingersite.com/wp-json/wc/store/v1/products?per_page=1', {
          method: 'GET',
          credentials: 'include'
        });
        
        const products = await productsResponse.json();
        
        if (!products || products.length === 0) {
          displayResult('cart-result', false, 'No products found');
          return;
        }
        
        const product = products[0];
        
        // Add product to cart
        const cartResponse = await fetch('https://deepskyblue-penguin-370791.hostingersite.com/wp-json/wc/store/v1/cart/add-item', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            id: product.id,
            quantity: 1
          })
        });
        
        const cartData = await cartResponse.json();
        
        if (cartData && !cartData.error) {
          displayResult(
            'cart-result', 
            true, 
            `Successfully added "${product.name}" to cart. You can now proceed to checkout.`, 
            cartResponse.headers
          );
          
          // Add checkout button
          document.getElementById('cart-result').innerHTML += `
            <button onclick="window.open('https://deepskyblue-penguin-370791.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1')" style="margin-top: 10px;">
              Proceed to Checkout
            </button>
          `;
        } else {
          displayResult('cart-result', false, `Failed to add product to cart: ${cartData.error || 'Unknown error'}`, cartResponse.headers);
        }
      } catch (error) {
        displayResult('cart-result', false, `Error: ${error.message}`);
      }
    }
  </script>
</body>
</html>
  