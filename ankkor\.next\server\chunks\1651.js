exports.id=1651,exports.ids=[1651],exports.modules={13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},13619:(e,t,r)=>{Promise.resolve().then(r.bind(r,933)),Promise.resolve().then(r.bind(r,46618)),Promise.resolve().then(r.bind(r,77321)),Promise.resolve().then(r.bind(r,59616)),Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,30292)),Promise.resolve().then(r.bind(r,2861)),Promise.resolve().then(r.bind(r,75367)),Promise.resolve().then(r.bind(r,32273))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(10326);r(17577);var o=r(33265);let s=()=>a.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,a.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),i=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>a.jsx(s,{})});function n(){return a.jsx("div",{className:"container mx-auto py-20",children:a.jsx(i,{})})}},59616:(e,t,r)=>{"use strict";function a({launchingState:e}){return null}r.r(t),r.d(t,{default:()=>a}),r(17577),r(30292)},77321:(e,t,r)=>{"use strict";r.d(t,{default:()=>c,j:()=>n});var a=r(10326),o=r(17577),s=r(86806);let i=(0,o.createContext)(void 0),n=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},c=({children:e})=>{let t=(0,s.rY)(),[r,n]=(0,o.useState)(!1),c={openCart:()=>n(!0),closeCart:()=>n(!1),toggleCart:()=>n(e=>!e),isOpen:r,itemCount:t.itemCount};return a.jsx(i.Provider,{value:c,children:e})}},68897:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{CustomerProvider:()=>g,O:()=>h});var o=r(10326),s=r(17577),i=r(35047),n=r(61296),c=r(42357),l=r(75367),d=r(54337),u=r(99274),m=e([n,c,d,u]);[n,c,d,u]=m.then?(await m)():m;let p=(0,s.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),h=()=>(0,s.useContext)(p);function g({children:e}){let[t,r]=(0,s.useState)(null),[a,m]=(0,s.useState)(!0),[g,h]=(0,s.useState)(null),[y,f]=(0,s.useState)(null),v=(0,i.useRouter)(),{addToast:w}=(0,l.p)();(0,u.j)();let k=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,x=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let t=await e.json();if(console.log("CustomerProvider: Auth API result:",t),!t.success||!t.customer)return f(null),{success:!1,message:t.message||"Not authenticated"};{let e=t.token;return console.log("CustomerProvider: Token from API response:",!!e),f(e||null),{success:!0,customer:t.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),f(null),{success:!1,message:"Network error"}}},b=async()=>{try{let e=await x();if(e.success){let t={...e.customer,token:e.token};r(k(t)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),r(null),f(null)}catch(e){console.error("Error refreshing customer data:",e),r(null),f(null)}},I=e=>{if(!e)return"An unknown error occurred";let t="string"==typeof e?e:e.message||JSON.stringify(e);return t.includes("Unidentified customer")?"The email or password you entered is incorrect. Please try again.":t.includes("already associated")?"An account with this email already exists. Please sign in instead.":t.includes("password")&&t.includes("too short")?"Your password must be at least 8 characters. Please try again.":t.includes("token")&&(t.includes("expired")||t.includes("invalid"))?"Your login session has expired. Please sign in again.":t.includes("network")||t.includes("failed to fetch")?"Network connection issue. Please check your internet connection and try again.":t},C=async e=>{m(!0),h(null);try{let t=await (0,n.x4)(e.email,e.password);if(!t||!t.success||!t.user)throw Error("Login failed: No user data returned");let a={id:t.user.id,databaseId:t.user.databaseId,email:t.user.email,firstName:t.user.firstName,lastName:t.user.lastName,token:t.token};r(k(a));let o=t.token;console.log("Login successful, token from API:",!!o),f(o||null);let s=d.xS.getState();try{await s.clearCart(),await s.initializeCart()}catch(e){console.error("Error initializing cart after login:",e)}w(`Welcome back, ${a?.firstName||"there"}!`,"success"),v.push("/")}catch(t){let e=I(t);throw h(e),w(e,"error"),t}finally{m(!1)}},P=async e=>{m(!0),h(null);try{let t=await (0,n.z2)(e.email,e.firstName,e.lastName,e.password);if(!t||!t.success||!t.customer)throw Error("Registration failed: No customer data returned");let a={...t.customer,token:t.token};r(k(a));let o=t.token;console.log("Registration successful, token from API:",!!o),f(o||null);let s=d.xS.getState();try{await s.clearCart(),await s.initializeCart()}catch(e){console.error("Error initializing cart after registration:",e)}w(`Welcome to Ankkor, ${t.customer?.firstName}!`,"success"),v.push("/")}catch(t){let e=I(t);throw h(e),w(e,"error"),t}finally{m(!1)}},N=async e=>{m(!0),h(null);try{let t=await (0,c.lG)(e);if(!t||!t.customer)throw Error("Profile update failed: No customer data returned");return r(k(t.customer)),w("Your profile has been updated successfully","success"),t}catch(t){let e=I(t);throw h(e),w(e,"error"),t}finally{m(!1)}};return o.jsx(p.Provider,{value:{customer:t,isLoading:a,isAuthenticated:!!t,token:y,login:C,register:P,logout:()=>{(0,n.kS)(),r(null),f(null),console.log("Logout completed, token cleared"),d.xS.getState().clearCart().catch(e=>{console.error("Error clearing cart during logout:",e)}),w("You have been signed out successfully","info"),v.push("/"),v.refresh()},updateProfile:N,error:g,refreshCustomer:b},children:e})}a()}catch(e){a(e)}})},30292:(e,t,r)=>{"use strict";r.d(t,{Gd:()=>n,default:()=>l});var a=r(10326),o=r(17577),s=r(60114),i=r(85251);let n=(0,s.Ue)()((0,i.tJ)(e=>({isLaunchingSoon:"true"===process.env.NEXT_PUBLIC_LAUNCHING_SOON,setIsLaunchingSoon:e=>{console.warn("Changing launch state is disabled in production.")}}),{name:"ankkor-launch-state"})),c=(0,o.createContext)(void 0),l=({children:e})=>{let t=n(),[r,s]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{s(!0);{let e="true"===process.env.NEXT_PUBLIC_LAUNCHING_SOON;t.isLaunchingSoon!==e&&n.setState({isLaunchingSoon:e})}},[t]),a.jsx(c.Provider,{value:t,children:r?e:null})}},2861:(e,t,r)=>{"use strict";r.d(t,{default:()=>h,r:()=>u});var a=r(10326),o=r(17577),s=r(35047),i=r(86462),n=r(92148);let c=({size:e="md",variant:t="thread",className:r=""})=>{let o={sm:{container:"w-16 h-16",text:"text-xs"},md:{container:"w-24 h-24",text:"text-sm"},lg:{container:"w-32 h-32",text:"text-base"}};return"thread"===t?(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[(0,a.jsxs)("div",{className:`relative ${o[e].container}`,children:[a.jsx(n.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27",borderRightColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1.5,repeat:1/0,ease:"linear"}}),a.jsx(n.E.div,{className:"absolute inset-2 rounded-full border-2 border-[#e5e2d9]",style:{borderBottomColor:"#8a8778",borderLeftColor:"#8a8778"},animate:{rotate:-360},transition:{duration:2,repeat:1/0,ease:"linear"}}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx("div",{className:"w-2 h-2 rounded-full bg-[#2c2c27]"})})]}),a.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${o[e].text}`,children:"Loading Collection"})]}):"fabric"===t?(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[(0,a.jsxs)("div",{className:`relative ${o[e].container} flex items-center justify-center`,children:[a.jsx(n.E.div,{className:"absolute w-1/3 h-1/3 bg-[#e5e2d9]",animate:{rotate:360,scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),a.jsx(n.E.div,{className:"absolute w-1/3 h-1/3 bg-[#8a8778]",animate:{rotate:-360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.3}}),a.jsx(n.E.div,{className:"absolute w-1/3 h-1/3 bg-[#2c2c27]",animate:{rotate:360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.6}})]}),a.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${o[e].text}`,children:"Preparing Your Style"})]}):"button"===t?(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[a.jsx("div",{className:`relative ${o[e].container} flex items-center justify-center`,children:a.jsx("div",{className:"relative flex",children:[0,1,2,3].map(e=>a.jsx(n.E.div,{className:"w-3 h-3 mx-1 rounded-full bg-[#2c2c27] border border-[#8a8778]",animate:{y:[0,-10,0],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,ease:"easeInOut",delay:.2*e}},e))})}),a.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${o[e].text}`,children:"Tailoring Experience"})]}):(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[a.jsx("div",{className:`relative ${o[e].container}`,children:a.jsx(n.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}),a.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${o[e].text}`,children:"Loading"})]})},l=({isLoading:e,variant:t="thread"})=>a.jsx(i.M,{children:e&&a.jsx(n.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"fixed inset-0 z-[200] flex items-center justify-center bg-[#f8f8f5]/90 backdrop-blur-sm",children:a.jsx(c,{variant:t,size:"lg"})})}),d=(0,o.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),u=()=>(0,o.useContext)(d),m={"/collection":"fabric","/collection/shirts":"fabric","/collection/polos":"fabric","/product":"thread","/about":"button","/customer-service":"button","/account":"thread","/wishlist":"thread"},g=({setIsLoading:e,setVariant:t})=>{let a=(0,s.usePathname)(),{useSearchParams:i}=r(35047),n=i();return(0,o.useEffect)(()=>{e(!0),t(m["/"+a.split("/")[1]]||m[a]||"thread");let r=setTimeout(()=>{e(!1)},1200);return()=>clearTimeout(r)},[a,n,e,t]),null},p=()=>a.jsx("div",{className:"hidden",children:"Loading route..."}),h=({children:e})=>{let[t,r]=(0,o.useState)(!1),[s,i]=(0,o.useState)("thread");return(0,a.jsxs)(d.Provider,{value:{isLoading:t,setLoading:r,variant:s,setVariant:i},children:[a.jsx(o.Suspense,{fallback:a.jsx(p,{}),children:a.jsx(g,{setIsLoading:r,setVariant:i})}),e,a.jsx(l,{isLoading:t,variant:s})]})}},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>m,p:()=>g});var a=r(10326),o=r(17577),s=r(92148),i=r(86462),n=r(54659),c=r(87888),l=r(18019),d=r(94019);let u=(0,o.createContext)(void 0);function m({children:e}){let[t,r]=(0,o.useState)([]);return(0,a.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",a=3e3)=>{let o=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:o,message:e,type:t,duration:a}])},removeToast:e=>{r(t=>t.filter(t=>t.id!==e))}},children:[e,a.jsx(h,{})]})}function g(){let e=(0,o.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function p({toast:e,onRemove:t}){return(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[a.jsx(()=>{switch(e.type){case"success":return a.jsx(n.Z,{className:"h-5 w-5"});case"error":return a.jsx(c.Z,{className:"h-5 w-5"});default:return a.jsx(l.Z,{className:"h-5 w-5"})}},{}),a.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),a.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:a.jsx(d.Z,{className:"h-4 w-4"})})]})}function h(){let{toasts:e,removeToast:t}=g();return a.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:a.jsx(i.M,{children:e.map(e=>a.jsx(p,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},32273:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(17577);let o=()=>{},s=()=>((0,a.useEffect)(()=>{o()},[]),null)},99274:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{j:()=>c});var o=r(17577),s=r(68897),i=r(96040),n=e([s,i]);function c(){let{isAuthenticated:e,customer:t}=(0,s.O)();(0,i.x)(),(0,i.Y)();let[r,a]=(0,o.useState)(!1);return(0,o.useRef)(e),(0,o.useRef)(t?.id||null),null}[s,i]=n.then?(await n)():n,a()}catch(e){a(e)}})},42357:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{lG:()=>i});var o=r(15725),s=e([o]);o=(s.then?(await s)():s)[0];async function i(e){try{let t=await fetch("/api/auth/update-profile",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),r=await t.json();if(!t.ok||!r.success)throw Error(r.message||"Profile update failed");return{customer:r.customer,accessToken:"token_managed_by_server",expiresAt:new Date(Date.now()+864e5).toISOString()}}catch(e){throw console.error("Error updating customer profile:",e),e}}a()}catch(e){a(e)}})},61296:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{kS:()=>l,ts:()=>d,x4:()=>n,z2:()=>c});var o=r(93690);r(18201);var s=e([o]);o=(s.then?(await s)():s)[0];let u="woo_auth_token",m="woo_refresh_token";(0,o.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,o.gql)`
  mutation RegisterUser($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      clientMutationId
      authToken
      refreshToken
      customer {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,o.gql)`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`,(0,o.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`,(0,o.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let g="https://maroon-lapwing-781450.hostingersite.com/graphql",p=g&&!g.startsWith("http")?`https://${g}`:g;function i(e){"undefined"!=typeof document&&(document.cookie=`${e}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;SameSite=Lax`)}async function n(e,t){try{let r=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e,password:t}),credentials:"include"});if(!r.ok){let e=await r.json();throw Error(e.message||"Login failed")}let a=await r.json();if(a.success&&a.user)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Login successful, user data received"),{success:!0,user:a.user,token:a.token};throw console.error("Login response missing user data"),Error("Login failed: Invalid response from server")}catch(e){return console.error("Login error:",e),{success:!1,message:e.message||"Login failed"}}}async function c(e,t,r,a){try{let o=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e,firstName:t,lastName:r,password:a}),credentials:"include"});if(!o.ok){let e=await o.json();throw Error(e.message||"Registration failed")}let s=await o.json();if(s.success&&s.customer)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Registration successful, user data received"),{success:!0,customer:s.customer,token:s.token};throw console.error("Registration response missing customer data"),Error("Registration failed: Invalid response from server")}catch(e){return console.error("Registration error:",e),{success:!1,message:e.message||"Registration failed"}}}async function l(){try{return await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"logout"}),credentials:"include"}),"undefined"!=typeof localStorage&&localStorage.removeItem("auth_session_started"),i(u),i(m),console.log("Logout successful, session cleared"),{success:!0}}catch(e){return console.error("Logout error:",e),"undefined"!=typeof localStorage&&localStorage.removeItem("auth_session_started"),i(u),i(m),{success:!0}}}async function d(){try{let e=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===e.status)return null;let t=await e.json();if(!e.ok||!t.success)return null;return t.user}catch(e){return console.error("Get user error:",e),null}}new o.GraphQLClient(p,{headers:{"Content-Type":"application/json"}}),a()}catch(e){a(e)}})},86806:(e,t,r)=>{"use strict";r.d(t,{rY:()=>n});var a=r(60114),o=r(85251);let s=()=>Math.random().toString(36).substring(2,15),i=async(e,t,r)=>{try{let a=await fetch(`/api/products/${e}/stock${r?`?variation_id=${r}`:""}`);if(!a.ok)return{available:!1,message:"Unable to verify stock availability"};let o=await a.json();if("IN_STOCK"!==o.stockStatus&&"instock"!==o.stockStatus)return{available:!1,message:"This product is currently out of stock",stockStatus:o.stockStatus};if(null!==o.stockQuantity&&o.stockQuantity<t)return{available:!1,message:`Only ${o.stockQuantity} items available in stock`,stockQuantity:o.stockQuantity,stockStatus:o.stockStatus};return{available:!0,stockQuantity:o.stockQuantity,stockStatus:o.stockStatus}}catch(e){return console.error("Stock validation error:",e),{available:!0,message:"Stock validation temporarily unavailable"}}},n=(0,a.Ue)()((0,o.tJ)((e,t)=>({items:[],itemCount:0,isLoading:!1,error:null,addToCart:async r=>{e({isLoading:!0,error:null});try{let a=await i(r.productId,r.quantity,r.variationId);if(!a.available)throw Error(a.message||"Product is out of stock");let o=t().items,n=r.price;"string"==typeof n&&(n=n.replace(/[₹$€£]/g,"").trim().replace(/,/g,""));let c={...r,price:n},l=o.findIndex(e=>e.productId===c.productId&&e.variationId===c.variationId);if(-1!==l){let t=[...o];t[l].quantity+=c.quantity,e({items:t,itemCount:t.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}else{let t={...c,id:s()};e({items:[...o,t],itemCount:o.reduce((e,t)=>e+t.quantity,0)+t.quantity,isLoading:!1})}console.log("Item added to cart successfully")}catch(t){console.error("Error adding item to cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:(r,a)=>{e({isLoading:!0,error:null});try{let o=t().items;if(a<=0)return t().removeCartItem(r);let s=o.map(e=>e.id===r?{...e,quantity:a}:e);e({items:s,itemCount:s.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}catch(t){console.error("Error updating cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:r=>{e({isLoading:!0,error:null});try{let a=t().items.filter(e=>e.id!==r);e({items:a,itemCount:a.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}catch(t){console.error("Error removing cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},clearCart:()=>{e({items:[],itemCount:0,isLoading:!1,error:null})},setError:t=>{e({error:t})},setIsLoading:t=>{e({isLoading:t})},subtotal:()=>{let e=t().items;try{let t=e.reduce((e,t)=>{let r=0;if("string"==typeof t.price){let e=t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");r=parseFloat(e)}else r=t.price;return isNaN(r)?(console.warn(`Invalid price for item ${t.id}: ${t.price}`),e):e+r*t.quantity},0);return isNaN(t)?0:t}catch(e){return console.error("Error calculating subtotal:",e),0}},total:()=>{let e=t().subtotal();return isNaN(e)?0:e},syncWithWooCommerce:async r=>{let{items:a}=t();if(0===a.length)throw Error("Cart is empty");try{if(console.log("Syncing cart with WooCommerce..."),console.log("Auth token provided:",!!r),e({isLoading:!0}),r){console.log("User is authenticated, using JWT-to-Cookie bridge");try{let t=await c(r,a);return e({isLoading:!1}),t}catch(e){console.error("JWT-to-Cookie bridge failed:",e),console.log("Falling back to guest checkout...")}}console.log("User is not authenticated, redirecting to WooCommerce checkout");let t="https://maroon-lapwing-781450.hostingersite.com/checkout/";return console.log("Guest checkout URL:",t),e({isLoading:!1}),t}catch(t){console.error("Error syncing cart with WooCommerce:",t),e({isLoading:!1});try{console.log("Attempting fallback method for cart sync...");let e="https://maroon-lapwing-781450.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1";return a.forEach((t,r)=>{0===r?e+=`&add-to-cart=${t.productId}&quantity=${t.quantity}`:e+=`&add-to-cart[${r}]=${t.productId}&quantity[${r}]=${t.quantity}`,t.variationId&&(e+=`&variation_id=${t.variationId}`)}),console.log("Fallback checkout URL:",e),e}catch(e){throw console.error("Fallback method failed:",e),Error("Failed to sync cart with WooCommerce. Please try again or contact support.")}}}}),{name:"ankkor-local-cart",version:1}));async function c(e,t){if(!e)throw Error("Authentication token is required");let r="https://maroon-lapwing-781450.hostingersite.com",a="https://maroon-lapwing-781450.hostingersite.com/checkout/";if(!r||!a)throw Error("WordPress or checkout URL not configured. Check your environment variables.");try{console.log("Creating WordPress session from JWT token..."),console.log("Using endpoint:",`${r}/wp-json/headless/v1/create-wp-session`),console.log("Token length:",e.length),console.log("Token preview:",e.substring(0,20)+"...");let t=await fetch(`${r}/wp-json/headless/v1/create-wp-session`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({token:e}),credentials:"include"});if(console.log("Response status:",t.status),console.log("Response headers:",Object.fromEntries(t.headers.entries())),!t.ok){let e=`HTTP ${t.status}: ${t.statusText}`;try{let r=await t.json();e=r.message||r.code||e,console.error("Error response data:",r)}catch(e){console.error("Could not parse error response:",e)}throw Error(`Failed to create WordPress session: ${e}`)}let o=await t.json();if(console.log("Response data:",o),!o.success)throw Error(o.message||"Failed to create WordPress session");return console.log("WordPress session created successfully"),console.log("Redirecting to checkout URL:",a),a}catch(e){if(console.error("Error creating WordPress session:",e),e instanceof TypeError&&e.message.includes("fetch"))throw Error("Network error: Could not connect to WordPress. Please check your internet connection.");throw Error(e instanceof Error?e.message:"Failed to prepare checkout")}}},96040:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Y:()=>u,x:()=>d});var o=r(60114),s=r(85251),i=r(15725),n=e([i]);i=(n.then?(await n)():n)[0];let c={getItem:e=>null,setItem:(e,t)=>{},removeItem:e=>{}},l=(e,t)=>{try{if(!t||!t.lines){console.error("Invalid normalized cart data",t);return}let r=t.lines.reduce((e,t)=>e+(t.quantity||0),0),a=t.lines.map(e=>({id:e.id,variantId:e.merchandise.id,productId:e.merchandise.product.id,title:e.merchandise.product.title,handle:e.merchandise.product.handle,image:e.merchandise.product.image?.url||"",price:e.merchandise.price,quantity:e.quantity,currencyCode:e.merchandise.currencyCode}));e({items:a,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,currencyCode:t.cost.totalAmount.currencyCode,itemCount:r,checkoutUrl:t.checkoutUrl,isLoading:!1})}catch(t){console.error("Error updating cart state:",t),e({items:[],subtotal:"0.00",total:"0.00",itemCount:0,isLoading:!1})}},d=(0,o.Ue)()((0,s.tJ)((e,t)=>({cartId:null,items:[],isOpen:!1,isLoading:!1,subtotal:"0.00",total:"0.00",currencyCode:"USD",itemCount:0,checkoutUrl:null,initializationInProgress:!1,initializationError:null,openCart:()=>e({isOpen:!0}),closeCart:()=>e({isOpen:!1}),toggleCart:()=>e(e=>({isOpen:!e.isOpen})),initCart:async()=>{let r=t();if(r.initializationInProgress)return console.log("Cart initialization already in progress, skipping"),null;e({isLoading:!0,initializationInProgress:!0,initializationError:null});try{if(r.cartId)try{let t=await (0,i.dv)();if(t)return e({isLoading:!1,initializationInProgress:!1}),t}catch(e){console.log("Existing cart validation failed, creating new cart")}let t=await (0,i.Bk)();if(t&&t.id)return e({cartId:t.id,checkoutUrl:t.checkoutUrl,isLoading:!1,initializationInProgress:!1}),console.log("Cart initialized with ID:",t.id),t;throw Error("Failed to create cart: No cart ID returned")}catch(t){return console.error("Failed to initialize cart:",t),e({isLoading:!1,initializationInProgress:!1,initializationError:t instanceof Error?t.message:"Unknown error initializing cart"}),null}},addItem:async r=>{e({isLoading:!0});try{if(!r.variantId)throw console.error("Cannot add item to cart: Missing variant ID",r),e({isLoading:!1}),Error("Missing variant ID for item");let a=t().cartId;if(!a){console.log("Cart not initialized, creating a new cart...");let e=await (0,i.Bk)();if(e&&e.id)console.log("New cart created:",e.id),a=e.id;else throw Error("Failed to initialize cart")}if(!a)throw Error("Failed to initialize cart: No cart ID available");console.log(`Adding item to cart: ${r.title} (${r.variantId}), quantity: ${r.quantity}`);try{let t=await (0,i.Xq)(a,[{merchandiseId:r.variantId,quantity:r.quantity||1}]);if(!t)throw Error("Failed to add item to cart: No cart returned");let o=(0,i.Id)(t);l(e,o),e({isOpen:!0}),console.log(`Item added to cart successfully. Cart now has ${o.lines.length} items.`)}catch(e){if(console.error("Shopify API error when adding to cart:",e),e instanceof Error)throw Error(`Failed to add item to cart: ${e.message}`);throw Error("Failed to add item to cart: Unknown API error")}}catch(t){throw console.error("Failed to add item to cart:",t),e({isLoading:!1}),t}},updateItem:async(r,a)=>{let o=t();e({isLoading:!0});try{if(!o.cartId)throw Error("Cart not initialized");if(console.log(`Updating item in cart: ${r}, new quantity: ${a}`),a<=0)return console.log(`Quantity is ${a}, removing item from cart`),t().removeItem(r);let s=await (0,i.xu)(o.cartId,[{id:r,quantity:a}]);if(!s)throw Error("Failed to update item: No cart returned");let n=(0,i.Id)(s);l(e,n),console.log(`Item updated successfully. Cart now has ${n.lines.length} items.`)}catch(t){throw console.error("Failed to update item in cart:",t),e({isLoading:!1}),t}},removeItem:async r=>{let a=t();e({isLoading:!0});try{if(!a.cartId)throw console.error("Cannot remove item: Cart not initialized"),Error("Cart not initialized");console.log(`Removing item from cart: ${r}`);let t=[...a.items],o=t.find(e=>e.id===r);o?console.log(`Removing "${o.title}" (${o.variantId}) from cart`):console.warn(`Item with ID ${r} not found in cart`);let s=await (0,i.h2)(a.cartId,[r]);if(!s)throw console.error("Failed to remove item: No cart returned from Shopify"),Error("Failed to remove item: No cart returned");let n=(0,i.Id)(s),c=n.lines.map(e=>({id:e.id,title:e.merchandise.product.title}));console.log("Cart before removal:",t.length,"items"),console.log("Cart after removal:",c.length,"items"),t.length===c.length&&console.warn("Item count did not change after removal operation"),l(e,n),console.log(`Item removed successfully. Cart now has ${n.lines.length} items.`)}catch(t){throw console.error("Failed to remove item from cart:",t),e({isLoading:!1}),t}},clearCart:async()=>{t(),e({isLoading:!0});try{console.log("Clearing cart and creating a new one");let t=await (0,i.Bk)();if(!t)throw Error("Failed to create new cart");e({cartId:t.id,items:[],subtotal:"0.00",total:"0.00",itemCount:0,checkoutUrl:t.checkoutUrl,isLoading:!1}),console.log("Cart cleared successfully. New cart ID:",t.id)}catch(t){throw console.error("Failed to clear cart:",t),e({isLoading:!1}),t}}}),{name:"ankkor-cart",storage:(0,s.FL)(()=>c),version:1,partialize:e=>({cartId:e.cartId,items:e.items,subtotal:e.subtotal,total:e.total,currencyCode:e.currencyCode,itemCount:e.itemCount,checkoutUrl:e.checkoutUrl})})),u=(0,o.Ue)()((0,s.tJ)((e,t)=>({items:[],isLoading:!1,addToWishlist:t=>{e(e=>e.items.some(e=>e.id===t.id)?e:{items:[...e.items,t]})},removeFromWishlist:t=>{e(e=>({items:e.items.filter(e=>e.id!==t)}))},clearWishlist:()=>{e({items:[]})},isInWishlist:e=>t().items.some(t=>t.id===e)}),{name:"ankkor-wishlist",storage:(0,s.FL)(()=>c),partialize:e=>({items:e.items})}));a()}catch(e){a(e)}})},53248:(e,t,r)=>{"use strict";new(r(78578)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""})},54337:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{xS:()=>c});var o=r(60114),s=r(85251),i=r(15725),n=e([i]);i=(n.then?(await n)():n)[0];let c=(0,o.Ue)()((0,s.tJ)((e,t)=>({id:null,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1,error:null,initializeCart:async()=>{e({isLoading:!0,error:null});try{let t=await i.dv();if(t){let r=i.Id(t);e({id:r.id,items:r.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1});return}let r=await i.Bk();if(r){let t=i.Id(r);e({id:t.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(t){console.error("Error initializing cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},addToCart:async r=>{e({isLoading:!0,error:null});try{t().id||await t().initializeCart();let a=[{productId:r.productId,quantity:r.quantity,variationId:r.variationId}],o=await i.Xq("",a);if(o){let t=i.Id(o);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to add item to cart")}catch(t){console.error("Error adding item to cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:async(r,a)=>{e({isLoading:!0,error:null});try{if(!t().id)throw Error("Cart not initialized");if(a<=0)return t().removeCartItem(r);let o=await i.xu([{key:r,quantity:a}]);if(o){let t=i.Id(o);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to update cart item")}catch(t){console.error("Error updating cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:async r=>{e({isLoading:!0,error:null});try{if(!t().id)throw Error("Cart not initialized");let a=await i.h2("",[r]);if(a){let t=i.Id(a);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to remove item from cart")}catch(t){console.error("Error removing cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},clearCart:async()=>{e({isLoading:!0,error:null});try{let t=await i.Bk();if(t){let r=i.Id(t);e({id:r.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(t){console.error("Error clearing cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},setError:t=>e({error:t}),setIsLoading:t=>e({isLoading:t})}),{name:"woo-cart-storage",version:1,partialize:e=>({id:e.id}),onRehydrateStorage:()=>e=>{e&&e.id&&e.initializeCart()}}));a()}catch(e){a(e)}})},15725:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Bk:()=>u,CP:()=>d,Dg:()=>l,Id:()=>y,ML:()=>f,Op:()=>h,Xp:()=>i,Xq:()=>g,dv:()=>m,h2:()=>p,mJ:()=>L,wv:()=>v,xu:()=>w});var o=r(93690);r(53248);var s=e([o]);o=(s.then?(await s)():s)[0];let k={storeUrl:"https://maroon-lapwing-781450.hostingersite.com",graphqlUrl:process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",apiVersion:"v1"},x=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",b=new o.GraphQLClient(x,{headers:{"Content-Type":"application/json",Accept:"application/json"}}),I=(0,o.gql)`
  fragment ProductFields on Product {
    id
    databaseId
    name
    slug
    description
    shortDescription
    type
    image {
      sourceUrl
      altText
    }
    galleryImages {
      nodes {
        sourceUrl
        altText
      }
    }
    ... on SimpleProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
    }
    ... on VariableProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      attributes {
        nodes {
          name
          options
        }
      }
    }
  }
`,C=(0,o.gql)`
  fragment VariableProductWithVariations on VariableProduct {
    attributes {
      nodes {
        name
        options
      }
    }
    variations {
      nodes {
        id
        databaseId
        name
        price
        regularPrice
        salePrice
        stockStatus
        stockQuantity
        attributes {
          nodes {
            name
            value
          }
        }
      }
    }
  }
`,P=(0,o.gql)`
  query GetProducts(
    $first: Int
    $after: String
    $where: RootQueryToProductConnectionWhereArgs
  ) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...ProductFields
        ... on VariableProduct {
          ...VariableProductWithVariations
        }
      }
    }
  }
  ${I}
  ${C}
`;async function i(e={}){try{return(await n(P,{first:e.first||12,after:e.after||null,where:e.where||{}},["products"],60)).products}catch(e){return console.error("Error fetching products:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function n(e,t={},r=[],a=60){try{{let o={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t}),next:{}};r&&r.length>0&&(o.next.tags=r),void 0!==a&&(o.next.revalidate=a);let s=await fetch(k.graphqlUrl,o);if(!s.ok)throw Error(`WooCommerce GraphQL API responded with status ${s.status}`);let{data:i,errors:n}=await s.json();if(n)throw console.error("GraphQL Errors:",n),Error(n[0].message);return i}}catch(e){throw console.error("Error fetching from WooCommerce:",e),e}}async function c({query:e,variables:t},r=3,a=1e3){let o=0,s=null;for(;o<r;)try{return await n(e,t,[],0)}catch(e){s=e,++o<r&&(console.log(`Retrying request (${o}/${r}) after ${a}ms`),await new Promise(e=>setTimeout(e,a)),a*=2)}throw console.error(`Failed after ${r} attempts:`,s),s}(0,o.gql)`
  query GetProductBySlug($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
    }
  }
  ${I}
  ${C}
`,(0,o.gql)`
  query GetProductBySlugWithTags($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
      productTags {
        nodes {
          id
          name
          slug
        }
      }
      productCategories {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${I}
  ${C}
`,(0,o.gql)`
  query GetCategories(
    $first: Int
    $after: String
    $where: RootQueryToProductCategoryConnectionWhereArgs
  ) {
    productCategories(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
      }
    }
  }
`;let N=(0,o.gql)`
  query GetAllProducts($first: Int = 20) {
    products(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        productCategories {
          nodes {
            id
            name
            slug
          }
        }
        ... on SimpleProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          variations {
            nodes {
              stockStatus
              stockQuantity
            }
          }
        }
        image {
          id
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            id
            sourceUrl
            altText
          }
        }
        ... on VariableProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
      }
    }
  }
`,S=((0,o.gql)`
  query GetProductsByCategory($slug: ID!, $first: Int = 20) {
    productCategory(id: $slug, idType: SLUG) {
      id
      name
      slug
      description
      products(first: $first) {
        nodes {
          id
          databaseId
          name
          slug
          ... on SimpleProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          ... on VariableProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          image {
            id
            sourceUrl
            altText
          }
        }
      }
    }
  }
`,(0,o.gql)`
  query GetAllCategories($first: Int = 20) {
    productCategories(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
        children {
          nodes {
            id
            name
            slug
          }
        }
      }
    }
  }
`);(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
    }
  }
`,(0,o.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
        nicename
        nickname
        username
      }
    }
  }
`;let E=(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`,$=(0,o.gql)`
  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {
    addToCart(
      input: {
        clientMutationId: "addToCart"
        productId: $productId
        variationId: $variationId
        quantity: $quantity
        extraData: $extraData
      }
    ) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
        contentsCount
      }
    }
  }
`,T=(0,o.gql)`
  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {
    removeItemsFromCart(input: { keys: $keys, all: $all }) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function l(e=20){try{let t=await c({query:N,variables:{first:e}});return t?.products?.nodes||[]}catch(e){return console.error("Error fetching all products:",e),[]}}async function d(e={}){try{let t=await c({query:S,variables:{first:e.first||20,after:e.after||null,where:e.where||{}}});return{nodes:t.productCategories.nodes,pageInfo:t.productCategories.pageInfo}}catch(e){return console.error("Error fetching categories:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function u(e=[]){try{if(0===e.length)return{contents:{nodes:[]},subtotal:"0",total:"0",totalTax:"0",isEmpty:!0,contentsCount:0};let t=e[0],r=await g("",[t]);if(e.length>1){for(let t=1;t<e.length;t++)await g("",[e[t]]);return await m()}return r}catch(e){throw console.error("Error creating cart:",e),e}}async function m(){try{let e=await c({query:E,variables:{}});return e?.cart||null}catch(e){return console.error("Error fetching cart:",e),null}}async function g(e,t){try{if(0===t.length)throw Error("No items provided to add to cart");let e=t[0],r={productId:parseInt(e.productId),quantity:e.quantity||1,variationId:e.variationId?parseInt(e.variationId):null,extraData:null};console.log("Adding to cart with variables:",r);let a=await c({query:$,variables:r});return console.log("Add to cart response:",a),a.addToCart.cart}catch(e){throw console.error("Error adding items to cart:",e),e}}async function p(e,t){try{let e=await c({query:T,variables:{keys:t,all:!1}});return e?.removeItemsFromCart?.cart||null}catch(e){throw console.error("Error removing items from cart:",e),e}}function h(e){if(!e)return null;let t=!!e.variations?.nodes?.length,r={minVariantPrice:{amount:e.price||"0",currencyCode:"INR"},maxVariantPrice:{amount:e.price||"0",currencyCode:"INR"}};if(t&&e.variations?.nodes?.length>0){let t=e.variations.nodes.map(e=>parseFloat(e.price||"0")).filter(e=>!isNaN(e));t.length>0&&(r={minVariantPrice:{amount:String(Math.min(...t)),currencyCode:"INR"},maxVariantPrice:{amount:String(Math.max(...t)),currencyCode:"INR"}})}let a=function(e){let t=[];return e.image&&t.push({url:e.image.sourceUrl,altText:e.image.altText||e.name||""}),e.galleryImages?.nodes?.length&&e.galleryImages.nodes.forEach(r=>{e.image&&r.sourceUrl===e.image.sourceUrl||t.push({url:r.sourceUrl,altText:r.altText||e.name||""})}),t}(e),o=e.variations?.nodes?.map(e=>({id:e.id,title:e.name,price:{amount:e.price||"0",currencyCode:"USD"},availableForSale:"IN_STOCK"===e.stockStatus,selectedOptions:e.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[],sku:e.sku||"",image:e.image?{url:e.image.sourceUrl,altText:e.image.altText||""}:null}))||[],s=e.attributes?.nodes?.map(e=>({name:e.name,values:e.options||[]}))||[],i=e.productCategories?.nodes?.map(e=>({handle:e.slug,title:e.name}))||[],n={};return e.metafields&&e.metafields.forEach(e=>{n[e.key]=e.value}),{id:e.id,handle:e.slug,title:e.name,description:e.description||"",descriptionHtml:e.description||"",priceRange:r,options:s,variants:o,images:a,collections:i,availableForSale:"OUT_OF_STOCK"!==e.stockStatus,metafields:n,_originalWooProduct:e}}(0,o.gql)`
  query GetShippingMethods {
    shippingMethods {
      nodes {
        id
        title
        description
        cost
      }
    }
  }
`,(0,o.gql)`
  query GetPaymentGateways {
    paymentGateways {
      nodes {
        id
        title
        description
        enabled
      }
    }
  }
`;let L=(e,t,r,a="")=>{if(!e||!e.metafields)return a;if(r){let o=`${r}:${t}`;return e.metafields[o]||a}return e.metafields[t]||a};function y(e){if(!e)return null;let t=e.contents?.nodes?.map(e=>{let t=e.product?.node,r=e.variation?.node;return{id:e.key,quantity:e.quantity,merchandise:{id:r?.id||t?.id,title:r?.name||t?.name,product:{id:t?.id,handle:t?.slug,title:t?.name,image:t?.image?{url:t?.image.sourceUrl,altText:t?.image.altText||""}:null},selectedOptions:r?.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[]},cost:{totalAmount:{amount:e.total||"0",currencyCode:"USD"}}}})||[],r=e.appliedCoupons?.nodes?.map(e=>({code:e.code,amount:e.discountAmount||"0"}))||[],a=t.reduce((e,t)=>e+t.quantity,0);return{id:e.id,checkoutUrl:"",totalQuantity:a,cost:{subtotalAmount:{amount:e.subtotal||"0",currencyCode:"USD"},totalAmount:{amount:e.total||"0",currencyCode:"USD"}},lines:t,discountCodes:r}}function f(e,t=!1){let r=`${k.storeUrl}/checkout`,a=e?`?cart=${e}`:"",o="";return t||(o=`${a?"&":"?"}guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`),`${r}${a}${o}`}async function v(e){let t=(0,o.gql)`
    query GetProduct($id: ID!) {
      product(id: $id, idType: DATABASE_ID) {
        id
        databaseId
        name
        slug
        description
        shortDescription
        price
        regularPrice
        salePrice
        onSale
        stockStatus
        stockQuantity
        image {
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            sourceUrl
            altText
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
          price
          regularPrice
          salePrice
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          attributes {
            nodes {
              name
              options
            }
          }
          variations {
            nodes {
              id
              databaseId
              name
              price
              regularPrice
              salePrice
              stockStatus
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
        }
      }
    }
  `;try{return(await b.request(t,{id:e})).product}catch(e){throw console.error("Error fetching product:",e),Error("Failed to fetch product")}}(0,o.gql)`
  mutation CreateCustomer($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
      }
      authToken
      refreshToken
    }
  }
`,(0,o.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
      customerUserErrors {
        field
        message
      }
    }
  }
`,(0,o.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      displayName
      username
      role
      date
      modified
      isPayingCustomer
      orderCount
      totalSpent
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders(first: 50) {
        nodes {
          id
          databaseId
          date
          status
          total
          subtotal
          totalTax
          shippingTotal
          discountTotal
          paymentMethodTitle
          customerNote
          billing {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
            email
            phone
          }
          shipping {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
          }
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                  slug
                  image {
                    sourceUrl
                    altText
                  }
                }
              }
              variation {
                node {
                  id
                  name
                  attributes {
                    nodes {
                      name
                      value
                    }
                  }
                }
              }
              quantity
              total
              subtotal
              totalTax
            }
          }
          shippingLines {
            nodes {
              methodTitle
              total
            }
          }
          feeLines {
            nodes {
              name
              total
            }
          }
          couponLines {
            nodes {
              code
              discount
            }
          }
        }
      }
      downloadableItems {
        nodes {
          name
          downloadId
          downloadsRemaining
          accessExpires
          product {
            node {
              id
              name
            }
          }
        }
      }
      metaData {
        key
        value
      }
    }
  }
`,(0,o.gql)`
  mutation CreateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation UpdateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation DeleteAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation SetDefaultAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let q=(0,o.gql)`
  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                name
                price
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function w(e){try{return(await c({query:q,variables:{input:{items:e}}})).updateItemQuantities.cart}catch(e){throw console.error("Error updating cart:",e),e}}a()}catch(e){a(e)}})},41783:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,metadata:()=>f});var a=r(19510),o=r(10527),s=r.n(o),i=r(36822),n=r.n(i);r(5023);var c=r(55782),l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#useCart`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#CartProvider`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#default`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#useLoading`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#LoadingProvider`);let u=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#default`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let m=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),g=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#useLaunchingSoonStore`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#useLaunchingSoon`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#LaunchingSoonProvider`);let p=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#default`),h=(0,c.default)(()=>r.e(9833).then(r.bind(r,29833)),{loadableGenerated:{modules:["app\\layout.tsx -> @/components/LaunchingStateServer"]},ssr:!0,loading:()=>null}),y=(0,c.default)(()=>r.e(203).then(r.bind(r,203)),{loadableGenerated:{modules:["app\\layout.tsx -> @/components/utils/LaunchUtilsInitializer"]},ssr:!1,loading:()=>null}),f={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function v({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:`${s().variable} ${n().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:a.jsx(g,{children:a.jsx(m,{children:a.jsx(d,{children:a.jsx(u,{children:(0,a.jsxs)(p,{children:[a.jsx(h,{}),a.jsx(y,{}),a.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})]})})})})})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};