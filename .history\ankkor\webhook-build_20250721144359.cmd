@echo off
echo Building Next.js app for webhook testing...

rem Set environment variables
set NEXT_SKIP_STATIC_GENERATION=true
set NEXT_USE_STATIC_PROPS=false
set NEXT_PUBLIC_SKIP_CLIENT_CACHE=true

rem Install cross-env if needed
call npm list cross-env || call npm install --save-dev cross-env

rem Clean .next directory
if exist .next (
  echo Cleaning .next directory...
  rmdir /s /q .next
)

rem Run build with cross-env
echo Building Next.js app with static generation disabled...
call npx cross-env NEXT_SKIP_STATIC_GENERATION=true NEXT_USE_STATIC_PROPS=false NEXT_PUBLIC_SKIP_CLIENT_CACHE=true next build

if %ERRORLEVEL% neq 0 (
  echo Build failed with error code %ERRORLEVEL%
  exit /b %ERRORLEVEL%
)

echo Build completed successfully!
echo Starting server...

rem Start the server
call next start 