# WooCommerce Guest Checkout Fix

This package contains solutions for fixing WooCommerce guest checkout redirect issues when using a headless frontend like Next.js.

## Contents

1. **WordPress Plugins**
   - `woo-guest-checkout-fix.php` - Comprehensive solution with custom API endpoints
   - `disable-login-redirect.php` - Quick fix focused on the login redirect issue

2. **Documentation**
   - `fix-woocommerce-checkout-redirect.md` - Detailed guide on implementing the fix

3. **Testing Tools**
   - `test-guest-checkout.js` - Script to test guest checkout functionality

## Quick Start

1. Choose one of the plugin files:
   - For a quick fix, use `disable-login-redirect.php`
   - For a complete solution, use `woo-guest-checkout-fix.php`

2. Upload your chosen plugin to your WordPress site's `/wp-content/plugins/` directory

3. Activate the plugin from the WordPress admin Plugins page

4. Update your Next.js frontend code to:
   - Use `credentials: 'include'` in all WooCommerce API fetch requests
   - Add guest checkout parameters to checkout URLs
   - Handle cart synchronization properly

5. Test your implementation using the provided test script:
   ```
   node test-guest-checkout.js
   ```

## Troubleshooting

If you're experiencing issues after implementing the fix:

1. Clear all caches (WordPress, browser, CDN)
2. Test in an incognito/private browser window
3. Check browser console for CORS or other errors
4. Verify WooCommerce settings (Accounts & Privacy)

For detailed instructions, refer to the `fix-woocommerce-checkout-redirect.md` file.

## Need Help?

If you need additional assistance, please refer to the WooCommerce documentation or reach out to our support team. 